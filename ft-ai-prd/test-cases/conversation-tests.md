# 对话管理模块测试用例

## 测试模块概述
- **模块名称**: 对话历史管理
- **测试范围**: 对话创建、历史列表、搜索删除功能
- **相关接口**: /api/conversations/*

## 1. 对话创建功能测试

### TC-CONV-001: 创建新对话
- **测试目标**: 验证用户能够成功创建新对话
- **前置条件**: 用户已登录
- **测试步骤**:
  1. 点击"新建对话"按钮
  2. 系统自动生成对话ID
  3. 进入空白对话界面
- **预期结果**:
  - 对话成功创建并保存到数据库
  - 界面切换到新对话视图
  - 显示AI欢迎消息
  - 对话历史列表更新显示新对话
- **优先级**: P0 (High)

### TC-CONV-002: 对话标题自动生成
- **测试目标**: 验证对话标题能够基于首次输入自动生成
- **前置条件**: 用户已创建新对话
- **测试步骤**:
  1. 在新对话中输入需求描述："我想做一个宠物社交App"
  2. 发送消息
  3. 等待AI响应
- **预期结果**:
  - 对话标题自动生成为"宠物社交App"或类似描述
  - 左侧历史列表显示更新后的标题
  - 标题基于用户首次输入的关键词生成
- **优先级**: P1 (High)

### TC-CONV-003: 并发对话创建
- **测试目标**: 验证用户能够创建多个并发对话
- **前置条件**: 用户已登录
- **测试步骤**:
  1. 创建第一个对话："电商平台需求"
  2. 创建第二个对话："在线教育系统"
  3. 在两个对话间切换
- **预期结果**:
  - 每个对话独立存在
  - 对话内容不会混淆
  - 能够正常在对话间切换
  - 历史列表正确显示所有对话
- **优先级**: P1 (High)

## 2. 对话历史管理测试

### TC-CONV-004: 对话历史列表显示
- **测试目标**: 验证对话历史列表正确显示
- **前置条件**: 用户已有多个历史对话
- **测试步骤**:
  1. 登录系统
  2. 查看左侧对话历史列表
- **预期结果**:
  - 显示用户所有历史对话
  - 按最近更新时间倒序排列
  - 显示对话标题和最后更新时间
  - 当前对话高亮显示
- **优先级**: P0 (High)

### TC-CONV-005: 对话历史加载
- **测试目标**: 验证点击历史对话能够正确加载
- **前置条件**: 用户已有历史对话
- **测试步骤**:
  1. 点击历史列表中的某个对话
  2. 等待对话内容加载
- **预期结果**:
  - 对话内容完整加载
  - 显示所有历史消息
  - 保持消息的时间顺序
  - 区分用户和AI消息
  - 如果存在PRD，显示PRD文稿标签
- **优先级**: P0 (High)

### TC-CONV-006: 对话分页加载
- **测试目标**: 验证大量历史对话的分页加载
- **前置条件**: 用户有超过20个历史对话
- **测试步骤**:
  1. 滚动对话历史列表到底部
  2. 触发分页加载
- **预期结果**:
  - 自动加载更多历史对话
  - 加载指示器显示
  - 新对话正确追加到列表
  - 不出现重复对话
- **优先级**: P2 (Medium)

## 3. 对话操作功能测试

### TC-CONV-007: 对话重命名
- **测试目标**: 验证用户能够重命名对话标题
- **前置条件**: 用户已有历史对话
- **测试步骤**:
  1. 鼠标悬浮在对话项上
  2. 点击"重命名"图标
  3. 输入新的对话标题："修改后的标题"
  4. 确认保存
- **预期结果**:
  - 对话标题成功更新
  - 数据库记录更新
  - 列表立即显示新标题
- **优先级**: P2 (Medium)

### TC-CONV-008: 对话删除
- **测试目标**: 验证用户能够删除对话
- **前置条件**: 用户已有历史对话
- **测试步骤**:
  1. 鼠标悬浮在对话项上
  2. 点击"删除"图标
  3. 确认删除操作
- **预期结果**:
  - 显示删除确认对话框
  - 确认后对话从列表移除
  - 数据库记录被标记删除
  - 相关消息和PRD一并删除
- **优先级**: P2 (Medium)

### TC-CONV-009: 删除当前激活对话
- **测试目标**: 验证删除当前激活对话的处理
- **前置条件**: 用户正在查看某个对话
- **测试步骤**:
  1. 删除当前正在查看的对话
  2. 确认删除
- **预期结果**:
  - 对话成功删除
  - 自动切换到其他对话或显示空白状态
  - 不出现页面错误
- **优先级**: P2 (Medium)

## 4. 对话搜索功能测试

### TC-CONV-010: 对话标题搜索
- **测试目标**: 验证能够通过标题搜索对话
- **前置条件**: 用户已有多个不同标题的对话
- **测试步骤**:
  1. 在搜索框输入对话标题关键词
  2. 等待搜索结果
- **预期结果**:
  - 显示包含关键词的对话
  - 支持模糊匹配
  - 实时搜索反馈
  - 高亮匹配的关键词
- **优先级**: P2 (Medium)

### TC-CONV-011: 对话内容搜索
- **测试目标**: 验证能够通过消息内容搜索对话
- **前置条件**: 用户已有包含特定内容的对话
- **测试步骤**:
  1. 在搜索框输入消息内容关键词
  2. 等待搜索结果
- **预期结果**:
  - 显示包含该内容的对话
  - 支持消息内容全文搜索
  - 显示匹配的消息片段
- **优先级**: P3 (Low)

### TC-CONV-012: 空搜索结果处理
- **测试目标**: 验证搜索无结果时的处理
- **测试步骤**:
  1. 输入不存在的搜索关键词
  2. 执行搜索
- **预期结果**:
  - 显示"无匹配结果"提示
  - 提供清空搜索或创建新对话的建议
  - 界面友好提示
- **优先级**: P3 (Low)

## 5. 对话状态管理测试

### TC-CONV-013: 对话状态同步
- **测试目标**: 验证多个浏览器标签页间的对话状态同步
- **前置条件**: 用户在多个标签页打开系统
- **测试步骤**:
  1. 在标签页A创建新对话
  2. 在标签页B刷新页面
  3. 检查对话列表
- **预期结果**:
  - 新对话在所有标签页中可见
  - 对话状态保持一致
  - 无需手动刷新即可同步
- **优先级**: P2 (Medium)

### TC-CONV-014: 对话持久化
- **测试目标**: 验证对话数据的持久化存储
- **前置条件**: 用户已创建对话并输入内容
- **测试步骤**:
  1. 关闭浏览器
  2. 重新打开并登录
  3. 检查对话历史
- **预期结果**:
  - 所有对话数据完整保存
  - 消息内容和时间戳正确
  - 对话状态恢复正常
- **优先级**: P0 (High)

### TC-CONV-015: 对话数据隔离
- **测试目标**: 验证不同用户的对话数据隔离
- **前置条件**: 存在多个测试用户
- **测试步骤**:
  1. 用户A创建对话
  2. 用户B登录系统
  3. 检查用户B的对话列表
- **预期结果**:
  - 用户B看不到用户A的对话
  - 每个用户只能访问自己的对话
  - 数据完全隔离
- **优先级**: P0 (High)

## 6. 异常情况测试

### TC-CONV-016: 网络异常处理
- **测试目标**: 验证网络异常时的对话管理
- **测试步骤**:
  1. 模拟网络断开
  2. 尝试创建新对话
  3. 尝试加载历史对话
- **预期结果**:
  - 显示网络错误提示
  - 保留用户已输入的内容
  - 网络恢复后自动重试
- **优先级**: P2 (Medium)

### TC-CONV-017: 大量对话性能测试
- **测试目标**: 验证大量对话的性能表现
- **前置条件**: 用户有100+个历史对话
- **测试步骤**:
  1. 加载对话历史列表
  2. 切换不同对话
  3. 搜索对话
- **预期结果**:
  - 列表加载时间 < 3秒
  - 对话切换响应时间 < 1秒
  - 搜索响应时间 < 2秒
  - 界面流畅无卡顿
- **优先级**: P2 (Medium)

### TC-CONV-018: 对话数据恢复
- **测试目标**: 验证误删除对话的恢复机制
- **前置条件**: 启用对话回收站功能
- **测试步骤**:
  1. 删除一个重要对话
  2. 进入回收站
  3. 恢复被删除的对话
- **预期结果**:
  - 对话成功恢复到原位置
  - 所有消息内容完整
  - 相关PRD一并恢复
- **优先级**: P3 (Low)

## 测试数据

### 对话测试数据
```json
{
  "sample_conversations": [
    {
      "title": "电商平台需求",
      "first_message": "我想开发一个类似淘宝的电商平台",
      "expected_ai_response": "您想开发电商平台，我需要了解一些关键信息..."
    },
    {
      "title": "在线教育系统", 
      "first_message": "需要一个在线教育平台，支持视频直播",
      "expected_ai_response": "在线教育是很好的方向，请描述您的目标用户..."
    },
    {
      "title": "宠物社交App",
      "first_message": "想做一个宠物主人的社交应用",
      "expected_ai_response": "宠物社交很有趣，您希望实现哪些核心功能..."
    }
  ]
}
```

### 搜索测试数据
```json
{
  "search_keywords": [
    "电商",
    "教育", 
    "宠物",
    "社交",
    "平台",
    "系统"
  ],
  "no_result_keywords": [
    "不存在的关键词",
    "xyz123",
    "测试无结果"
  ]
}
```

## 自动化测试优先级
1. **P0 (必须自动化)**: 对话创建、历史加载、数据隔离
2. **P1 (建议自动化)**: 对话操作、状态同步
3. **P2 (可选自动化)**: 搜索功能、性能测试
4. **P3 (手动测试)**: 异常恢复、边界情况 