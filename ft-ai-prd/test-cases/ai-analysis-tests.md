# AI分析引擎测试用例

## 测试模块概述
- **模块名称**: AI需求理解引擎
- **测试范围**: 多模态输入处理、信心指数计算、澄清问答、需求分析
- **相关接口**: /api/analysis/*, /api/files/upload

## 1. 多模态输入处理测试

### TC-AI-001: 文本输入处理
- **测试目标**: 验证AI能够正确处理和分析文本输入
- **前置条件**: 用户已登录并创建对话
- **测试步骤**:
  1. 在输入框输入需求描述："我想开发一个在线购物平台，支持多商家入驻"
  2. 点击发送按钮
  3. 等待AI分析响应
- **预期结果**:
  - AI成功接收和解析文本
  - 信心指数初始计算显示（预期30-50%）
  - AI回复包含对需求的理解和进一步问题
  - 消息正确保存到对话历史
- **优先级**: P0 (High)

### TC-AI-002: 文件上传处理 (.txt)
- **测试目标**: 验证AI能够处理文本文件上传
- **前置条件**: 准备测试文件 requirement.txt
- **测试数据**: 包含需求描述的纯文本文件
- **测试步骤**:
  1. 点击文件上传按钮
  2. 选择.txt格式的需求文档
  3. 等待文件处理和AI分析
- **预期结果**:
  - 文件成功上传到云存储
  - 文本内容正确提取
  - AI分析文件内容并给出反馈
  - 信心指数相应更新
- **优先级**: P1 (High)

### TC-AI-003: 文档文件处理 (.docx/.pdf)
- **测试目标**: 验证AI能够处理Word和PDF文档
- **前置条件**: 准备不同格式的测试文档
- **测试数据**: 
  - requirement.docx (包含格式化需求文档)
  - requirement.pdf (包含需求描述的PDF)
- **测试步骤**:
  1. 分别上传.docx和.pdf文件
  2. 等待文件解析和内容提取
  3. 检查AI分析结果
- **预期结果**:
  - 不同格式文件都能正确解析
  - 文本内容准确提取（包括表格、列表）
  - AI能理解文档结构和内容
  - 格式信息正确处理
- **优先级**: P1 (High)

### TC-AI-004: 图片上传处理 (OCR)
- **测试目标**: 验证AI能够通过OCR处理图片中的文字
- **前置条件**: 准备包含文字的测试图片
- **测试数据**:
  - 手绘草图 (sketch.png)
  - 屏幕截图 (screenshot.jpg)
  - 包含文字的照片 (text_image.jpeg)
- **测试步骤**:
  1. 上传包含文字的图片
  2. 等待OCR识别处理
  3. 检查文字识别结果
- **预期结果**:
  - 图片成功上传
  - OCR准确识别图片中的文字
  - AI分析识别出的文字内容
  - 支持中英文混合识别
- **优先级**: P2 (Medium)

### TC-AI-005: 文件大小限制验证
- **测试目标**: 验证文件上传大小限制
- **测试数据**:
  - 正常大小文件 (< 10MB)
  - 超大文件 (> 10MB)
- **测试步骤**:
  1. 尝试上传不同大小的文件
  2. 检查系统响应
- **预期结果**:
  - 正常文件成功上传
  - 超大文件显示错误提示
  - 错误信息友好明确
- **优先级**: P2 (Medium)

### TC-AI-006: 不支持的文件格式处理
- **测试目标**: 验证不支持文件格式的处理
- **测试数据**:
  - .exe, .zip, .mp4, .mp3等不支持格式
- **测试步骤**:
  1. 尝试上传不支持的文件格式
  2. 检查系统响应
- **预期结果**:
  - 显示文件格式不支持提示
  - 不允许上传
  - 提示支持的文件格式列表
- **优先级**: P2 (Medium)

## 2. 信心指数计算测试

### TC-AI-007: 信心指数初始计算
- **测试目标**: 验证首次输入后信心指数的计算
- **测试数据**:
  - 简单需求: "做一个App"
  - 详细需求: "开发B2C电商平台，包含商品管理、订单处理、支付集成、用户管理等功能"
- **测试步骤**:
  1. 分别输入不同详细程度的需求
  2. 观察信心指数变化
- **预期结果**:
  - 简单需求: 信心指数 < 30%
  - 详细需求: 信心指数 40-60%
  - 指数变化合理且显示正确
- **优先级**: P0 (High)

### TC-AI-008: 信心指数递增验证
- **测试目标**: 验证随着信息增加，信心指数递增
- **测试步骤**:
  1. 输入初始需求
  2. 回答AI的澄清问题
  3. 继续补充信息
  4. 观察信心指数变化
- **预期结果**:
  - 信心指数呈上升趋势
  - 每次有效信息补充都会提升指数
  - 达到80%时触发PRD生成提示
- **优先级**: P0 (High)

### TC-AI-009: 信心指数颜色变化
- **测试目标**: 验证信心指数进度条颜色随数值变化
- **测试步骤**:
  1. 观察不同信心指数阶段的颜色
  2. 验证颜色变化的阈值
- **预期结果**:
  - 0-40%: 红色
  - 40-80%: 黄色  
  - 80-100%: 绿色
  - 颜色变化平滑自然
- **优先级**: P2 (Medium)

### TC-AI-010: 信心指数Tooltip提示
- **测试目标**: 验证鼠标悬浮在信心指数上的提示信息
- **测试步骤**:
  1. 鼠标悬浮在信心指数进度条上
  2. 检查Tooltip内容
- **预期结果**:
  - 显示当前信心指数含义
  - 提供改进建议
  - 信息准确且有指导性
- **优先级**: P3 (Low)

## 3. AI澄清问答测试

### TC-AI-011: 澄清问题生成
- **测试目标**: 验证AI能够生成针对性的澄清问题
- **前置条件**: 输入模糊或不完整的需求
- **测试数据**: "我想做一个系统"（故意模糊）
- **测试步骤**:
  1. 输入模糊需求
  2. 等待AI分析并生成问题
- **预期结果**:
  - AI生成具体的澄清问题
  - 问题针对性强，有助于收集关键信息
  - 问题格式清晰（如选择题、填空题）
- **优先级**: P1 (High)

### TC-AI-012: 选择题形式问答
- **测试目标**: 验证AI生成选择题形式的澄清问题
- **测试步骤**:
  1. 输入需要澄清的需求
  2. 查看AI生成的选择题
  3. 选择答案并提交
- **预期结果**:
  - 生成合理的选择项
  - 选项覆盖主要情况
  - 能够处理用户的选择答案
- **优先级**: P1 (High)

### TC-AI-013: 开放性问题处理
- **测试目标**: 验证AI处理开放性问题的回答
- **测试步骤**:
  1. AI提出开放性问题
  2. 用户给出文字回答
  3. AI分析并给出后续问题
- **预期结果**:
  - AI正确理解开放性回答
  - 基于回答内容生成后续问题
  - 问题逻辑连贯合理
- **优先级**: P1 (High)

### TC-AI-014: 问答轮次限制
- **测试目标**: 验证AI澄清问答的轮次控制
- **测试步骤**:
  1. 持续与AI进行问答交互
  2. 观察是否有问答轮次限制
- **预期结果**:
  - 避免无限循环提问
  - 在合理轮次后推进到下一阶段
  - 即使信心指数未达标也能生成基础PRD
- **优先级**: P2 (Medium)

## 4. 需求分析能力测试

### TC-AI-015: 关键信息提取
- **测试目标**: 验证AI能够提取需求中的关键信息
- **测试数据**: 
  ```
  "我们公司想开发一个B2B电商平台，主要面向制造业客户，
   需要支持批量采购、询价报价、供应商管理等功能，
   预计用户规模1000+企业客户"
  ```
- **测试步骤**:
  1. 输入包含多个关键信息的需求
  2. 检查AI提取的信息
- **预期结果**:
  - 正确识别产品类型: B2B电商平台
  - 正确识别目标用户: 制造业客户
  - 正确识别核心功能: 批量采购、询价报价等
  - 正确识别规模: 1000+企业客户
- **优先级**: P0 (High)

### TC-AI-016: 业务场景理解
- **测试目标**: 验证AI对不同业务场景的理解能力
- **测试数据**:
  - 电商平台场景
  - 在线教育场景  
  - 企业管理系统场景
  - 社交应用场景
- **测试步骤**:
  1. 分别输入不同业务场景的需求
  2. 检查AI的理解和问题生成
- **预期结果**:
  - AI能识别不同业务场景
  - 针对性提问符合行业特点
  - 展现对业务流程的理解
- **优先级**: P1 (High)

### TC-AI-017: 技术方案推荐
- **测试目标**: 验证AI能够基于需求推荐合适的技术方案
- **测试步骤**:
  1. 输入包含技术要求的需求
  2. 查看AI的技术方案建议
- **预期结果**:
  - 推荐的技术栈合理
  - 考虑需求的技术特点
  - 提供多种技术选择
- **优先级**: P2 (Medium)

### TC-AI-018: 矛盾需求处理
- **测试目标**: 验证AI处理相互矛盾需求的能力
- **测试数据**: "要求系统功能强大但是开发成本极低，要求高安全性但是快速上线"
- **测试步骤**:
  1. 输入包含矛盾的需求
  2. 观察AI如何处理
- **预期结果**:
  - AI能识别需求矛盾
  - 提出平衡方案或优先级建议
  - 引导用户澄清优先级
- **优先级**: P2 (Medium)

## 5. 性能和异常测试

### TC-AI-019: AI分析响应时间
- **测试目标**: 验证AI分析的响应时间
- **测试步骤**:
  1. 输入不同长度的需求文本
  2. 测量AI响应时间
- **预期结果**:
  - 简单需求响应时间 < 3秒
  - 复杂需求响应时间 < 10秒
  - 文件处理时间 < 15秒
- **优先级**: P2 (Medium)

### TC-AI-020: 大文件处理性能
- **测试目标**: 验证大文件的处理性能
- **测试数据**: 接近10MB的大文档
- **测试步骤**:
  1. 上传大文档
  2. 测量处理时间
- **预期结果**:
  - 处理时间 < 30秒
  - 显示处理进度
  - 不出现超时错误
- **优先级**: P2 (Medium)

### TC-AI-021: AI服务异常处理
- **测试目标**: 验证AI服务不可用时的处理
- **测试步骤**:
  1. 模拟AI服务异常
  2. 尝试发送消息
- **预期结果**:
  - 显示服务暂时不可用提示
  - 保留用户输入内容
  - 提供重试机制
- **优先级**: P1 (High)

### TC-AI-022: 网络中断恢复
- **测试目标**: 验证网络中断后的恢复处理
- **测试步骤**:
  1. 发送消息过程中断网
  2. 恢复网络连接
  3. 检查消息处理状态
- **预期结果**:
  - 自动检测网络恢复
  - 重新发送未完成的请求
  - 状态正确同步
- **优先级**: P2 (Medium)

## 6. 边界条件测试

### TC-AI-023: 空输入处理
- **测试目标**: 验证空输入的处理
- **测试步骤**:
  1. 不输入任何内容点击发送
  2. 只输入空格字符
- **预期结果**:
  - 显示输入不能为空提示
  - 不发送请求到AI服务
- **优先级**: P2 (Medium)

### TC-AI-024: 超长文本输入
- **测试目标**: 验证超长文本的处理
- **测试数据**: 超过10000字符的文本
- **测试步骤**:
  1. 输入超长文本
  2. 点击发送
- **预期结果**:
  - 显示文本长度限制提示
  - 或者截取前N个字符处理
- **优先级**: P2 (Medium)

### TC-AI-025: 特殊字符处理
- **测试目标**: 验证特殊字符的正确处理
- **测试数据**: 包含emoji、特殊符号、不同语言的文本
- **测试步骤**:
  1. 输入包含特殊字符的文本
  2. 检查AI处理结果
- **预期结果**:
  - 特殊字符正确显示
  - AI能正常分析内容
  - 不出现乱码或错误
- **优先级**: P3 (Low)

## 测试数据

### 需求测试数据
```json
{
  "requirement_samples": {
    "simple": "做一个购物App",
    "detailed": "开发一个B2C电商移动应用，包含商品浏览、购物车、订单管理、支付集成、用户评价等功能，支持iOS和Android平台",
    "complex": "构建企业级CRM系统，整合销售管理、客户服务、营销自动化、数据分析等模块，支持多租户架构，预计服务1000+企业用户",
    "vague": "我想做一个系统",
    "contradictory": "要求功能非常强大但开发成本极低，需要高安全性但要快速上线"
  }
}
```

### 文件测试数据
```json
{
  "test_files": {
    "text_files": ["requirement.txt", "spec.md"],
    "doc_files": ["PRD.docx", "需求文档.pdf"],
    "image_files": ["wireframe.png", "sketch.jpg", "screenshot.jpeg"],
    "invalid_files": ["test.exe", "archive.zip", "video.mp4"]
  }
}
```

## 自动化测试优先级
1. **P0 (必须自动化)**: 基本输入处理、信心指数计算
2. **P1 (建议自动化)**: 文件上传、澄清问答、异常处理
3. **P2 (可选自动化)**: 性能测试、边界条件
4. **P3 (手动测试)**: 复杂场景、用户体验验证 