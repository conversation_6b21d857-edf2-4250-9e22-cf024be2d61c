# 用户账户模块测试用例

## 测试模块概述
- **模块名称**: 用户账户模块
- **测试范围**: 用户注册、登录、用户设置功能
- **依赖系统**: 权限中心组件

## 1. 用户注册功能测试

### TC-AUTH-001: 有效用户注册
- **测试目标**: 验证用户能够成功注册新账户
- **前置条件**: 系统正常运行，用户未注册
- **测试步骤**:
  1. 访问注册页面
  2. 输入有效的用户名 (<EMAIL>)
  3. 输入有效的密码 (Test123456)
  4. 确认密码输入
  5. 输入显示名称 (测试用户)
  6. 点击注册按钮
- **预期结果**:
  - 注册成功提示信息
  - 自动跳转到登录页面或主界面
  - 用户信息保存到数据库
- **优先级**: P0 (High)

### TC-AUTH-002: 邮箱格式验证
- **测试目标**: 验证注册时邮箱格式校验
- **测试数据**:
  - 无效邮箱: test.user, @example.com, test@, test@.com
- **测试步骤**:
  1. 输入无效邮箱格式
  2. 输入其他有效信息
  3. 点击注册按钮
- **预期结果**:
  - 显示邮箱格式错误提示
  - 注册失败，不允许提交
- **优先级**: P1 (High)

### TC-AUTH-003: 密码强度验证
- **测试目标**: 验证密码强度要求
- **测试数据**:
  - 弱密码: 123, abc, test
  - 无特殊字符: Test1234
  - 长度不足: Te1!
- **测试步骤**:
  1. 输入弱密码
  2. 输入其他有效信息
  3. 点击注册按钮
- **预期结果**:
  - 显示密码强度提示
  - 注册失败
- **优先级**: P1 (High)

### TC-AUTH-004: 重复用户名注册
- **测试目标**: 验证用户名唯一性检查
- **前置条件**: 已存在用户名 <EMAIL>
- **测试步骤**:
  1. 使用已存在的用户名注册
  2. 输入其他有效信息
  3. 点击注册按钮
- **预期结果**:
  - 显示用户名已存在提示
  - 注册失败
- **优先级**: P1 (High)

## 2. 用户登录功能测试

### TC-AUTH-005: 有效用户登录
- **测试目标**: 验证用户能够成功登录
- **前置条件**: 用户已注册
- **测试步骤**:
  1. 访问登录页面
  2. 输入有效用户名和密码
  3. 点击登录按钮
- **预期结果**:
  - 登录成功
  - 获得有效的JWT Token
  - 跳转到主界面
  - 显示用户信息
- **优先级**: P0 (High)

### TC-AUTH-006: 无效凭据登录
- **测试目标**: 验证无效凭据登录处理
- **测试数据**:
  - 错误密码: wrongpassword
  - 不存在的用户名: <EMAIL>
- **测试步骤**:
  1. 输入无效凭据
  2. 点击登录按钮
- **预期结果**:
  - 显示登录失败提示
  - 不生成Token
  - 停留在登录页面
- **优先级**: P1 (High)

### TC-AUTH-007: 空字段登录验证
- **测试目标**: 验证空字段验证
- **测试步骤**:
  1. 用户名为空，密码有效
  2. 用户名有效，密码为空
  3. 用户名和密码都为空
  4. 分别点击登录按钮
- **预期结果**:
  - 显示相应的字段必填提示
  - 登录失败
- **优先级**: P2 (Medium)

### TC-AUTH-008: Token过期处理
- **测试目标**: 验证Token过期后的处理
- **前置条件**: 用户已登录
- **测试步骤**:
  1. 等待Token过期或手动修改Token
  2. 执行需要认证的操作
- **预期结果**:
  - 显示登录过期提示
  - 自动跳转到登录页面
  - 清除本地存储的Token
- **优先级**: P1 (High)

## 3. 用户设置功能测试

### TC-AUTH-009: 用户信息修改
- **测试目标**: 验证用户能够修改个人信息
- **前置条件**: 用户已登录
- **测试步骤**:
  1. 进入用户设置页面
  2. 修改显示名称
  3. 修改其他可编辑信息
  4. 保存修改
- **预期结果**:
  - 修改成功提示
  - 信息更新到数据库
  - 界面显示更新后的信息
- **优先级**: P2 (Medium)

### TC-AUTH-010: 密码修改
- **测试目标**: 验证用户能够修改密码
- **前置条件**: 用户已登录
- **测试步骤**:
  1. 进入密码修改页面
  2. 输入当前密码
  3. 输入新密码
  4. 确认新密码
  5. 提交修改
- **预期结果**:
  - 密码修改成功
  - 使用新密码能够登录
  - 旧密码失效
- **优先级**: P1 (High)

### TC-AUTH-011: 账户注销
- **测试目标**: 验证用户能够安全注销
- **前置条件**: 用户已登录
- **测试步骤**:
  1. 点击注销按钮
  2. 确认注销操作
- **预期结果**:
  - 清除本地Token
  - 跳转到登录页面
  - 后续请求需要重新认证
- **优先级**: P1 (High)

## 4. 权限验证测试

### TC-AUTH-012: 未认证访问控制
- **测试目标**: 验证未认证用户的访问控制
- **测试步骤**:
  1. 在未登录状态下访问受保护页面
  2. 尝试调用受保护的API接口
- **预期结果**:
  - 自动跳转到登录页面
  - API返回401未授权错误
- **优先级**: P0 (High)

### TC-AUTH-013: Session管理
- **测试目标**: 验证用户会话管理
- **测试步骤**:
  1. 用户登录后长时间无操作
  2. 多个浏览器标签页同时登录
  3. 不同设备同时登录
- **预期结果**:
  - 超时后自动注销
  - 多标签页状态同步
  - 设备间登录状态独立
- **优先级**: P2 (Medium)

## 5. 安全性测试

### TC-AUTH-014: SQL注入防护
- **测试目标**: 验证登录接口的SQL注入防护
- **测试数据**:
  - `admin' OR '1'='1' --`
  - `'; DROP TABLE users; --`
- **测试步骤**:
  1. 在用户名/密码字段输入SQL注入代码
  2. 提交登录请求
- **预期结果**:
  - 登录失败
  - 数据库不受影响
  - 记录安全日志
- **优先级**: P0 (High)

### TC-AUTH-015: XSS防护
- **测试目标**: 验证用户输入的XSS防护
- **测试数据**:
  - `<script>alert('XSS')</script>`
  - `javascript:alert('XSS')`
- **测试步骤**:
  1. 在用户名等字段输入XSS代码
  2. 提交表单
- **预期结果**:
  - 输入被转义或过滤
  - 脚本不执行
  - 页面正常显示
- **优先级**: P0 (High)

## 测试数据

### 有效测试用户
```json
{
  "valid_user": {
    "username": "<EMAIL>",
    "password": "Test123456!",
    "display_name": "测试用户"
  },
  "admin_user": {
    "username": "<EMAIL>", 
    "password": "Admin123456!",
    "display_name": "管理员"
  }
}
```

### 无效测试数据
```json
{
  "invalid_emails": [
    "invalid-email",
    "@example.com",
    "test@",
    "test@.com",
    ""
  ],
  "weak_passwords": [
    "123",
    "password",
    "test",
    "",
    "12345678"
  ]
}
```

## 自动化测试优先级
1. **P0 (必须自动化)**: 基本登录注册流程
2. **P1 (建议自动化)**: 输入验证、权限控制
3. **P2 (可选自动化)**: 用户设置、会话管理 