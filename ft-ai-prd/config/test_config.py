"""
AI PRD助手项目测试配置文件
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class TestConfig:
    # 基础配置
    BASE_URL = os.getenv('TEST_BASE_URL', 'http://localhost:8066')
    API_BASE_URL = f"{BASE_URL}/ai-prd/api"
    
    # 浏览器配置
    BROWSER = os.getenv('TEST_BROWSER', 'chrome')  # chrome, firefox, edge
    HEADLESS = os.getenv('TEST_HEADLESS', 'false').lower() == 'true'
    WINDOW_SIZE = os.getenv('TEST_WINDOW_SIZE', '1920,1080')
    
    # 超时配置
    IMPLICIT_WAIT = int(os.getenv('TEST_IMPLICIT_WAIT', '10'))
    EXPLICIT_WAIT = int(os.getenv('TEST_EXPLICIT_WAIT', '30'))
    PAGE_LOAD_TIMEOUT = int(os.getenv('TEST_PAGE_LOAD_TIMEOUT', '30'))
    
    # API测试配置
    API_TIMEOUT = int(os.getenv('TEST_API_TIMEOUT', '30'))
    MAX_RETRIES = int(os.getenv('TEST_MAX_RETRIES', '3'))
    
    # 测试数据配置
    TEST_DATA_DIR = os.path.join(os.path.dirname(__file__), '..', 'data')
    TEST_FILES_DIR = os.path.join(TEST_DATA_DIR, 'test-files')
    TEST_IMAGES_DIR = os.path.join(TEST_DATA_DIR, 'test-images')
    
    # 报告配置
    REPORTS_DIR = os.path.join(os.path.dirname(__file__), '..', 'reports')
    SCREENSHOTS_DIR = os.path.join(REPORTS_DIR, 'screenshots')
    
    # 测试用户配置
    TEST_USERS = {
        'valid_user': {
            'username': os.getenv('TEST_USER_NAME', '<EMAIL>'),
            'password': os.getenv('TEST_USER_PASSWORD', 'Test123456'),
            'display_name': 'Test User'
        },
        'admin_user': {
            'username': os.getenv('TEST_ADMIN_NAME', '<EMAIL>'),
            'password': os.getenv('TEST_ADMIN_PASSWORD', 'Admin123456'),
            'display_name': 'Admin User'
        }
    }
    
    # AI分析配置
    AI_CONFIDENCE_THRESHOLD = 80
    MIN_CONFIDENCE_SCORE = 0
    MAX_CONFIDENCE_SCORE = 100
    
    # 文件上传限制
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    SUPPORTED_FILE_TYPES = ['.txt', '.md', '.docx', '.pdf', '.png', '.jpg', '.jpeg']
    
    # 性能测试配置
    LOAD_TEST_USERS = int(os.getenv('LOAD_TEST_USERS', '10'))
    LOAD_TEST_DURATION = int(os.getenv('LOAD_TEST_DURATION', '300'))  # 5分钟
    
    @classmethod
    def get_browser_options(cls):
        """获取浏览器配置选项"""
        options = {
            'headless': cls.HEADLESS,
            'window_size': tuple(map(int, cls.WINDOW_SIZE.split(','))),
            'implicit_wait': cls.IMPLICIT_WAIT,
            'page_load_timeout': cls.PAGE_LOAD_TIMEOUT
        }
        return options
    
    @classmethod
    def ensure_directories(cls):
        """确保测试目录存在"""
        directories = [
            cls.TEST_DATA_DIR,
            cls.TEST_FILES_DIR,
            cls.TEST_IMAGES_DIR,
            cls.REPORTS_DIR,
            cls.SCREENSHOTS_DIR
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)

# 环境特定配置
class DevelopmentConfig(TestConfig):
    BASE_URL = 'http://localhost:8066'
    
class StagingConfig(TestConfig):
    BASE_URL = 'https://staging-ai-prd.example.com'
    
class ProductionConfig(TestConfig):
    BASE_URL = 'https://ai-prd.example.com'

# 根据环境变量选择配置
ENV = os.getenv('TEST_ENV', 'development').lower()
if ENV == 'staging':
    config = StagingConfig()
elif ENV == 'production':
    config = ProductionConfig()
else:
    config = TestConfig()

# 确保目录存在
config.ensure_directories() 