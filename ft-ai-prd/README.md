# AI PRD助手 功能测试框架

## 概述

本测试框架专为AI PRD助手项目设计，基于PRD需求文档完成全面的功能测试用例和自动化测试脚本。

## 测试目录结构

```
ft-ai-prd/
├── README.md                    # 测试框架说明
├── test-cases/                  # 测试用例文档
│   ├── user-account-tests.md    # 用户账户模块测试用例
│   ├── conversation-tests.md    # 对话管理模块测试用例
│   ├── ai-analysis-tests.md     # AI分析引擎测试用例
│   ├── prd-generation-tests.md  # PRD生成模块测试用例
│   └── integration-tests.md     # 集成测试用例
├── api-tests/                   # API接口测试
│   ├── auth-api-tests.py        # 认证接口测试
│   ├── conversation-api-tests.py # 对话接口测试
│   ├── file-upload-api-tests.py # 文件上传接口测试
│   └── prd-api-tests.py         # PRD相关接口测试
├── ui-tests/                    # UI自动化测试
│   ├── login-ui-tests.py        # 登录页面测试
│   ├── conversation-ui-tests.py # 对话界面测试
│   ├── prd-editor-ui-tests.py   # PRD编辑器测试
│   └── page-objects/            # 页面对象模型
├── performance-tests/           # 性能测试
│   ├── load-testing.py          # 负载测试
│   └── stress-testing.py        # 压力测试
├── data/                        # 测试数据
│   ├── test-files/              # 测试文件
│   └── test-images/             # 测试图片
├── utils/                       # 测试工具类
│   ├── api_client.py            # API客户端工具
│   ├── data_generator.py        # 测试数据生成器
│   └── test_config.py           # 测试配置
└── reports/                     # 测试报告
    └── .gitkeep
```

## 测试范围

### 1. 功能测试
- 用户注册/登录功能
- 对话创建与管理
- 多模态输入处理
- AI需求分析引擎
- 信心指数计算
- PRD自动生成
- 在线编辑器功能
- 文档导出与分享

### 2. 集成测试
- 用户认证与权限验证
- 对话流程端到端测试
- 文件上传与处理集成
- AI分析与PRD生成集成

### 3. 性能测试
- 并发用户对话处理
- 文件上传性能
- AI分析响应时间
- 数据库查询性能

### 4. 安全测试
- 认证安全性
- 文件上传安全性
- 数据隔离验证
- XSS/CSRF防护

## 运行环境要求

- Python 3.8+
- pytest 框架
- selenium 用于UI测试
- requests 用于API测试
- faker 用于测试数据生成

## 快速开始

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 配置测试环境：
```bash
cp config/test_config.template.py config/test_config.py
# 修改配置文件中的测试环境信息
```

3. 运行所有测试：
```bash
pytest --html=reports/test_report.html
```

4. 运行特定模块测试：
```bash
pytest api-tests/ -v
pytest ui-tests/ -v
```

## 测试报告

测试报告将生成在 `reports/` 目录下，包含：
- HTML格式的详细测试报告
- JUnit XML格式的CI/CD集成报告
- 性能测试结果图表
- 错误截图和日志

## 注意事项

1. 运行UI测试前需要启动浏览器驱动
2. API测试需要确保后端服务正常运行
3. 文件上传测试需要准备相应的测试文件
4. 性能测试建议在专用环境中执行 