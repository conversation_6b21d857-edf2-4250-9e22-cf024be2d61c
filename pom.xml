<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.yeepay.ai</groupId>
    <artifactId>ai-prd-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>ai-prd</name>
    <description>AI PRD助手 - 后端服务</description>
    <modules>
        <module>ai-prd-main</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <mysql-connector.version>8.0.28</mysql-connector.version>
        <mybatis-plus.version>*******</mybatis-plus.version>
        <druid.version>1.2.8</druid.version>
        <uia-sdk.version>1.0.2-SNAPSHOT</uia-sdk.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <hutool.version>5.8.10</hutool.version>
        <fastjson.version>2.0.25</fastjson.version>
        <jwt.version>3.19.2</jwt.version>
        <yeepay-utils-common.version>4.2.0</yeepay-utils-common.version>
        <kotlin.version>1.8.0</kotlin.version>

        <jmh.version>1.33</jmh.version>
        <lombok.version>1.18.36</lombok.version>
        <mapstruct.version>1.6.0.Beta2</mapstruct.version>
        <mapstruct-lombok.version>0.2.0</mapstruct-lombok.version>

        <yeepay-boot.version>2.6.4-SNAPSHOT</yeepay-boot.version>
    </properties>
    <dependencyManagement>
        <dependencies>

            <!-- 权限中心SDK -->
            <dependency>
                <groupId>com.yeepay.g3</groupId>
                <artifactId>uia-sdk</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <!--云存储SDK-->
            <dependency>
                <groupId>object-storage</groupId>
                <artifactId>object-storage-sdk</artifactId>
                <version>2.0.3</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>${kotlin.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>5.3.31</version>
            </dependency>

            <!-- Yeepay boot -->
            <dependency>
                <groupId>com.yeepay.boot</groupId>
                <artifactId>yeepay-boot-dependencies</artifactId>
                <version>${yeepay-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.yeepay.g3.utils</groupId>
                <artifactId>yeepay-utils-common</artifactId>
                <version>${yeepay-utils-common.version}</version>
            </dependency>

            <!-- 数据库相关 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.4.3</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>3.5.0</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 权限中心SDK -->
            <dependency>
                <groupId>com.yeepay.g3</groupId>
                <artifactId>uia-sdk-core</artifactId>
                <version>${uia-sdk.version}</version>
            </dependency>

            <!-- JWT依赖 -->
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <failOnError>true</failOnError>
                        <verbose>true</verbose>
                        <fork>true</fork>
                        <compilerArgument>-nowarn</compilerArgument>
                        <compilerArgument>-XDignore.symbol.file</compilerArgument>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>UTF-8</encoding>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok-mapstruct-binding</artifactId>
                                <version>${mapstruct-lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.openjdk.jmh</groupId>
                                <artifactId>jmh-generator-annprocess</artifactId>
                                <version>${jmh.version}</version>
                            </path>
                            <path>
                                <groupId>com.tngtech.archunit</groupId>
                                <artifactId>archunit-junit4</artifactId>
                                <version>0.23.1</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                    <configuration>
                        <skip>false</skip>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <!-- 易宝内部仓库配置 -->
    <repositories>
        <repository>
            <id>YeepayReleases</id>
            <name>Internal Releases</name>
            <url>http://artifact.paas.yp:8000/artifactory/yp3g-subsystem-release/</url>
        </repository>
        <repository>
            <id>YeepaySnapshots</id>
            <name>Internal Snapshots</name>
            <url>http://artifact.paas.yp:8000/artifactory/yp3g-subsystem-snapshot/</url>
        </repository>
    </repositories>

</project> 