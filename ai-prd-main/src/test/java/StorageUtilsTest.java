/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

import com.yeepay.ai.main.util.storage.StorageUtils;
import com.yeepay.storage.sdk.StorageClient;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.junit.jupiter.api.Test;

import java.io.InputStream;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/24 11:14
 */
public class StorageUtilsTest {
    private static final CloseableHttpClient HTTP_CLIENT = HttpClientBuilder.create().build();

    @Test
    public void testPut() {
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("test.md");
        String pathName = "teste/test.md";
        String path = StorageUtils.upload(pathName, inputStream);
        System.out.println(path);
        StorageUtils.delete(pathName);
    }
}