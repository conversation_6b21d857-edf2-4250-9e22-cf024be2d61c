/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

import com.yeepay.g3.utils.common.encrypt.AES;
import com.yeepay.g3.utils.common.encrypt.Base64;
import org.junit.jupiter.api.Test;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/26 10:21
 */
public class ApiKeyEncryptTest {
    @Test
    public void testEncrypt(){
        String key = "I am a fool, OK?";
        String apiKey = "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";
        System.out.println(AES.encryptWithKeyBase64(apiKey, Base64.encode(key)));
    }
}