package com.yeepay.ai.main.controller;

import com.yeepay.ai.main.dto.MessageDTO;
import com.yeepay.ai.main.dto.PageResult;
import com.yeepay.ai.main.common.exection.ResponseResult;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.List;

/**
 * 对话控制器测试
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.properties")
public class ConversationControllerTest {

    @Test
    public void testPageResultCreation() {
        // 测试分页结果创建
        List<MessageDTO> messages = new java.util.ArrayList<>();
        PageResult<MessageDTO> pageResult = PageResult.of(messages, 0L, 1, 10);
        
        assert pageResult.getTotal() == 0L;
        assert pageResult.getPage() == 1;
        assert pageResult.getSize() == 10;
        assert pageResult.getPages() == 0;
        assert !pageResult.getHasNext();
        assert !pageResult.getHasPrevious();
        
        System.out.println("Page result test passed");
    }

    @Test
    public void testEmptyPageResult() {
        // 测试空分页结果创建
        PageResult<MessageDTO> emptyResult = PageResult.empty(1, 10);
        
        assert emptyResult.getTotal() == 0L;
        assert emptyResult.getRecords().isEmpty();
        assert emptyResult.getPage() == 1;
        assert emptyResult.getSize() == 10;
        
        System.out.println("Empty page result test passed");
    }
} 