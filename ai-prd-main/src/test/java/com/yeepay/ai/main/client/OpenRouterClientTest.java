package com.yeepay.ai.main.client;

import com.yeepay.ai.main.client.ai.AIConstants;
import com.yeepay.ai.main.client.ai.impl.OpenRouterClient;
import com.yeepay.ai.main.client.ai.model.ChatMessage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInstance;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OpenRouter客户端测试
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@TestInstance(TestInstance.Lifecycle.PER_METHOD)
public class OpenRouterClientTest {

    private OpenRouterClient createTestClient() {
        OpenRouterClient client = new OpenRouterClient();
        // 设置测试配置
        client.setBaseUrl(AIConstants.Defaults.OPENROUTER_BASE_URL);
        client.setModel(AIConstants.Defaults.OPENROUTER_MODEL);
        client.setMaxTokens(AIConstants.Defaults.OPENROUTER_MAX_TOKENS);
        client.setTemperature(AIConstants.Defaults.OPENROUTER_TEMPERATURE);
        client.setTopP(AIConstants.Defaults.OPENROUTER_TOP_P);
        return client;
    }

    @Test
    public void testGetProvider() {
        // 测试获取提供商名称
        OpenRouterClient client = createTestClient();
        String provider = client.getProvider();
        assertEquals(AIConstants.PROVIDERS.OPENROUTER, provider);
        client.close();
    }

    @Test
    public void testGetModel() {
        // 测试获取模型名称
        OpenRouterClient client = createTestClient();
        String model = client.getModel();
        assertEquals(AIConstants.Defaults.OPENROUTER_MODEL, model);
        client.close();
    }

    @Test
    public void testSettersAndGetters() {
        // 测试设置方法
        OpenRouterClient client = new OpenRouterClient();
        String testApiKey = "test-api-key";
        String testBaseUrl = "https://test.openrouter.ai/api/v1";
        String testModel = "test-model";
        int testMaxTokens = 1000;
        double testTemperature = 0.5;
        double testTopP = 0.8;

        client.setApiKey(testApiKey);
        client.setBaseUrl(testBaseUrl);
        client.setModel(testModel);
        client.setMaxTokens(testMaxTokens);
        client.setTemperature(testTemperature);
        client.setTopP(testTopP);

        // 验证设置是否正确
        assertEquals(testModel, client.getModel());
        assertEquals(AIConstants.PROVIDERS.OPENROUTER, client.getProvider());
        client.close();
    }

    @Test
    public void testChatMessageCreation() {
        // 测试聊天消息创建
        ChatMessage userMessage = ChatMessage.user("Hello, OpenRouter!");
        ChatMessage systemMessage = ChatMessage.system("You are a helpful assistant.");
        ChatMessage assistantMessage = ChatMessage.assistant("Hello! How can I help you?");

        assertEquals("user", userMessage.getRole());
        assertEquals("Hello, OpenRouter!", userMessage.getContent());
        
        assertEquals("system", systemMessage.getRole());
        assertEquals("You are a helpful assistant.", systemMessage.getContent());
        
        assertEquals("assistant", assistantMessage.getRole());
        assertEquals("Hello! How can I help you?", assistantMessage.getContent());
    }

    @Test
    public void testClientLifecycle() {
        // 测试客户端生命周期
        OpenRouterClient client = createTestClient();
        assertDoesNotThrow(() -> {
            client.close();
        });
    }
} 