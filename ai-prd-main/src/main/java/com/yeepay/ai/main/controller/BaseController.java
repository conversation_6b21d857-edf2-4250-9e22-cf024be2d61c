package com.yeepay.ai.main.controller;

import com.yeepay.ai.main.common.exection.UnauthorizedException;
import com.yeepay.ai.main.entity.UserInfo;
import com.yeepay.ai.main.util.config.ConfigUtils;
import com.yeepay.ai.main.util.config.ConfigEnum;
import com.yeepay.g3.core.yuia.yuiacommons.patron.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

/**
 * 基础控制器类
 * 提供统一的权限验证等功能
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@RestController
@Slf4j
public class BaseController {

    private static final String LOGIN_NAME = "X-Login-Name";

    /**
     * 获取当前用户ID
     *
     * @return 用户ID
     */
    protected String getCurrentUserId() {
        UserInfo userInfo = validateAndGetUser(getCurrentRequest());
        return userInfo.getUserId();
    }

    /**
     * 获取当前用户登录名
     *
     * @return 登录名
     */
    protected String getLoginName() {
        String loginName = null;
        com.yeepay.g3.core.yuia.yuiacommons.patronclient.UserPrincipal user = (com.yeepay.g3.core.yuia.yuiacommons.patronclient.UserPrincipal) Optional.ofNullable(SecurityUtils.getSubject()).map(item -> item.getPrincipal()).orElse(null);
        return Optional.ofNullable(user).map(item -> item.getLoginName()).orElse(null);
    }

    /**
     * 获取当前HttpServletRequest
     */
    protected HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            throw new UnauthorizedException("Cannot get request context");
        }
        return attributes.getRequest();
    }

    /**
     * 验证并获取用户信息
     *
     * @param request HTTP请求
     * @return 用户信息
     */
    protected UserInfo validateAndGetUser(HttpServletRequest request) {
        String loginName = getLoginName();
        if (StringUtils.isBlank(loginName)) {
            //throw new UnauthorizedException("User not authenticated");
            loginName = "SYSTEM"; // 使用默认登录名
            log.warn("User not authenticated, using default login name: {}", loginName);
        }

        // 构建用户信息对象
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(loginName); // 使用登录名作为用户ID
        userInfo.setLoginName(loginName);
        userInfo.setUsername(loginName);
        userInfo.setDisplayName(loginName);

        return userInfo;
    }

    /**
     * 从请求中提取Token（保留方法，用于将来可能的Token验证）
     */
    private String extractToken(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (StringUtils.isNotBlank(authHeader) && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        return request.getParameter("token");
    }
}

/**
 * 用户主体类（需要根据实际易宝组件调整）
 */
class UserPrincipal {
    private String loginName;
    private String userId;

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
} 