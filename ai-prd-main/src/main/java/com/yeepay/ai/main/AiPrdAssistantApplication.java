package com.yeepay.ai.main;

import com.yeepay.g3.core.yuia.yuiacommons.patronclient.DefaultPatronConfiguration;
import com.yeepay.g3.utils.common.InitializeUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.PropertySource;

/**
 * AI PRD助手应用启动类
 * 
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@SpringBootApplication(exclude = {
        DataSourceAutoConfiguration.class,
        TransactionAutoConfiguration.class})
@PropertySource(value = {"classpath:runtimecfg/spring-redis-conf.properties"})
@MapperScan("com.yeepay.ai.main.dao")
public class AiPrdAssistantApplication {

    public static void main(String[] args) {
        InitializeUtils.initComponents();
        SpringApplication.run(AiPrdAssistantApplication.class, args);
    }

} 