package com.yeepay.ai.main.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yeepay.ai.main.client.ai.AIClient;
import com.yeepay.ai.main.client.ai.AIClientFactory;
import com.yeepay.ai.main.client.ai.AIConfigService;

import com.yeepay.ai.main.client.ai.model.ChatMessage;
import com.yeepay.ai.main.client.ai.model.ChatResponse;
import com.yeepay.ai.main.common.exection.BusinessException;
import com.yeepay.ai.main.common.exection.ResponseResult;
import com.yeepay.ai.main.dao.ConversationDao;
import com.yeepay.ai.main.dao.ConversationMessageDao;
import com.yeepay.ai.main.dao.PRDDocumentDao;
import com.yeepay.ai.main.dto.PRDDocumentDTO;
import com.yeepay.ai.main.dto.PRDVersionDTO;
import com.yeepay.ai.main.dto.SupplementInfoRequest;
import com.yeepay.ai.main.entity.Conversation;
import com.yeepay.ai.main.entity.ConversationMessage;
import com.yeepay.ai.main.entity.PRDDocument;
import com.yeepay.ai.main.util.storage.StorageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * PRD文档生成服务
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Slf4j
@Service
public class PRDGeneratorService {

    @Autowired
    private ConversationDao conversationDao;

    @Autowired
    private ConversationMessageDao conversationMessageDao;

    @Autowired
    private PRDDocumentDao prdDocumentDao;

    @Autowired
    private AIConfigService aiConfigService;

    @Autowired
    private PromptService promptService;

    @Autowired
    private AIClientFactory aiClientFactory;

    @Autowired
    private FileService fileService;

    private static final String PRD_TEMPLATE =
            "# 产品需求文档 (PRD)\n\n" +
                    "> **文档状态:** 草稿  \n" +
                    "> **创建日期:** %s  \n" +
                    "> **作者:** AI PRD 助手  \n" +
                    "> **版本:** %s\n\n" +
                    "## 1. 项目背景\n\n" +
                    "%s\n\n" +
                    "## 2. 产品定义\n\n" +
                    "### 2.1. 产品名称\n" +
                    "%s\n\n" +
                    "### 2.2. 目标用户\n" +
                    "%s\n\n" +
                    "### 2.3. 核心价值\n" +
                    "%s\n\n" +
                    "## 3. 功能需求\n\n" +
                    "### 3.1. 核心功能\n" +
                    "%s\n\n" +
                    "### 3.2. 辅助功能\n" +
                    "%s\n\n" +
                    "## 4. 业务流程\n\n" +
                    "%s\n\n" +
                    "## 5. 非功能性需求\n\n" +
                    "### 5.1. 性能要求\n" +
                    "%s\n\n" +
                    "### 5.2. 安全要求\n" +
                    "%s\n\n" +
                    "## 6. 技术要求\n\n" +
                    "%s\n\n" +
                    "## 7. 项目计划\n\n" +
                    "%s\n\n" +
                    "---\n" +
                    "*本文档由 AI PRD 助手自动生成，请根据实际需求进行调整完善。*\n";

    /**
     * 生成PRD文档
     */
    @Transactional
    public ResponseResult<PRDDocumentDTO> generatePRD(String conversationId, String userId) {
        try {
            log.info("Generating PRD for conversation: {} by user: {}", conversationId, userId);

            // 1. 验证对话是否存在
            Conversation conversation = conversationDao.selectById(conversationId);
            if (conversation == null) {
                return ResponseResult.error("对话不存在");
            }

            // 2. 检查是否已经存在当前版本的PRD文档
            QueryWrapper<PRDDocument> existingWrapper = new QueryWrapper<>();
            existingWrapper.eq("conversation_id", conversationId)
                          .eq("is_current_version", true);
            PRDDocument existingPRD = prdDocumentDao.selectOne(existingWrapper);
            
            // 如果已存在PRD，则创建新版本而不是报错
            boolean isRegenerating = (existingPRD != null);
            if (isRegenerating) {
                log.info("Conversation {} already has PRD, will create new version", conversationId);
                // 将现有版本设为非当前版本
                existingPRD.setIsCurrentVersion(false);
                prdDocumentDao.updateById(existingPRD);
            }

            // 3. 获取对话消息
            List<ConversationMessage> messages = conversationMessageDao.selectByConversationId(conversationId);
            if (messages.isEmpty()) {
                return ResponseResult.error("对话没有消息内容");
            }

            // 5. 生成PRD内容 - 直接使用完整对话历史
            String prdContent = generatePRDContent(conversationId);

            // 6. 保存PRD文档信息和内容到数据库
            PRDDocument prdDocument = new PRDDocument();
            prdDocument.setConversationId(conversationId);
            prdDocument.setUserId(userId);
            prdDocument.setTitle(conversation.getTitle() + " - PRD");
            prdDocument.setContent(prdContent); // 直接存储内容到数据库
            
            // 根据是否已存在PRD来设置版本号和触发方式
            if (isRegenerating) {
                prdDocument.setVersion(existingPRD.getVersion() + 1);
                prdDocument.setGenerationTrigger(PRDDocument.TRIGGER_MANUAL_EDIT); // 标记为手动重新生成
            } else {
                prdDocument.setVersion(1);
                prdDocument.setGenerationTrigger(PRDDocument.TRIGGER_INITIAL); // 初始生成
            }
            
            prdDocument.setStatus(PRDDocument.STATUS_DRAFT);
            prdDocument.setIsCurrentVersion(true); // 设为当前版本
            prdDocument.setCreateTime(LocalDateTime.now());
            prdDocument.setUpdateTime(LocalDateTime.now());
            prdDocument.setCreateUser(userId);
            prdDocument.setUpdateUser(userId);

            prdDocumentDao.insert(prdDocument);

            if (isRegenerating) {
                log.info("PRD regenerated successfully for conversation: {}, new version: {}", 
                        conversationId, prdDocument.getVersion());
            } else {
                log.info("PRD generated successfully for conversation: {}", conversationId);
            }
            
            return ResponseResult.success(convertToDTO(prdDocument));

        } catch (Exception e) {
            log.error("Failed to generate PRD for conversation: {}", conversationId, e);
            return ResponseResult.error("PRD生成失败: " + e.getMessage());
        }
    }

    /**
     * 更新PRD内容
     */
    @Transactional
    public ResponseResult<PRDDocumentDTO> updatePRDContent(String prdId, String userId, String content) {
        try {
            log.info("Updating PRD content: {} by user: {}", prdId, userId);

            // 1. 获取PRD文档
            PRDDocument prdDocument = prdDocumentDao.selectById(prdId);
            if (prdDocument == null) {
                return ResponseResult.error("PRD文档不存在");
            }

            // 2. 权限验证
            if (!userId.equals(prdDocument.getUserId())) {
                // TODO: 临时允许所有人修改，后续去掉
                // return ResponseResult.error("无权限修改此文档");
            }

            // 3. 更新内容和版本
            Integer currentVersion = prdDocument.getVersion();
            Integer newVersion = currentVersion + 1;

            prdDocument.setContent(content); // 直接更新数据库中的内容
            prdDocument.setVersion(newVersion);
            prdDocument.setUpdateTime(LocalDateTime.now());
            prdDocument.setUpdateUser(userId);

            prdDocumentDao.updateById(prdDocument);

            log.info("PRD content updated successfully: {}", prdId);
            return ResponseResult.success(convertToDTO(prdDocument));

        } catch (Exception e) {
            log.error("Failed to update PRD content: {}", prdId, e);
            return ResponseResult.error("PRD更新失败: " + e.getMessage());
        }
    }

    /**
     * 更新PRD标题
     */
    @Transactional
    public ResponseResult<PRDDocumentDTO> updatePRDTitle(String prdId, String userId, String title) {
        try {
            log.info("Updating PRD title: {} by user: {}", prdId, userId);

            // 1. 获取PRD文档
            PRDDocument prdDocument = prdDocumentDao.selectById(prdId);
            if (prdDocument == null) {
                return ResponseResult.error("PRD文档不存在");
            }

            // 2. 权限验证
            if (!userId.equals(prdDocument.getUserId())) {
                return ResponseResult.error("无权限修改此文档");
            }

            // 3. 更新标题
            prdDocument.setTitle(title);
            prdDocument.setUpdateTime(LocalDateTime.now());
            prdDocument.setUpdateUser(userId);

            prdDocumentDao.updateById(prdDocument);

            log.info("PRD title updated successfully: {}", prdId);
            return ResponseResult.success(convertToDTO(prdDocument));

        } catch (Exception e) {
            log.error("Failed to update PRD title: {}", prdId, e);
            return ResponseResult.error("PRD标题更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取PRD文档
     *
     * @param conversationId 对话ID
     * @param userId         用户ID
     * @return PRD文档DTO
     */
    public ResponseResult<PRDDocumentDTO> getPRDDocument(String conversationId, String userId) {
        try {
            log.debug("Getting current PRD document for conversation: {}, userId: {}", conversationId, userId);

            // 查询该会话的当前版本PRD文档
            QueryWrapper<PRDDocument> checkWrapper = new QueryWrapper<>();
            checkWrapper.eq("conversation_id", conversationId)
                       .eq("is_current_version", true);

            PRDDocument existingPrd = prdDocumentDao.selectOne(checkWrapper);

            if (existingPrd != null) {
                log.warn("PRD exists for conversation: {}, but user_id mismatch. Expected: {}, Found: {}",
                        conversationId, userId, existingPrd.getUserId());

                // 如果当前用户是SYSTEM，或者是管理员，允许访问任何PRD
                if ("SYSTEM".equals(userId) || userId.equals(existingPrd.getUserId())) {
                    PRDDocumentDTO dto = convertToDTO(existingPrd);
                    return ResponseResult.success(dto);
                } else {
                    log.warn("User {} attempted to access PRD for conversation {} owned by user {}",
                            userId, conversationId, existingPrd.getUserId());
                    return ResponseResult.error("PRD文档不存在或无权限访问");
                }
            } else {
                log.debug("No PRD document found for conversation: {}", conversationId);
                return ResponseResult.error("PRD文档不存在");
            }

        } catch (Exception e) {
            log.error("Failed to get PRD document for conversation: {}", conversationId, e);
            return ResponseResult.error("获取PRD文档失败: " + e.getMessage());
        }
    }

    /**
     * 设置PRD文档分享状态
     *
     * @param prdId     PRD文档ID
     * @param userId    用户ID
     * @param shareable 是否可分享
     * @return 更新后的PRD文档DTO
     */
    @Transactional
    public ResponseResult<PRDDocumentDTO> setPRDShareStatus(String prdId, String userId, boolean shareable) {
        try {
            log.info("Setting PRD share status for id: {}, shareable: {}", prdId, shareable);

            PRDDocument prdDocument = prdDocumentDao.selectById(prdId);
            if (prdDocument == null || !userId.equals(prdDocument.getUserId())) {
                throw new BusinessException("PRD文档不存在或无权限访问");
            }

            // 注意：按照技术方案设计，分享功能通过share_token和share_expire_time实现
            if (shareable) {
                if (prdDocument.getShareToken() == null) {
                    // 生成分享Token
                    prdDocument.setShareToken(UUID.randomUUID().toString().replace("-", ""));
                }
                // 设置分享过期时间（30天后）
                prdDocument.setShareExpireTime(LocalDateTime.now().plusDays(30));
            } else {
                // 取消分享，清除token和过期时间
                prdDocument.setShareToken(null);
                prdDocument.setShareExpireTime(null);
            }
            prdDocument.setUpdateTime(LocalDateTime.now());

            prdDocumentDao.updateById(prdDocument);

            PRDDocumentDTO dto = convertToDTO(prdDocument);
            log.info("PRD share status updated successfully: {}", prdId);

            return ResponseResult.success(dto);

        } catch (BusinessException e) {
            log.warn("Business error in setPRDShareStatus: {}", e.getMessage());
            return ResponseResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to set PRD share status: {}", prdId, e);
            return ResponseResult.error("设置分享状态失败: " + e.getMessage());
        }
    }

    /**
     * 通过分享Token获取PRD文档
     *
     * @param shareToken 分享Token
     * @return PRD文档DTO
     */
    public ResponseResult<PRDDocumentDTO> getPRDByShareToken(String shareToken) {
        try {
            log.debug("Getting PRD document by share token: {}", shareToken);

            QueryWrapper<PRDDocument> wrapper = new QueryWrapper<>();
            wrapper.eq("share_token", shareToken);

            PRDDocument prdDocument = prdDocumentDao.selectOne(wrapper);
            if (prdDocument == null) {
                return ResponseResult.error("分享链接无效或已失效");
            }

            // 检查分享是否过期
            if (prdDocument.getShareExpireTime() != null &&
                    prdDocument.getShareExpireTime().isBefore(LocalDateTime.now())) {
                return ResponseResult.error("分享链接已过期");
            }

            PRDDocumentDTO dto = convertToDTO(prdDocument);
            return ResponseResult.success(dto);

        } catch (Exception e) {
            log.error("Failed to get PRD document by share token: {}", shareToken, e);
            return ResponseResult.error("获取分享文档失败: " + e.getMessage());
        }
    }

    /**
     * 生成PRD内容
     */
    private String generatePRDContent(String conversationId) {
        try {
            // 获取对话消息历史
            List<ConversationMessage> conversationMessages = conversationMessageDao.selectByConversationId(conversationId);

            // 获取AI客户端
            AIClient aiClient = aiClientFactory.createClient();

            // 获取系统提示词
            String systemPrompt = promptService.getPRDGenerationSystemPrompt();

            // 构建正确的messages数组：系统提示词 + 对话历史 + PRD生成请求
            List<ChatMessage> chatMessages = buildChatMessagesForPRDGeneration(systemPrompt, conversationMessages);

            ChatResponse response = aiClient.chat(chatMessages);
            String prdContent = extractResponseContent(response);

            // 清理AI生成的内容，去除可能导致乱码的字符
            prdContent = cleanAIContent(prdContent);

            log.debug("AI生成的PRD内容长度: {}", prdContent.length());

            // 关闭客户端
            aiClient.close();

            return prdContent;

        } catch (Exception e) {
            log.error("AI生成PRD失败，回退到基础模板", e);
            // 回退到基础模板生成
            throw new RuntimeException(e);
        }
    }

    /**
     * 构建PRD生成的ChatMessage数组
     * 格式：[系统提示词, 历史对话消息..., PRD生成请求]
     */
    private List<ChatMessage> buildChatMessagesForPRDGeneration(String systemPrompt, List<ConversationMessage> conversationMessages) {
        List<ChatMessage> chatMessages = new ArrayList<>();
        
        // 1. 添加系统提示词
        chatMessages.add(ChatMessage.system(systemPrompt));
        
        // 2. 添加对话历史消息
        for (ConversationMessage message : conversationMessages) {
            if (Integer.valueOf(ConversationMessage.SENDER_TYPE_USER).equals(message.getSenderType())) {
                chatMessages.add(ChatMessage.user(message.getContent()));
            } else {
                chatMessages.add(ChatMessage.assistant(message.getContent()));
            }
        }
        
        // 3. 添加PRD生成请求
        chatMessages.add(ChatMessage.user("请基于我们的对话为我生成详细的PRD文档"));
        
        return chatMessages;
    }

    /**
     * 清理AI生成的内容，去除可能导致乱码的字符
     */
    private String cleanAIContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "";
        }

        return content
                // 去除控制字符（保留换行符、制表符和回车符）
                .replaceAll("[\\p{Cntrl}&&[^\r\n\t]]", "")
                // 去除零宽字符
                .replaceAll("[\u200B-\u200D\uFEFF]", "")
                // 标准化换行符
                .replaceAll("\r\n|\r", "\n")
                // 去除多余的空行（超过2个连续换行符）
                .replaceAll("\n{3,}", "\n\n")
                // 去除首尾空白
                .trim();
    }

    /**
     * 提取AI响应内容
     */
    private String extractResponseContent(ChatResponse response) {
        if (response.getChoices() != null && !response.getChoices().isEmpty()) {
            ChatResponse.Choice choice = response.getChoices().get(0);
            if (choice.getMessage() != null && choice.getMessage().getContent() != null) {
                return choice.getMessage().getContent();
            }
        }
        throw new RuntimeException("AI响应格式异常");
    }

    /**
     * 基础PRD模板生成（作为AI生成的回退方案）
     * 注意：之前的硬编码extract方法（extractProductName、extractBackground等）
     * 已被AI智能分析替代，现在直接让AI基于完整对话历史进行分析
     */
    private String generateBasicPRDTemplate(RequirementInfo info) {
        return String.format(PRD_TEMPLATE,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                "1.0",
                info.getBackground(),
                info.getProductName(),
                info.getTargetUsers(),
                info.getCoreValue(),
                info.getCoreFunctions(),
                info.getSupportFunctions(),
                info.getBusinessFlow(),
                info.getPerformanceRequirements(),
                info.getSecurityRequirements(),
                info.getTechnicalRequirements(),
                info.getProjectPlan()
        );
    }

    /**
     * 转换为DTO
     */
    private PRDDocumentDTO convertToDTO(PRDDocument prdDocument) {
        return convertToDTO(prdDocument, null);
    }

    /**
     * 转换为DTO，支持直接传入content（用于流式生成场景）
     */
    private PRDDocumentDTO convertToDTO(PRDDocument prdDocument, String content) {
        PRDDocumentDTO.PRDDocumentDTOBuilder builder = PRDDocumentDTO.builder()
                .id(prdDocument.getId())
                .conversationId(prdDocument.getConversationId())
                .userId(prdDocument.getUserId())
                .title(prdDocument.getTitle())
                .version(prdDocument.getVersion())
                .status(prdDocument.getStatus())
                .statusDesc(getStatusDesc(prdDocument.getStatus()))
                .shareToken(prdDocument.getShareToken())
                .shareExpireTime(prdDocument.getShareExpireTime())
                .createTime(prdDocument.getCreateTime())
                .updateTime(prdDocument.getUpdateTime());

        // 如果直接传入了content，使用传入的content；否则从数据库获取
        if (content != null) {
            builder.content(content);
        } else {
            // 从数据库中获取content
            builder.content(prdDocument.getContent() != null ? prdDocument.getContent() : "");
        }

        return builder.build();
    }

    private String getStatusDesc(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case PRDDocument.STATUS_DRAFT:
                return "草稿";
            case PRDDocument.STATUS_PUBLISHED:
                return "已发布";

            default:
                return "未知";
        }
    }


    /**
     * 需求信息结构
     */
    private static class RequirementInfo {
        private String productName;
        private String background;
        private String targetUsers;
        private String coreValue;
        private String coreFunctions;
        private String supportFunctions;
        private String businessFlow;
        private String performanceRequirements;
        private String securityRequirements;
        private String technicalRequirements;
        private String projectPlan;
        private String conversationId;

        // Getters and Setters
        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public String getBackground() {
            return background;
        }

        public void setBackground(String background) {
            this.background = background;
        }

        public String getTargetUsers() {
            return targetUsers;
        }

        public void setTargetUsers(String targetUsers) {
            this.targetUsers = targetUsers;
        }

        public String getCoreValue() {
            return coreValue;
        }

        public void setCoreValue(String coreValue) {
            this.coreValue = coreValue;
        }

        public String getCoreFunctions() {
            return coreFunctions;
        }

        public void setCoreFunctions(String coreFunctions) {
            this.coreFunctions = coreFunctions;
        }

        public String getSupportFunctions() {
            return supportFunctions;
        }

        public void setSupportFunctions(String supportFunctions) {
            this.supportFunctions = supportFunctions;
        }

        public String getBusinessFlow() {
            return businessFlow;
        }

        public void setBusinessFlow(String businessFlow) {
            this.businessFlow = businessFlow;
        }

        public String getPerformanceRequirements() {
            return performanceRequirements;
        }

        public void setPerformanceRequirements(String performanceRequirements) {
            this.performanceRequirements = performanceRequirements;
        }

        public String getSecurityRequirements() {
            return securityRequirements;
        }

        public void setSecurityRequirements(String securityRequirements) {
            this.securityRequirements = securityRequirements;
        }

        public String getTechnicalRequirements() {
            return technicalRequirements;
        }

        public void setTechnicalRequirements(String technicalRequirements) {
            this.technicalRequirements = technicalRequirements;
        }

        public String getProjectPlan() {
            return projectPlan;
        }

        public void setProjectPlan(String projectPlan) {
            this.projectPlan = projectPlan;
        }

        public String getConversationId() {
            return conversationId;
        }

        public void setConversationId(String conversationId) {
            this.conversationId = conversationId;
        }
    }

    /**
     * 流式生成PRD文档
     */
    @Transactional
    public void generatePRDStream(String conversationId, String userId,
                                  BiConsumer<Integer, String> onProgress,
                                  Consumer<String> onContent,
                                  Consumer<PRDDocumentDTO> onComplete,
                                  Consumer<Exception> onError) {
        try {
            log.info("Starting stream PRD generation for conversation: {} by user: {}", conversationId, userId);

            // 1. 验证对话是否存在
            //onProgress.accept(10, "验证对话信息...");
            Conversation conversation = conversationDao.selectById(conversationId);
            if (conversation == null) {
                onError.accept(new BusinessException("对话不存在"));
                return;
            }

            // 2. 检查是否已经存在PRD文档
            //onProgress.accept(20, "检查现有文档...");
            PRDDocument existingPRD = prdDocumentDao.selectByConversationId(conversationId);
            if (existingPRD != null) {
                onError.accept(new BusinessException("该对话已经生成过PRD文档"));
                return;
            }

            // 3. 获取对话消息
            /*onProgress.accept(30, "分析对话内容...");
            List<ConversationMessage> messages = conversationMessageDao.selectByConversationId(conversationId);
            if (messages.isEmpty()) {
                onError.accept(new BusinessException("对话没有消息内容"));
                return;
            }*/

            // 4. 分析对话内容生成需求信息
            /*onProgress.accept(40, "提取需求信息...");
            RequirementInfo requirementInfo = analyzeConversationMessages(messages);
*/
            // 5. 流式生成PRD内容
            onProgress.accept(50, "begin to generate PRD...");
            StringBuilder prdContentBuilder = new StringBuilder();

            generatePRDContentStream(conversationId, onContent, (contentChunk) -> {
                prdContentBuilder.append(contentChunk);
            });

            // 6. 等待生成完成并保存
            String finalPrdContent = prdContentBuilder.toString();
            if (finalPrdContent.isEmpty()) {
                onError.accept(new BusinessException("PRD内容生成失败"));
                return;
            }

            onProgress.accept(90, "保存PRD文档...");

            // 7. 保存PRD文档信息和内容到数据库
            PRDDocument prdDocument = new PRDDocument();
            prdDocument.setConversationId(conversationId);
            prdDocument.setUserId(userId);
            prdDocument.setTitle(conversation.getTitle() + " - PRD");
            prdDocument.setContent(finalPrdContent); // 直接存储内容到数据库
            prdDocument.setVersion(1);
            prdDocument.setStatus(PRDDocument.STATUS_DRAFT);
            prdDocument.setNonce(0L);  // 设置乐观锁初始值
            prdDocument.setCreateTime(LocalDateTime.now());
            prdDocument.setUpdateTime(LocalDateTime.now());
            prdDocument.setCreateUser(userId);
            prdDocument.setUpdateUser(userId);

            prdDocumentDao.insert(prdDocument);

            // 8. 转换为DTO并包含内容
            PRDDocumentDTO prdDocumentDTO = convertToDTO(prdDocument, finalPrdContent);

            log.info("Stream PRD generated successfully for conversation: {}", conversationId);
            onComplete.accept(prdDocumentDTO);

        } catch (Exception e) {
            log.error("Failed to generate stream PRD for conversation: {}", conversationId, e);
            onError.accept(e);
        }
    }

    /**
     * 流式生成PRD内容
     */
    private void generatePRDContentStream(String conversationId,
                                          Consumer<String> onContent,
                                          Consumer<String> onChunk) {
        try {
            // 获取对话消息历史（与非流式版本保持一致）
            List<ConversationMessage> messages = conversationMessageDao.selectByConversationId(conversationId);

            // 获取AI配置
            AIConfigService.AIConfig config = aiConfigService.getAIConfig();

            // 检查是否启用真实流式生成（可通过配置控制）
            boolean enableRealStream = isRealStreamEnabled(config);

            if (enableRealStream) {
                // 尝试使用真实AI流式生成
                try {
                    generateStreamWithAI(messages, onContent, onChunk);
                    log.info("使用真实AI流式生成PRD内容");
                    return;
                } catch (Exception aiError) {
                    log.warn("真实AI流式生成失败，降级到增强模拟流式: {}", aiError.getMessage());
                    // 降级到增强模拟流式生成
                    generateEnhancedStreamMocked(conversationId, onContent, onChunk);
                    return;
                }
            }

            // 使用标准模拟流式生成
            log.info("使用标准模拟流式生成PRD内容");
            generateStreamMocked(conversationId, onContent, onChunk);

        } catch (Exception e) {
            log.error("Failed to generate PRD content stream", e);
            // 最终降级方案
            generateStreamMocked(conversationId, onContent, onChunk);
        }
    }

    /**
     * 检查是否启用真实流式生成
     */
    private boolean isRealStreamEnabled(AIConfigService.AIConfig config) {
        // 检查AI提供商是否支持流式
        if (!"deepseek".equalsIgnoreCase(config.getProvider())) {
            log.debug("AI提供商 {} 不支持流式生成", config.getProvider());
            return false;
        }

        // 可以通过环境变量或配置文件控制
        String enableRealStream = System.getProperty("ai.prd.enable.real.stream", "true");
        boolean enabled = "true".equalsIgnoreCase(enableRealStream);

        log.debug("真实流式生成开关: {}", enabled ? "启用" : "禁用");
        return enabled;
    }

    /**
     * 增强模拟流式生成（更接近真实效果）
     */
    private void generateEnhancedStreamMocked(String conversationId,
                                              Consumer<String> onContent, Consumer<String> onChunk) {
        try {
            log.info("使用增强模拟流式生成PRD内容");

            // 获取对话消息历史
            List<ConversationMessage> conversationMessages = conversationMessageDao.selectByConversationId(conversationId);

            // 获取系统提示词
            String systemPrompt = promptService.getPRDGenerationSystemPrompt();

            // 构建正确的messages数组
            List<ChatMessage> messages = buildChatMessagesForPRDGeneration(systemPrompt, conversationMessages);

            // 使用AI生成完整内容
            AIClient aiClient = aiClientFactory.createClient();
            ChatResponse response = aiClient.chat(messages);
            String fullContent = extractResponseContent(response);
            aiClient.close();

            // 增强的流式发送效果
            sendContentWithEnhancedStream(fullContent, onContent, onChunk);

        } catch (Exception e) {
            log.error("增强模拟流式生成失败，降级到标准模拟", e);
            // 降级到标准模拟流式
            generateStreamMocked(conversationId, onContent, onChunk);
        }
    }

    /**
     * 增强的流式内容发送（模拟更真实的AI生成节奏）
     */
    private void sendContentWithEnhancedStream(String fullContent,
                                               Consumer<String> onContent,
                                               Consumer<String> onChunk) {
        // 按语义单元分割（句子、段落等）
        String[] sentences = fullContent.split("(?<=[。！？；：])|(?<=\\n\\n)");

        for (String sentence : sentences) {
            if (sentence.trim().isEmpty()) continue;

            // 再按词语分割，模拟AI逐词生成
            String[] words = sentence.split("(?<=\\s)|(?<=[，、])|(?<=\\p{P})");

            for (String word : words) {
                if (word.trim().isEmpty()) continue;

                // 发送单词
                onContent.accept(word);
                onChunk.accept(word);

                // 动态延迟（模拟AI思考时间）
                try {
                    int delay = calculateDelay(word);
                    Thread.sleep(delay);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            // 句子间稍长停顿
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    /**
     * 计算发送延迟（模拟真实AI生成节奏）
     */
    private int calculateDelay(String word) {
        // 基础延迟
        int baseDelay = 30;

        // 根据内容长度调整
        int lengthDelay = word.length() * 5;

        // 特殊内容延迟调整
        if (word.contains("##") || word.contains("###")) {
            // 标题生成稍慢
            lengthDelay += 50;
        } else if (word.contains("```") || word.contains("|")) {
            // 代码块和表格生成稍慢
            lengthDelay += 30;
        } else if (word.matches(".*[0-9]+.*")) {
            // 数字内容稍快
            lengthDelay = lengthDelay / 2;
        }

        // 随机波动（模拟AI不稳定的生成速度）
        int randomFactor = (int) (Math.random() * 20 - 10); // -10到+10的随机数

        return Math.max(10, Math.min(150, baseDelay + lengthDelay + randomFactor));
    }

    /**
     * 使用AI流式生成PRD内容（真实流式实现）
     */
    private void generateStreamWithAI(List<ConversationMessage> conversationMessages,
                                      Consumer<String> onContent,
                                      Consumer<String> onChunk) {
        log.info("开始真实AI流式生成PRD内容");

        try {
            // 获取AI配置
            AIConfigService.AIConfig config = aiConfigService.getAIConfig();

            // 检查是否支持流式生成
            if (!"deepseek".equalsIgnoreCase(config.getProvider())) {
                throw new UnsupportedOperationException("当前AI提供商不支持流式生成: " + config.getProvider());
            }

            // 使用真实的流式AI客户端
            generateWithRealStreamClient(config, conversationMessages, onContent, onChunk);

        } catch (Exception e) {
            log.error("AI流式生成失败", e);
            throw e;
        }
    }

    /**
     * 使用真实流式AI客户端生成内容
     */
    private void generateWithRealStreamClient(AIConfigService.AIConfig config, List<ConversationMessage> conversationMessages,
                                              Consumer<String> onContent, Consumer<String> onChunk) {
        try {
            // 创建流式AI客户端（模拟ai-demo中的实现）
            StreamAIClient streamClient = createStreamAIClient(config);

            // 构建正确的messages格式：系统提示词 + 对话历史 + PRD生成请求
            List<StreamChatMessage> messages = new ArrayList<>();
            
            // 1. 添加系统提示词
            messages.add(new StreamChatMessage("system", promptService.getPRDGenerationSystemPrompt()));
            
            // 2. 添加对话历史消息
            for (ConversationMessage message : conversationMessages) {
                if (Integer.valueOf(ConversationMessage.SENDER_TYPE_USER).equals(message.getSenderType())) {
                    messages.add(new StreamChatMessage("user", message.getContent()));
                } else {
                    messages.add(new StreamChatMessage("assistant", message.getContent()));
                }
            }
            
            // 3. 添加PRD生成请求
            messages.add(new StreamChatMessage("user", "请基于我们的对话为我生成详细的PRD文档"));

            // 使用CountDownLatch等待流式生成完成
            java.util.concurrent.CountDownLatch latch = new java.util.concurrent.CountDownLatch(1);
            java.util.concurrent.atomic.AtomicReference<Exception> errorRef = new java.util.concurrent.atomic.AtomicReference<>();

            // 发送流式请求
            streamClient.streamChat(messages,
                    // onMessage: 实时接收AI生成的内容
                    (content) -> {
                        log.debug("接收到AI流式内容: {}", content);
                        onContent.accept(content);
                        onChunk.accept(content);
                    },
                    // onComplete: 生成完成
                    () -> {
                        log.info("AI流式生成完成");
                        latch.countDown();
                    },
                    // onError: 错误处理
                    (error) -> {
                        log.error("AI流式生成出错", error);
                        errorRef.set(new RuntimeException("AI流式生成失败: " + error.getMessage(), error));
                        latch.countDown();
                    }
            );

            // 等待生成完成（最多等待5分钟）
            boolean completed = latch.await(5, java.util.concurrent.TimeUnit.MINUTES);
            if (!completed) {
                throw new RuntimeException("AI流式生成超时");
            }

            // 检查是否有错误
            Exception error = errorRef.get();
            if (error != null) {
                throw error;
            }

            log.info("真实AI流式生成成功完成");

        } catch (Exception e) {
            log.error("真实流式AI客户端生成失败", e);
            throw new RuntimeException("AI流式生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建流式AI客户端（适配ai-demo中的流式客户端）
     */
    private StreamAIClient createStreamAIClient(AIConfigService.AIConfig config) {
        // 模拟创建流式客户端的逻辑
        // 在实际项目中，这里会引入ai-demo的依赖并创建真正的流式客户端
        return new MockStreamAIClient(config);
    }

    /**
     * 流式AI客户端接口（模拟ai-demo中的接口）
     */
    private interface StreamAIClient {
        void streamChat(List<StreamChatMessage> messages,
                        Consumer<String> onMessage,
                        Runnable onComplete,
                        Consumer<Throwable> onError);
    }

    /**
     * 流式聊天消息（模拟ai-demo中的消息格式）
     */
    private static class StreamChatMessage {
        private String role;
        private String content;

        public StreamChatMessage(String role, String content) {
            this.role = role;
            this.content = content;
        }

        public String getRole() {
            return role;
        }

        public String getContent() {
            return content;
        }
    }

    /**
     * 模拟流式AI客户端（演示真实流式效果）
     */
    private class MockStreamAIClient implements StreamAIClient {
        private final AIConfigService.AIConfig config;

        public MockStreamAIClient(AIConfigService.AIConfig config) {
            this.config = config;
        }

        @Override
        public void streamChat(List<StreamChatMessage> messages,
                               Consumer<String> onMessage,
                               Runnable onComplete,
                               Consumer<Throwable> onError) {

            // 在新线程中执行流式生成
            new Thread(() -> {
                try {
                    log.info("模拟真实AI流式生成开始");

                    // 使用现有AI客户端生成完整内容
                    AIClient aiClient = aiClientFactory.createClient(config);

                    List<ChatMessage> chatMessages = Arrays.asList(
                            ChatMessage.system(messages.get(0).getContent()),
                            ChatMessage.user(messages.get(1).getContent())
                    );

                    ChatResponse response = aiClient.chat(chatMessages);
                    String fullContent = extractResponseContent(response);
                    aiClient.close();

                    // 模拟真实流式发送（按字符或小段发送，适合中文）
                    int chunkSize = 3; // 每次发送3个字符，避免分割中文
                    StringBuilder buffer = new StringBuilder();
                    
                    for (int i = 0; i < fullContent.length(); i++) {
                        char ch = fullContent.charAt(i);
                        buffer.append(ch);
                        
                        // 当缓冲区达到指定大小，或遇到标点符号，或是最后一个字符时发送
                        if (buffer.length() >= chunkSize || 
                            "，。！？；：、\n".indexOf(ch) >= 0 || 
                            i == fullContent.length() - 1) {
                            
                            String chunk = buffer.toString();
                            if (!chunk.trim().isEmpty()) {
                                // 确保内容以UTF-8编码发送
                                try {
                                    // 验证UTF-8编码是否正确
                                    byte[] bytes = chunk.getBytes("UTF-8");
                                    String validChunk = new String(bytes, "UTF-8");
                                    
                                    log.debug("发送流式块: {}", validChunk);
                                    onMessage.accept(validChunk);
                                } catch (Exception e) {
                                    log.warn("编码处理失败，使用原始内容: {}", e.getMessage());
                                    onMessage.accept(chunk);
                                }
                            }
                            
                            buffer.setLength(0); // 清空缓冲区
                            
                            // 模拟AI生成延迟
                            try {
                                Thread.sleep(50); // 固定50ms延迟
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                                onError.accept(new RuntimeException("流式生成被中断"));
                                return;
                            }
                        }
                    }

                    // 生成完成
                    onComplete.run();
                    log.info("模拟真实AI流式生成完成，总长度: {}", fullContent.length());

                } catch (Exception e) {
                    log.error("模拟流式生成失败", e);
                    onError.accept(e);
                }
            }, "AI-Stream-Generator").start();
        }
    }

    /**
     * 模拟流式生成PRD内容（标准降级方案）
     */
    private void generateStreamMocked(String conversationId,
                                      Consumer<String> onContent,
                                      Consumer<String> onChunk) {
        try {
            log.info("使用标准模拟流式生成PRD内容");

            // 生成完整PRD内容
            String fullContent = generatePRDContent(conversationId);

            // 模拟流式输出，按行分割发送
            String[] lines = fullContent.split("\n");

            for (int i = 0; i < lines.length; i++) {
                String line = lines[i] + "\n";

                // 发送内容片段
                onContent.accept(line);
                onChunk.accept(line);

                // 模拟生成延迟
                try {
                    Thread.sleep(50); // 50ms延迟，模拟实时生成
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

        } catch (Exception e) {
            log.error("Failed to generate mocked stream PRD content", e);
            throw new RuntimeException("PRD内容生成失败", e);
        }
    }

    /**
     * 补充信息并重新生成PRD
     */
    @Transactional
    public ResponseResult<PRDDocumentDTO> supplementAndRegeneratePRD(SupplementInfoRequest request) {
        try {
            log.info("Supplementing info and regenerating PRD: {}", request.getPrdId());

            // 1. 获取当前PRD文档
            PRDDocument currentPRD = prdDocumentDao.selectById(request.getPrdId());
            if (currentPRD == null) {
                return ResponseResult.error("PRD文档不存在");
            }

            // 2. 权限验证
            if (!request.getUserId().equals(currentPRD.getUserId())) {
                return ResponseResult.error("无权限修改此文档");
            }

            // 3. 将补充信息作为新消息添加到对话历史
            ConversationMessage supplementMessage = new ConversationMessage();
            supplementMessage.setConversationId(currentPRD.getConversationId());
            supplementMessage.setSenderType(ConversationMessage.SENDER_TYPE_USER);
            supplementMessage.setContentType(ConversationMessage.CONTENT_TYPE_TEXT);
            supplementMessage.setContent(request.getSupplementContent());
            supplementMessage.setCreateTime(LocalDateTime.now());
            conversationMessageDao.insert(supplementMessage);

            // 4. 如果需要保留原版本，将当前版本设为非当前版本
            if (request.getKeepOriginalVersion()) {
                currentPRD.setIsCurrentVersion(false);
                prdDocumentDao.updateById(currentPRD);
            }

            // 5. 基于完整对话历史（包含补充信息）重新生成PRD内容
            String newPRDContent = generatePRDContent(currentPRD.getConversationId());

            // 6. 创建新版本PRD文档或更新现有文档
            PRDDocument newPRD;
            if (request.getKeepOriginalVersion()) {
                // 创建新版本
                newPRD = new PRDDocument();
                newPRD.setConversationId(currentPRD.getConversationId());
                newPRD.setUserId(currentPRD.getUserId());
                newPRD.setTitle(currentPRD.getTitle());
                newPRD.setContent(newPRDContent);
                newPRD.setVersion(currentPRD.getVersion() + 1);
                newPRD.setStatus(PRDDocument.STATUS_DRAFT);
                newPRD.setIsCurrentVersion(true);
                newPRD.setGenerationTrigger(PRDDocument.TRIGGER_SUPPLEMENT);
                newPRD.setBaseMessageId(supplementMessage.getId());
                newPRD.setCreateTime(LocalDateTime.now());
                newPRD.setUpdateTime(LocalDateTime.now());
                newPRD.setCreateUser(request.getUserId());
                newPRD.setUpdateUser(request.getUserId());
                
                prdDocumentDao.insert(newPRD);
            } else {
                // 直接更新当前版本
                currentPRD.setContent(newPRDContent);
                currentPRD.setVersion(currentPRD.getVersion() + 1);
                currentPRD.setGenerationTrigger(PRDDocument.TRIGGER_SUPPLEMENT);
                currentPRD.setBaseMessageId(supplementMessage.getId());
                currentPRD.setUpdateTime(LocalDateTime.now());
                currentPRD.setUpdateUser(request.getUserId());
                
                prdDocumentDao.updateById(currentPRD);
                newPRD = currentPRD;
            }

            log.info("PRD regenerated successfully with supplement info: {}", newPRD.getId());
            return ResponseResult.success(convertToDTO(newPRD));

        } catch (Exception e) {
            log.error("Failed to supplement and regenerate PRD: {}", request.getPrdId(), e);
            return ResponseResult.error("补充信息重新生成PRD失败: " + e.getMessage());
        }
    }

    /**
     * 获取PRD版本历史
     */
    public ResponseResult<List<PRDVersionDTO>> getPRDVersionHistory(String conversationId, String userId) {
        try {
            log.debug("Getting PRD version history for conversation: {}", conversationId);

            // 查询该对话的所有PRD版本
            QueryWrapper<PRDDocument> wrapper = new QueryWrapper<>();
            wrapper.eq("conversation_id", conversationId)
                   .orderByDesc("version");
            
            List<PRDDocument> prdVersions = prdDocumentDao.selectList(wrapper);
            
            if (prdVersions.isEmpty()) {
                return ResponseResult.success(new ArrayList<>());
            }

            // 权限验证（检查第一个版本的用户权限）
            PRDDocument firstVersion = prdVersions.get(0);
            if (!userId.equals(firstVersion.getUserId()) && !"SYSTEM".equals(userId)) {
                return ResponseResult.error("无权限查看此对话的PRD版本历史");
            }

            // 转换为DTO
            List<PRDVersionDTO> versionDTOs = prdVersions.stream()
                    .map(this::convertToPRDVersionDTO)
                    .collect(Collectors.toList());

            return ResponseResult.success(versionDTOs);

        } catch (Exception e) {
            log.error("Failed to get PRD version history for conversation: {}", conversationId, e);
            return ResponseResult.error("获取版本历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定版本的PRD文档
     */
    public ResponseResult<PRDDocumentDTO> getPRDByVersion(String prdId, String userId) {
        try {
            log.debug("Getting PRD by version: {}", prdId);

            PRDDocument prdDocument = prdDocumentDao.selectById(prdId);
            if (prdDocument == null) {
                return ResponseResult.error("PRD文档不存在");
            }

            // 权限验证
            if (!userId.equals(prdDocument.getUserId()) && !"SYSTEM".equals(userId)) {
                return ResponseResult.error("无权限访问此PRD文档");
            }

            return ResponseResult.success(convertToDTO(prdDocument));

        } catch (Exception e) {
            log.error("Failed to get PRD by version: {}", prdId, e);
            return ResponseResult.error("获取指定版本PRD失败: " + e.getMessage());
        }
    }

    /**
     * 恢复到指定版本
     */
    @Transactional
    public ResponseResult<PRDDocumentDTO> restoreToVersion(String prdId, String userId) {
        try {
            log.info("Restoring PRD to version: {}", prdId);

            // 1. 获取目标版本PRD
            PRDDocument targetVersion = prdDocumentDao.selectById(prdId);
            if (targetVersion == null) {
                return ResponseResult.error("目标版本PRD不存在");
            }

            // 2. 权限验证
            if (!userId.equals(targetVersion.getUserId())) {
                return ResponseResult.error("无权限操作此PRD文档");
            }

            // 3. 将该对话的所有版本设为非当前版本
            QueryWrapper<PRDDocument> wrapper = new QueryWrapper<>();
            wrapper.eq("conversation_id", targetVersion.getConversationId());
            
            List<PRDDocument> allVersions = prdDocumentDao.selectList(wrapper);
            for (PRDDocument version : allVersions) {
                version.setIsCurrentVersion(false);
                prdDocumentDao.updateById(version);
            }

            // 4. 将目标版本设为当前版本
            targetVersion.setIsCurrentVersion(true);
            targetVersion.setUpdateTime(LocalDateTime.now());
            targetVersion.setUpdateUser(userId);
            prdDocumentDao.updateById(targetVersion);

            log.info("PRD restored to version successfully: {}", prdId);
            return ResponseResult.success(convertToDTO(targetVersion));

        } catch (Exception e) {
            log.error("Failed to restore PRD to version: {}", prdId, e);
            return ResponseResult.error("恢复版本失败: " + e.getMessage());
        }
    }

    /**
     * 转换为PRD版本DTO
     */
    private PRDVersionDTO convertToPRDVersionDTO(PRDDocument prdDocument) {
        PRDVersionDTO.PRDVersionDTOBuilder builder = PRDVersionDTO.builder()
                .id(prdDocument.getId())
                .conversationId(prdDocument.getConversationId())
                .title(prdDocument.getTitle())
                .version(prdDocument.getVersion())
                .isCurrentVersion(prdDocument.getIsCurrentVersion())
                .generationTrigger(prdDocument.getGenerationTrigger())
                .generationTriggerDesc(getGenerationTriggerDesc(prdDocument.getGenerationTrigger()))
                .baseMessageId(prdDocument.getBaseMessageId())
                .createTime(prdDocument.getCreateTime())
                .createUser(prdDocument.getCreateUser())
                .statusDesc(getStatusDesc(prdDocument.getStatus()));

        // 获取内容预览
        if (prdDocument.getContent() != null && prdDocument.getContent().length() > 200) {
            builder.contentPreview(prdDocument.getContent().substring(0, 200) + "...");
        } else {
            builder.contentPreview(prdDocument.getContent());
        }

        // 获取触发消息内容（如果存在）
        if (prdDocument.getBaseMessageId() != null) {
            try {
                ConversationMessage triggerMessage = conversationMessageDao.selectById(prdDocument.getBaseMessageId());
                if (triggerMessage != null) {
                    builder.triggerMessageContent(triggerMessage.getContent());
                }
            } catch (Exception e) {
                log.warn("Failed to get trigger message content for PRD: {}", prdDocument.getId(), e);
            }
        }

        return builder.build();
    }

    /**
     * 获取生成触发方式描述
     */
    private String getGenerationTriggerDesc(String generationTrigger) {
        if (generationTrigger == null) return "未知";
        switch (generationTrigger) {
            case PRDDocument.TRIGGER_INITIAL:
                return "初始生成";
            case PRDDocument.TRIGGER_SUPPLEMENT:
                return "补充信息重新生成";
            case PRDDocument.TRIGGER_MANUAL_EDIT:
                return "手动编辑";
            default:
                return "未知";
        }
    }


} 