package com.yeepay.ai.main.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 对话DTO
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Data
@Builder
public class ConversationDTO {

    /**
     * 对话ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 对话标题
     */
    private String title;

    /**
     * 状态:1-进行中,2-已完成,3-已删除
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 当前信心指数
     */
    private Integer confidenceScore;

    /**
     * 消息数量
     */
    private Integer messageCount;

    /**
     * 是否已生成PRD
     */
    private Boolean hasPRD;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 最后一条消息预览
     */
    private String lastMessagePreview;

    /**
     * 消息列表
     */
    private List<MessageDTO> messages;
} 