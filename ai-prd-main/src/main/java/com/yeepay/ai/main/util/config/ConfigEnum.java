/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ai.main.util.config;

import com.yeepay.ai.main.client.ai.AIConstants;
import com.yeepay.ai.main.common.constants.YeepayStorageConstants;
import lombok.Getter;

import java.util.HashMap;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/24 10:58
 */
@Getter
public enum ConfigEnum implements ConfigKey {

    STORAGE_CLIENT_CONFIG(new HashMap() {
        {
            put(YeepayStorageConstants.END_POINT, "qargwapi.tc.yp:30231");
            put(YeepayStorageConstants.ACCESS_HOST, "https://qastaticres.yeepay.com");
            put(YeepayStorageConstants.SECRET, "7EFAC920705D0D0A54D531CFEFCD9E6AB118E1B3A9314E13B08DD0193EA62B5B882C39727BBBA152C71723760EACD09DF132DB697E1AB0D414339353174336883D50DB4303EF201983AC7936553A2F51");
            put(YeepayStorageConstants.BUCKET_NAME, "yop_quantum_statics");
        }
    }),

    /**
     * AI相关配置 - 统一的MAP配置
     * JSON格式示例：
     * {
     * "provider": "deepseek",
     * "baseUrl": "https://api.deepseek.com/v1",
     * "model": "deepseek-chat",
     * "apiKey": "encrypted_key_here",
     * "maxTokens": 8000,
     * "temperature": 1.0,
     * "topP": 0.95
     * }
     */
    AWESOME_AI_CONFIG(new HashMap() {
        {
            /*
            // 生成的效果于gemini效果相比较差
            put(AIConstants.CONFIG_KEYS.PROVIDER, "deepseek");
            put(AIConstants.CONFIG_KEYS.BASE_URL, "https://api.deepseek.com/v1");
            put(AIConstants.CONFIG_KEYS.MODEL, "deepseek-chat");
            put(AIConstants.CONFIG_KEYS.API_KEY, "1CZakd7Nbk+kYOJUJWjeETxSl8niKZBMEgwNa2Ih5fccXP/7xeDWc6sbGnD7a1K+");
            put(AIConstants.CONFIG_KEYS.PROVIDER, "gemini");
            put(AIConstants.CONFIG_KEYS.BASE_URL, "https://generativelanguage.googleapis.com");
            put(AIConstants.CONFIG_KEYS.MODEL, "gemini-2.5-flash-preview-05-20");
            put(AIConstants.CONFIG_KEYS.API_KEY, "Y3XxhPOpaUVwba4rJTdG8Xpp5qgUXFoqAONTpAzPaS7nf6XB13UcGFAc8Hqo0P/c");
          */

            put(AIConstants.CONFIG_KEYS.PROVIDER, "openrouter");
            put(AIConstants.CONFIG_KEYS.BASE_URL, "https://openrouter.ai/api/v1");
            put(AIConstants.CONFIG_KEYS.MODEL, "google/gemini-2.5-flash");
            put(AIConstants.CONFIG_KEYS.API_KEY, "SVqD6nE3Mtscz9PI+jennYewQyWnn196Pv9cavxV3nbq0mKjvByDkShO/NrrFl1+LOzy6Imt5Y3O87s5LmdvxeDisyhbGX7BBbSbrKQLqvQ=");

            put(AIConstants.CONFIG_KEYS.MAX_TOKENS, "8000");
            put(AIConstants.CONFIG_KEYS.TEMPERATURE," 1.0");
            put(AIConstants.CONFIG_KEYS.TOP_P, "0.95");
        }
    });

    private Object defaultValue;


    ConfigEnum(Object defaultValue) {
        this.defaultValue = defaultValue;
    }

    @Override
    public String getConfigKey() {
        return this.name();
    }

    public Object getDefaultValue() {
        return defaultValue;
    }
}