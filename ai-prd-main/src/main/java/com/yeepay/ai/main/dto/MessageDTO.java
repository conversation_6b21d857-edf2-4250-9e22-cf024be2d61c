package com.yeepay.ai.main.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 消息DTO
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MessageDTO implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 消息ID
     */
    private String id;

    /**
     * 对话ID
     */
    private String conversationId;

    /**
     * 发送者类型:1-用户,2-AI
     */
    private Integer senderType;

    /**
     * 发送者类型描述
     */
    private String senderTypeDesc;

    /**
     * 内容类型:1-文本,2-文件,3-图片
     */
    private Integer contentType;

    /**
     * 内容类型描述
     */
    private String contentTypeDesc;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 文件URL数组
     */
    private List<String> fileUrls;

    /**
     * 文件元信息
     */
    private Map<String, Object> metadata;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 是否为AI分析结果
     */
    private Boolean isAnalysisResult;

    /**
     * 信心指数（仅AI消息有效）
     */
    private Integer confidenceScore;
    
    /**
     * 缺失信息列表（仅AI分析结果有效）
     */
    private List<String> missingInfo;
    
    /**
     * 澄清问题列表（仅AI分析结果有效）
     */
    private List<ClarificationQuestionDTO> clarificationQuestions;
} 