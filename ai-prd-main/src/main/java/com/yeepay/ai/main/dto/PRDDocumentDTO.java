package com.yeepay.ai.main.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * PRD文档DTO
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Data
@Builder
public class PRDDocumentDTO {

    /**
     * PRD文档ID
     */
    private String id;

    /**
     * 对话ID
     */
    private String conversationId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 文档标题
     */
    private String title;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 状态:1-草稿,2-已发布
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 分享token
     */
    private String shareToken;

    /**
     * 分享链接
     */
    private String shareUrl;

    /**
     * 分享过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime shareExpireTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * PRD内容（仅在查看详情时返回）
     */
    private String content;
} 