package com.yeepay.ai.main.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 补充信息请求DTO
 * 用于接收用户的补充信息并重新生成PRD
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplementInfoRequest {

    /**
     * PRD文档ID
     */
    @NotNull(message = "PRD文档ID不能为空")
    private String prdId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private String userId;

    /**
     * 补充信息内容
     * 用户输入的补充需求、澄清或修正内容
     */
    @NotBlank(message = "补充信息不能为空")
    private String supplementContent;

    /**
     * 是否保留原PRD版本
     * true: 创建新版本，保留原版本 (默认)
     * false: 直接覆盖原版本
     */
    @Builder.Default
    private Boolean keepOriginalVersion = true;
} 