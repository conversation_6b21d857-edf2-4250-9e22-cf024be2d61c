package com.yeepay.ai.main.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 分页响应结果DTO
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Data
@ApiModel("分页响应结果")
public class PageResult<T> {

    /**
     * 数据列表
     */
    @ApiModelProperty("数据列表")
    private List<T> records;

    /**
     * 总记录数
     */
    @ApiModelProperty("总记录数")
    private Long total;

    /**
     * 当前页码
     */
    @ApiModelProperty("当前页码")
    private Integer page;

    /**
     * 每页大小
     */
    @ApiModelProperty("每页大小")
    private Integer size;

    /**
     * 总页数
     */
    @ApiModelProperty("总页数")
    private Integer pages;

    /**
     * 是否有下一页
     */
    @ApiModelProperty("是否有下一页")
    private Boolean hasNext;

    /**
     * 是否有上一页
     */
    @ApiModelProperty("是否有上一页")
    private Boolean hasPrevious;

    public PageResult() {
    }

    public PageResult(List<T> records, Long total, Integer page, Integer size) {
        this.records = records;
        this.total = total;
        this.page = page;
        this.size = size;
        this.pages = (int) Math.ceil((double) total / size);
        this.hasNext = page < pages;
        this.hasPrevious = page > 1;
    }

    /**
     * 创建分页结果
     *
     * @param records 数据列表
     * @param total   总记录数
     * @param page    当前页码
     * @param size    每页大小
     * @param <T>     数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> of(List<T> records, Long total, Integer page, Integer size) {
        return new PageResult<>(records, total, page, size);
    }

    /**
     * 创建空的分页结果
     *
     * @param page 当前页码
     * @param size 每页大小
     * @param <T>  数据类型
     * @return 空的分页结果
     */
    public static <T> PageResult<T> empty(Integer page, Integer size) {
        return new PageResult<>(new java.util.ArrayList<>(), 0L, page, size);
    }
} 