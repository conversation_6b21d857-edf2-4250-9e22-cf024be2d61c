package com.yeepay.ai.main.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 待确认事项DTO
 * 用于表示PRD中需要用户进一步确认的信息点
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PendingItemDTO implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 事项ID（用于标识）
     */
    private String id;

    /**
     * 事项标题/问题
     */
    private String title;

    /**
     * 事项描述
     */
    private String description;

    /**
     * 事项类型：
     * - SELECTION: 选择类型（单选或多选）
     * - INPUT: 输入类型（需要用户输入文本）
     * - CONFIRMATION: 确认类型（是/否）
     */
    private String type;

    /**
     * 选择项列表（当type为SELECTION时使用）
     */
    private List<String> options;

    /**
     * 是否必填
     */
    private Boolean required;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 提示信息
     */
    private String hint;

    /**
     * 在PRD中的位置标识（用于后续替换）
     */
    private String contextPath;

    // 事项类型常量
    public static final String TYPE_SELECTION = "SELECTION";
    public static final String TYPE_INPUT = "INPUT";
    public static final String TYPE_CONFIRMATION = "CONFIRMATION";
} 