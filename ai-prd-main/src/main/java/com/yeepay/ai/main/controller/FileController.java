package com.yeepay.ai.main.controller;

import com.yeepay.ai.main.common.exection.ResponseResult;
import com.yeepay.ai.main.service.FileService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 文件管理控制器
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Api(tags = "文件管理", description = "文件上传、下载、预览、删除等相关接口")
@Slf4j
@RestController
@RequestMapping("/api/file")
public class FileController extends BaseController {

    @Autowired
    private FileService fileService;

    /**
     * 上传单个文件
     *
     * @param file 上传的文件
     * @return 文件信息
     */
    @PostMapping("/upload")
    public ResponseResult<FileService.FileInfo> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            String userId = getCurrentUserId();
            log.info("User {} uploading file: {}", userId, file.getOriginalFilename());

            return fileService.uploadFile(file, userId);

        } catch (Exception e) {
            log.error("Failed to upload file: {}", file.getOriginalFilename(), e);
            return ResponseResult.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 批量上传文件
     *
     * @param files 上传的文件列表
     * @return 文件信息列表
     */
    @PostMapping("/upload/batch")
    public ResponseResult<List<FileService.FileInfo>> uploadFiles(@RequestParam("files") MultipartFile[] files) {
        try {
            String userId = getCurrentUserId();
            log.info("User {} uploading {} files", userId, files.length);

            List<FileService.FileInfo> fileInfoList = new ArrayList<>();
            List<String> errors = new ArrayList<>();

            for (MultipartFile file : files) {
                try {
                    ResponseResult<FileService.FileInfo> result = fileService.uploadFile(file, userId);
                    if (result.isSuccess()) {
                        fileInfoList.add(result.getData());
                    } else {
                        errors.add(file.getOriginalFilename() + ": " + result.getMessage());
                    }
                } catch (Exception e) {
                    errors.add(file.getOriginalFilename() + ": " + e.getMessage());
                    log.warn("Failed to upload file in batch: {}", file.getOriginalFilename(), e);
                }
            }

            if (!errors.isEmpty() && fileInfoList.isEmpty()) {
                // 全部失败
                return ResponseResult.error("所有文件上传失败: " + String.join("; ", errors));
            } else if (!errors.isEmpty()) {
                // 部分失败
                log.warn("Some files failed to upload: {}", errors);
            }

            return ResponseResult.success(fileInfoList);

        } catch (Exception e) {
            log.error("Failed to upload files in batch", e);
            return ResponseResult.error("批量上传失败: " + e.getMessage());
        }
    }

    /**
     * 读取文件内容（仅支持文本文件）
     *
     * @param relativePath 文件相对路径
     * @return 文件内容
     */
    @GetMapping("/content")
    public ResponseResult<String> readFileContent(@RequestParam String relativePath) {
        try {
            String userId = getCurrentUserId();
            log.debug("User {} reading file content: {}", userId, relativePath);

            return fileService.readFileContent(relativePath, userId);

        } catch (Exception e) {
            log.error("Failed to read file content: {}", relativePath, e);
            return ResponseResult.error("读取文件失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件
     *
     * @param relativePath 文件相对路径
     * @return 文件二进制数据
     */
    @GetMapping("/download")
    public ResponseEntity<byte[]> downloadFile(@RequestParam String relativePath) {
        try {
            String userId = getCurrentUserId();
            log.info("User {} downloading file: {}", userId, relativePath);

            ResponseResult<byte[]> result = fileService.downloadFile(relativePath, userId);
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().build();
            }

            // 从路径中提取文件名
            String fileName = relativePath.substring(relativePath.lastIndexOf("/") + 1);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(result.getData().length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(result.getData());

        } catch (Exception e) {
            log.error("Failed to download file: {}", relativePath, e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 删除文件
     *
     * @param relativePath 文件相对路径
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    public ResponseResult<Boolean> deleteFile(@RequestParam String relativePath) {
        try {
            String userId = getCurrentUserId();
            log.info("User {} deleting file: {}", userId, relativePath);

            return fileService.deleteFile(relativePath, userId);

        } catch (Exception e) {
            log.error("Failed to delete file: {}", relativePath, e);
            return ResponseResult.error("删除文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取上传配置信息
     *
     * @return 上传配置
     */
    @GetMapping("/upload/config")
    public ResponseResult<UploadConfig> getUploadConfig() {
        try {
            UploadConfig config = new UploadConfig();
            config.setMaxFileSize(10 * 1024 * 1024L); // 10MB
            config.setAllowedTypes(Arrays.asList("txt", "md", "docx", "pdf", "png", "jpg", "jpeg"));
            config.setMaxFiles(5);

            return ResponseResult.success(config);

        } catch (Exception e) {
            log.error("Failed to get upload config", e);
            return ResponseResult.error("获取上传配置失败: " + e.getMessage());
        }
    }

    /**
     * 验证文件是否可以上传
     *
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @return 验证结果
     */
    @PostMapping("/upload/validate")
    public ResponseResult<Boolean> validateFile(@RequestParam String fileName, @RequestParam Long fileSize) {
        try {
            String userId = getCurrentUserId();
            log.debug("User {} validating file: {} ({}B)", userId, fileName, fileSize);

            // 简单的验证逻辑
            if (fileSize > 10 * 1024 * 1024L) {
                return ResponseResult.error("文件大小超过限制（10MB）");
            }

            String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
            List<String> allowedTypes = Arrays.asList("txt", "md", "docx", "pdf", "png", "jpg", "jpeg");
            if (!allowedTypes.contains(extension)) {
                return ResponseResult.error("不支持的文件类型");
            }

            return ResponseResult.success(true);

        } catch (Exception e) {
            log.error("Failed to validate file: {}", fileName, e);
            return ResponseResult.error("文件验证失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件预览（仅支持文本文件）
     *
     * @param relativePath 文件相对路径
     * @return 文件预览内容
     */
    @GetMapping("/preview")
    public ResponseResult<String> previewFile(@RequestParam String relativePath) {
        try {
            String userId = getCurrentUserId();
            log.debug("User {} previewing file: {}", userId, relativePath);

            ResponseResult<String> contentResult = fileService.readFileContent(relativePath, userId);
            if (!contentResult.isSuccess()) {
                return contentResult;
            }

            String content = contentResult.getData();
            // 限制预览内容长度
            if (content.length() > 1000) {
                content = content.substring(0, 1000) + "\n\n... (内容过长，仅显示前1000字符)";
            }

            return ResponseResult.success(content);

        } catch (Exception e) {
            log.error("Failed to preview file: {}", relativePath, e);
            return ResponseResult.error("文件预览失败: " + e.getMessage());
        }
    }

    /**
     * 上传配置DTO
     */
    public static class UploadConfig {
        private Long maxFileSize;        // 最大文件大小（字节）
        private List<String> allowedTypes; // 允许的文件类型
        private Integer maxFiles;        // 最大文件数量

        // Getters and Setters
        public Long getMaxFileSize() { return maxFileSize; }
        public void setMaxFileSize(Long maxFileSize) { this.maxFileSize = maxFileSize; }

        public List<String> getAllowedTypes() { return allowedTypes; }
        public void setAllowedTypes(List<String> allowedTypes) { this.allowedTypes = allowedTypes; }

        public Integer getMaxFiles() { return maxFiles; }
        public void setMaxFiles(Integer maxFiles) { this.maxFiles = maxFiles; }
    }
} 