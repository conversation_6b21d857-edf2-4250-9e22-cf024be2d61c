/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ai.main.util.storage;

import com.yeepay.ai.main.common.constants.YeepayStorageConstants;
import com.yeepay.ai.main.util.config.ConfigEnum;
import com.yeepay.ai.main.util.config.ConfigUtils;
import com.yeepay.storage.sdk.StorageClient;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;

import java.io.InputStream;
import java.util.Map;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/24 14:54
 */
public class StorageUtils {
    private static final CloseableHttpClient HTTP_CLIENT = HttpClientBuilder.create().build();
    private static StorageClient client;
    private static Map<String, String> storageConfig = ConfigUtils.getConfigParam(ConfigEnum.STORAGE_CLIENT_CONFIG, Map.class);

    static {
        client = new StorageClient(storageConfig.get(YeepayStorageConstants.END_POINT), storageConfig.get(YeepayStorageConstants.SECRET));
        client.setHttpClient(HTTP_CLIENT);
    }

    /**
     * 上传文件到静态资源桶
     *
     * @param pathName    文件相对路径
     * @param inputStream 文件输入流
     * @return 文件访问地址
     */
    public static String upload(String pathName, InputStream inputStream) {
        client.put(storageConfig.get(YeepayStorageConstants.BUCKET_NAME), pathName, inputStream);
        return storageConfig.get(YeepayStorageConstants.ACCESS_HOST) + "/" + storageConfig.get(YeepayStorageConstants.BUCKET_NAME) + "/" + pathName;
    }

    /**
     * 下载文件
     *
     * @param pathName 文件相对路径
     * @return 文件输入流
     */
    public static InputStream download(String pathName) {
        return client.get(storageConfig.get(YeepayStorageConstants.BUCKET_NAME), pathName);
    }

    /**
     * 删除文件
     *
     * @param pathName 文件相对路径
     */
    public static void delete(String pathName) {
        client.delete(storageConfig.get(YeepayStorageConstants.BUCKET_NAME), pathName);
    }
}