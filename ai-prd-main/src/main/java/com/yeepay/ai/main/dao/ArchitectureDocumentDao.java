package com.yeepay.ai.main.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yeepay.ai.main.entity.ArchitectureDocument;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 架构设计文档DAO接口
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Mapper
public interface ArchitectureDocumentDao extends BaseMapper<ArchitectureDocument> {

    /**
     * 根据对话ID查询架构设计文档
     *
     * @param conversationId 对话ID
     * @return 架构设计文档
     */
    ArchitectureDocument selectByConversationId(@Param("conversationId") String conversationId);

    /**
     * 根据PRD ID查询架构设计文档
     *
     * @param prdId PRD文档ID
     * @return 架构设计文档
     */
    ArchitectureDocument selectByPrdId(@Param("prdId") String prdId);

    /**
     * 根据分享Token查询架构设计文档
     *
     * @param shareToken 分享Token
     * @return 架构设计文档
     */
    ArchitectureDocument selectByShareToken(@Param("shareToken") String shareToken);

    /**
     * 根据用户ID查询架构设计文档数量
     *
     * @param userId 用户ID
     * @return 文档数量
     */
    Integer countByUserId(@Param("userId") String userId);

    /**
     * 根据PRD ID查询最新版本的架构设计文档
     *
     * @param prdId PRD文档ID
     * @return 最新版本的架构设计文档
     */
    ArchitectureDocument selectLatestByPrdId(@Param("prdId") String prdId);

    /**
     * 根据PRD ID查询所有版本的架构设计文档
     *
     * @param prdId PRD文档ID
     * @return 所有版本的架构设计文档列表
     */
    java.util.List<ArchitectureDocument> selectAllVersionsByPrdId(@Param("prdId") String prdId);

    /**
     * 根据PRD ID和版本号查询架构设计文档
     *
     * @param prdId PRD文档ID
     * @param version 版本号
     * @return 指定版本的架构设计文档
     */
    ArchitectureDocument selectByPrdIdAndVersion(@Param("prdId") String prdId, @Param("version") Integer version);

    /**
     * 获取PRD对应的最大版本号
     *
     * @param prdId PRD文档ID
     * @return 最大版本号
     */
    Integer getMaxVersionByPrdId(@Param("prdId") String prdId);

    /**
     * 将PRD的所有架构文档版本标记为非最新
     *
     * @param prdId PRD文档ID
     * @return 更新的记录数
     */
    int markAllVersionsAsNotLatest(@Param("prdId") String prdId);

    /**
     * 检查是否需要重新生成架构文档（PRD有更新）
     *
     * @param prdId PRD文档ID
     * @param prdUpdateTime PRD最新更新时间
     * @return true-需要重新生成, false-不需要
     */
    boolean shouldRegenerateForPrdUpdate(@Param("prdId") String prdId, @Param("prdUpdateTime") java.time.LocalDateTime prdUpdateTime);
} 