package com.yeepay.ai.main.client.ai;

/**
 * AI配置常量定义
 * 定义AI_CONFIG配置项MAP中的key常量
 */
public class AIConstants {
    
    /**
     * AI配置MAP中的key定义
     */
    public static class CONFIG_KEYS {
        /** AI提供商类型 */
        public static final String PROVIDER = "provider";
        
        /** API基础URL */
        public static final String BASE_URL = "baseUrl";
        
        /** 模型名称 */
        public static final String MODEL = "model";
        
        /** 加密的API密钥 */
        public static final String API_KEY = "apiKey";
        
        /** 最大token数 */
        public static final String MAX_TOKENS = "maxTokens";
        
        /** 温度参数 */
        public static final String TEMPERATURE = "temperature";
        
        /** Top-P参数 */
        public static final String TOP_P = "topP";
    }
    
    /**
     * 支持的AI提供商名称
     */
    public static class PROVIDERS {
        /** DeepSeek */
        public static final String DEEPSEEK = "deepseek";
        
        /** OpenAI */
        public static final String OPENAI = "openai";
        
        /** Google Gemini */
        public static final String GEMINI = "gemini";
        
        /** OpenRouter */
        public static final String OPENROUTER = "openrouter";
    }
    
    /**
     * 默认配置值
     */
    public static class Defaults {
        /** 默认提供商 */
        public static final String PROVIDER = PROVIDERS.DEEPSEEK;
        
        /** DeepSeek默认配置 */
        public static final String DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1";
        public static final String DEEPSEEK_MODEL = "deepseek-chat";
        public static final int DEEPSEEK_MAX_TOKENS = 8000;
        public static final double DEEPSEEK_TEMPERATURE = 1.0;
        public static final double DEEPSEEK_TOP_P = 0.95;
        
        /** OpenAI默认配置 */
        public static final String OPENAI_BASE_URL = "https://api.openai.com/v1";
        public static final String OPENAI_MODEL = "gpt-3.5-turbo";
        public static final int OPENAI_MAX_TOKENS = 4000;
        public static final double OPENAI_TEMPERATURE = 0.7;
        public static final double OPENAI_TOP_P = 0.9;
        
        /** Gemini默认配置 */
        public static final String GEMINI_BASE_URL = "https://generativelanguage.googleapis.com";
        public static final String GEMINI_MODEL = "gemini-2.5-flash-preview-05-20";
        public static final int GEMINI_MAX_TOKENS = 8192;
        public static final double GEMINI_TEMPERATURE = 0.9;
        public static final double GEMINI_TOP_P = 0.95;
        
        /** OpenRouter默认配置 */
        public static final String OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1";
        public static final String OPENROUTER_MODEL = "google/gemini-2.5-flash";
        public static final int OPENROUTER_MAX_TOKENS = 8192;
        public static final double OPENROUTER_TEMPERATURE = 0.9;
        public static final double OPENROUTER_TOP_P = 0.95;
    }
    
    /**
     * API端点路径
     */
    public static class Endpoints {
        /** 聊天完成端点 */
        public static final String CHAT_COMPLETIONS = "/chat/completions";
        
        /** Gemini OpenAI兼容端点 */
        public static final String GEMINI_CHAT_COMPLETIONS = "/v1beta/openai/chat/completions";
        
        /** Gemini原生生成端点 */
        public static final String GEMINI_GENERATE = "/models/{model}:generateContent";
    }
    
    /**
     * 错误消息
     */
    public static class ErrorMessages {
        public static final String API_KEY_NOT_CONFIGURED = "API key is not configured for provider: %s";
        public static final String PROVIDER_NOT_SUPPORTED = "AI provider not supported: %s";
        public static final String CONFIG_PARSE_FAILED = "Failed to parse AI configuration";
        public static final String CLIENT_CREATE_FAILED = "Failed to create AI client for provider: %s";
        public static final String API_CALL_FAILED = "AI API call failed: %s";
        public static final String DECRYPT_KEY_FAILED = "Failed to decrypt API key";
    }
} 