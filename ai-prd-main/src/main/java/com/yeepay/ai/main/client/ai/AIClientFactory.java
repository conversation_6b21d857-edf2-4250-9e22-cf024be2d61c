package com.yeepay.ai.main.client.ai;

import com.yeepay.ai.main.client.ai.impl.DeepSeekClient;
import com.yeepay.ai.main.client.ai.impl.GeminiClient;
import com.yeepay.ai.main.client.ai.impl.OpenRouterClient;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * AI客户端工厂
 * 根据配置动态创建对应的AI客户端
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Component
public class AIClientFactory {
    
    private static final Logger logger = LogManager.getLogger(AIClientFactory.class);
    
    @Autowired
    private AIConfigService aiConfigService;
    
    /**
     * 创建AI客户端
     * @return AI客户端实例
     */
    public AIClient createClient() {
        AIConfigService.AIConfig config = aiConfigService.getAIConfig();
        return createClient(config);
    }
    
    /**
     * 根据配置创建AI客户端
     * @param config AI配置
     * @return AI客户端实例
     */
    public AIClient createClient(AIConfigService.AIConfig config) {
        if (config.getApiKey() == null || config.getApiKey().trim().isEmpty()) {
            logger.error("API key is not configured for provider: {}", config.getProvider());
            throw new RuntimeException("API key is not configured for provider: " + config.getProvider());
        }
        
        String provider = config.getProvider().toLowerCase();
        logger.debug("Creating AI client for provider: {}", provider);
        
        switch (provider) {
            case AIConstants.PROVIDERS.DEEPSEEK:
                return createDeepSeekClient(config);
            case AIConstants.PROVIDERS.OPENAI:
                return createOpenAIClient(config);
            case AIConstants.PROVIDERS.GEMINI:
                return createGeminiClient(config);
            case AIConstants.PROVIDERS.OPENROUTER:
                return createOpenRouterClient(config);
            default:
                logger.warn("Unknown AI provider: {}, falling back to DeepSeek", provider);
                return createDeepSeekClient(config);
        }
    }
    
    /**
     * 创建DeepSeek客户端
     */
    private AIClient createDeepSeekClient(AIConfigService.AIConfig config) {
        try {
            DeepSeekClient client = new DeepSeekClient();
            client.setApiKey(config.getApiKey());
            client.setBaseUrl(config.getBaseUrl());
            client.setModel(config.getModel());
            client.setMaxTokens(config.getMaxTokens());
            client.setTemperature(config.getTemperature());
            client.setTopP(config.getTopP());
            
            logger.info("Created DeepSeek client with model: {}", config.getModel());
            return client;
        } catch (Exception e) {
            logger.error("Failed to create DeepSeek client", e);
            throw new RuntimeException("Failed to create DeepSeek client", e);
        }
    }
    
    /**
     * 创建OpenAI客户端
     * TODO: 实现OpenAI客户端
     */
    private AIClient createOpenAIClient(AIConfigService.AIConfig config) {
        logger.error("OpenAI client not implemented yet");
        throw new UnsupportedOperationException("OpenAI client not implemented yet");
    }
    
    /**
     * 创建Gemini客户端
     */
    private AIClient createGeminiClient(AIConfigService.AIConfig config) {
        try {
            GeminiClient client = new GeminiClient();
            client.setApiKey(config.getApiKey());
            client.setBaseUrl(config.getBaseUrl());
            client.setModel(config.getModel());
            client.setMaxTokens(config.getMaxTokens());
            client.setTemperature(config.getTemperature());
            client.setTopP(config.getTopP());
            
            logger.info("Created Gemini client with model: {}", config.getModel());
            return client;
        } catch (Exception e) {
            logger.error("Failed to create Gemini client", e);
            throw new RuntimeException("Failed to create Gemini client", e);
        }
    }
    
    /**
     * 创建OpenRouter客户端
     */
    private AIClient createOpenRouterClient(AIConfigService.AIConfig config) {
        try {
            OpenRouterClient client = new OpenRouterClient();
            client.setApiKey(config.getApiKey());
            client.setBaseUrl(config.getBaseUrl());
            client.setModel(config.getModel());
            client.setMaxTokens(config.getMaxTokens());
            client.setTemperature(config.getTemperature());
            client.setTopP(config.getTopP());
            
            logger.info("Created OpenRouter client with model: {}", config.getModel());
            return client;
        } catch (Exception e) {
            logger.error("Failed to create OpenRouter client", e);
            throw new RuntimeException("Failed to create OpenRouter client", e);
        }
    }
} 