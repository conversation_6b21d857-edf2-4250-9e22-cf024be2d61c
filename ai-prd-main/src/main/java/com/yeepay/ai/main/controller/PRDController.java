package com.yeepay.ai.main.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.ai.main.common.exection.ResponseResult;
import com.yeepay.ai.main.dto.PRDDocumentDTO;
import com.yeepay.ai.main.dto.PRDVersionDTO;
import com.yeepay.ai.main.dto.SupplementInfoRequest;
import com.yeepay.ai.main.service.FileService;
import com.yeepay.ai.main.service.PRDGeneratorService;
import com.yeepay.ai.main.util.EncodingUtils;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.Executors;
import java.util.concurrent.ExecutorService;
import java.util.Map;
import java.util.HashMap;
import java.util.List;

/**
 * PRD文档管理控制器
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Api(tags = "PRD文档管理", description = "PRD文档生成、查看、编辑、分享、导出等相关接口")
@Slf4j
@RestController
@RequestMapping("/api/prd")
public class PRDController extends BaseController {

    @Autowired
    private PRDGeneratorService prdGeneratorService;

    @Autowired
    private FileService fileService;

    private final ExecutorService executor = Executors.newCachedThreadPool();

    /**
     * 生成PRD文档
     *
     * @param conversationId 对话ID
     * @return PRD文档信息
     */
    @ApiOperation(value = "生成PRD文档", notes = "基于对话内容生成产品需求文档")
    @ApiResponses({
            @ApiResponse(code = 200, message = "生成成功"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 404, message = "对话不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/generate/{conversationId}")
    public ResponseResult<PRDDocumentDTO> generatePRD(
            @ApiParam(value = "对话ID", required = true) @PathVariable String conversationId) {
        try {
            String userId = getCurrentUserId();
            log.info("User {} generating PRD for conversation: {}", userId, conversationId);

            return prdGeneratorService.generatePRD(conversationId, userId);

        } catch (Exception e) {
            log.error("Failed to generate PRD for conversation: {}", conversationId, e);
            return ResponseResult.error("生成PRD失败: " + e.getMessage());
        }
    }

    /**
     * 获取PRD文档
     *
     * @param conversationId 对话ID
     * @return PRD文档信息
     */
    @GetMapping("/conversation/{conversationId}")
    public ResponseResult<PRDDocumentDTO> getPRDDocument(@PathVariable String conversationId) {
        try {
            String userId = getCurrentUserId();
            log.debug("User {} getting PRD for conversation: {}", userId, conversationId);

            return prdGeneratorService.getPRDDocument(conversationId, userId);

        } catch (Exception e) {
            log.error("Failed to get PRD for conversation: {}", conversationId, e);
            return ResponseResult.error("获取PRD文档失败: " + e.getMessage());
        }
    }

    /**
     * 更新PRD文档内容
     *
     * @param prdId   PRD文档ID
     * @param content 新内容
     * @return 更新后的PRD文档信息
     */
    @PutMapping("/{prdId}/content")
    public ResponseResult<PRDDocumentDTO> updatePRDContent(@PathVariable String prdId,
                                                           @RequestBody String content) {
        try {
            String userId = getCurrentUserId();
            log.info("User {} updating PRD content: {}", userId, prdId);

            return prdGeneratorService.updatePRDContent(prdId, userId, content);

        } catch (Exception e) {
            log.error("Failed to update PRD content: {}", prdId, e);
            return ResponseResult.error("更新PRD内容失败: " + e.getMessage());
        }
    }

    /**
     * 更新PRD文档标题
     *
     * @param prdId PRD文档ID
     * @param title 新标题
     * @return 更新后的PRD文档信息
     */
    @PutMapping("/{prdId}/title")
    public ResponseResult<PRDDocumentDTO> updatePRDTitle(@PathVariable String prdId,
                                                         @RequestBody String title) {
        try {
            String userId = getCurrentUserId();
            log.info("User {} updating PRD title: {}", userId, prdId);

            return prdGeneratorService.updatePRDTitle(prdId, userId, title);

        } catch (Exception e) {
            log.error("Failed to update PRD title: {}", prdId, e);
            return ResponseResult.error("更新PRD标题失败: " + e.getMessage());
        }
    }

    /**
     * 更新PRD文档（包含标题和内容）
     *
     * @param prdId   PRD文档ID
     * @param request 更新请求
     * @return 更新后的PRD文档信息
     */
    @PutMapping("/{prdId}")
    public ResponseResult<PRDDocumentDTO> updatePRD(@PathVariable String prdId,
                                                    @RequestBody Map<String, String> request) {
        try {
            String userId = getCurrentUserId();
            log.info("User {} updating PRD: {}", userId, prdId);

            String title = request.get("title");
            String content = request.get("content");

            if (title != null && content != null) {
                // 更新标题和内容
                ResponseResult<PRDDocumentDTO> titleResult = prdGeneratorService.updatePRDTitle(prdId, userId, title);
                if (!titleResult.isSuccess()) {
                    return titleResult;
                }
                return prdGeneratorService.updatePRDContent(prdId, userId, content);
            } else if (title != null) {
                // 只更新标题
                return prdGeneratorService.updatePRDTitle(prdId, userId, title);
            } else if (content != null) {
                // 只更新内容
                return prdGeneratorService.updatePRDContent(prdId, userId, content);
            } else {
                return ResponseResult.error("请提供要更新的标题或内容");
            }

        } catch (Exception e) {
            log.error("Failed to update PRD: {}", prdId, e);
            return ResponseResult.error("更新PRD失败: " + e.getMessage());
        }
    }

    /**
     * 设置PRD文档分享状态
     *
     * @param prdId     PRD文档ID
     * @param shareable 是否可分享
     * @return 更新后的PRD文档信息
     */
    @PutMapping("/{prdId}/share")
    public ResponseResult<PRDDocumentDTO> setPRDShareStatus(@PathVariable String prdId,
                                                            @RequestParam boolean shareable) {
        try {
            String userId = getCurrentUserId();
            log.info("User {} setting PRD share status: {} -> {}", userId, prdId, shareable);

            return prdGeneratorService.setPRDShareStatus(prdId, userId, shareable);

        } catch (Exception e) {
            log.error("Failed to set PRD share status: {}", prdId, e);
            return ResponseResult.error("设置分享状态失败: " + e.getMessage());
        }
    }

    /**
     * 通过分享Token获取PRD文档（公开访问，无需登录）
     *
     * @param shareToken 分享Token
     * @return PRD文档信息
     */
    @GetMapping("/share/{shareToken}")
    public ResponseResult<PRDDocumentDTO> getPRDByShareToken(@PathVariable String shareToken) {
        try {
            log.info("Getting PRD by share token: {}", shareToken);

            return prdGeneratorService.getPRDByShareToken(shareToken);

        } catch (Exception e) {
            log.error("Failed to get PRD by share token: {}", shareToken, e);
            return ResponseResult.error("获取分享文档失败: " + e.getMessage());
        }
    }

    /**
     * 导出PRD为Markdown文件
     *
     * @param prdId PRD文档ID
     * @return 文件下载响应
     */
    @GetMapping("/{prdId}/export")
    public ResponseEntity<byte[]> exportPRDAsMarkdown(@PathVariable String prdId) {
        try {
            String userId = getCurrentUserId();
            log.info("User {} exporting PRD as markdown: {}", userId, prdId);

            // 获取PRD文档
            ResponseResult<PRDDocumentDTO> prdResult = prdGeneratorService.getPRDDocument("", userId);
            if (!prdResult.isSuccess()) {
                return ResponseEntity.badRequest().build();
            }

            PRDDocumentDTO prdDocument = prdResult.getData();
            String content = prdDocument.getContent();
            String fileName = prdDocument.getTitle() + "_v" + prdDocument.getVersion() + ".md";

            // 导出为文件
            ResponseResult<FileService.FileInfo> fileResult = 
                    fileService.exportPRDAsMarkdown(content, fileName, userId);

            if (!fileResult.isSuccess()) {
                return ResponseEntity.badRequest().build();
            }

            // 构建响应
            byte[] fileBytes = content.getBytes(StandardCharsets.UTF_8);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(fileBytes.length);

            log.info("PRD exported successfully: {}", fileName);
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(fileBytes);

        } catch (Exception e) {
            log.error("Failed to export PRD: {}", prdId, e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 预览PRD文档（返回HTML格式）
     *
     * @param prdId    PRD文档ID
     * @param response HTTP响应
     */
    @GetMapping("/{prdId}/preview")
    public void previewPRD(@PathVariable String prdId, HttpServletResponse response) {
        try {
            String userId = getCurrentUserId();
            log.debug("User {} previewing PRD: {}", userId, prdId);

            // 获取PRD文档
            ResponseResult<PRDDocumentDTO> prdResult = prdGeneratorService.getPRDDocument("", userId);
            if (!prdResult.isSuccess()) {
                response.setStatus(404);
                return;
            }

            PRDDocumentDTO prdDocument = prdResult.getData();
            
            // 构建简单的HTML预览页面
            String htmlContent = buildHTMLPreview(prdDocument);

            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().write(htmlContent);
            response.getWriter().flush();

            log.debug("PRD preview generated successfully: {}", prdId);

        } catch (IOException e) {
            log.error("Failed to preview PRD: {}", prdId, e);
            try {
                response.setStatus(500);
                response.getWriter().write("预览生成失败");
            } catch (IOException ioException) {
                log.error("Failed to write error response", ioException);
            }
        } catch (Exception e) {
            log.error("Failed to preview PRD: {}", prdId, e);
            try {
                response.setStatus(500);
                response.getWriter().write("预览生成失败: " + e.getMessage());
            } catch (IOException ioException) {
                log.error("Failed to write error response", ioException);
            }
        }
    }

    /**
     * 构建HTML预览内容
     */
    private String buildHTMLPreview(PRDDocumentDTO prdDocument) {
        StringBuilder htmlBuilder = new StringBuilder();
        htmlBuilder.append("<!DOCTYPE html>\n")
                .append("<html lang=\"zh-CN\">\n")
                .append("<head>\n")
                .append("    <meta charset=\"UTF-8\">\n")
                .append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n")
                .append("    <title>").append(prdDocument.getTitle()).append(" - PRD预览</title>\n")
                .append("    <style>\n")
                .append("        body {\n")
                .append("            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n")
                .append("            line-height: 1.6;\n")
                .append("            color: #333;\n")
                .append("            max-width: 800px;\n")
                .append("            margin: 0 auto;\n")
                .append("            padding: 20px;\n")
                .append("            background-color: #fafafa;\n")
                .append("        }\n")
                .append("        .container {\n")
                .append("            background: white;\n")
                .append("            border-radius: 8px;\n")
                .append("            padding: 40px;\n")
                .append("            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n")
                .append("        }\n")
                .append("        h1, h2, h3 { color: #2c3e50; }\n")
                .append("        h1 { border-bottom: 3px solid #3498db; padding-bottom: 10px; }\n")
                .append("        h2 { border-bottom: 1px solid #ecf0f1; padding-bottom: 5px; }\n")
                .append("        blockquote {\n")
                .append("            background: #f8f9fa;\n")
                .append("            border-left: 4px solid #3498db;\n")
                .append("            margin: 1.5em 0;\n")
                .append("            padding: 1em;\n")
                .append("            font-style: italic;\n")
                .append("        }\n")
                .append("        code {\n")
                .append("            background: #f1f2f6;\n")
                .append("            padding: 2px 4px;\n")
                .append("            border-radius: 3px;\n")
                .append("            font-family: 'Monaco', 'Menlo', monospace;\n")
                .append("        }\n")
                .append("        .meta {\n")
                .append("            background: #ecf0f1;\n")
                .append("            padding: 15px;\n")
                .append("            border-radius: 5px;\n")
                .append("            margin-bottom: 30px;\n")
                .append("            font-size: 14px;\n")
                .append("            color: #7f8c8d;\n")
                .append("        }\n")
                .append("        .content {\n")
                .append("            white-space: pre-wrap;\n")
                .append("        }\n")
                .append("    </style>\n")
                .append("</head>\n")
                .append("<body>\n")
                .append("    <div class=\"container\">\n")
                .append("        <div class=\"meta\">\n")
                .append("            <strong>文档信息：</strong> ").append(prdDocument.getTitle())
                .append(" | 版本：").append(prdDocument.getVersion())
                .append(" | 状态：").append(prdDocument.getStatusDesc())
                .append(" | 更新时间：").append(prdDocument.getUpdateTime().toString()).append("\n")
                .append("        </div>\n")
                .append("        <div class=\"content\">").append(prdDocument.getContent()).append("</div>\n")
                .append("    </div>\n")
                .append("</body>\n")
                .append("</html>");
        
        return htmlBuilder.toString();
    }

    /**
     * 流式生成PRD文档
     *
     * @param conversationId 对话ID
     * @return SSE流式响应
     */
    @ApiOperation(value = "流式生成PRD文档", notes = "基于对话内容流式生成产品需求文档，实时显示生成进度")
    @GetMapping(value = "/stream/generate/{conversationId}", produces = "text/event-stream; charset=utf-8")
    public SseEmitter streamGeneratePRD(
            @ApiParam(value = "对话ID", required = true) @PathVariable String conversationId,
            @ApiParam(value = "用户ID", required = false) @RequestParam(value = "userId", required = false) String userIdParam,
            HttpServletResponse response) {
        
        // 设置响应编码和头部
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Type", "text/event-stream; charset=utf-8");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Headers", "Cache-Control");
        
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        
        // 优先使用URL参数中的userId，如果没有则尝试从认证信息获取
        final String userId = getUserId(userIdParam);
        
        log.debug("Using userId: {}", userId);
        
        final String finalConversationId = conversationId;
        log.info("User {} starting stream PRD generation for conversation: {}", userId, finalConversationId);

        executor.execute(() -> {
            try {
                // 发送开始事件
                Map<String, Object> startData = new HashMap<>();
                startData.put("message", EncodingUtils.ensureUtf8("开始生成PRD文档..."));
                startData.put("progress", 0);
                startData.put("timestamp", System.currentTimeMillis());
                
                ObjectMapper objectMapper = new ObjectMapper();
                emitter.send(SseEmitter.event()
                    .name("start")
                    .data(objectMapper.writeValueAsString(startData))
                );

                prdGeneratorService.generatePRDStream(finalConversationId, userId,
                    // 进度回调
                    (progress, message) -> {
                        try {
                            Map<String, Object> progressData = new HashMap<>();
                            progressData.put("progress", progress);
                            progressData.put("message", EncodingUtils.ensureUtf8(message));
                            progressData.put("timestamp", System.currentTimeMillis());

                            emitter.send(SseEmitter.event()
                                .name("progress")
                                .data(objectMapper.writeValueAsString(progressData))
                            );
                        } catch (Exception e) {
                            log.warn("Debug - Failed to send progress event, error: {}", e.getMessage());
                        }
                    },
                    // 内容回调
                    (content) -> {
                        try {
                            // 发送内容事件 - 确保UTF-8编码正确处理
                            Map<String, Object> contentData = new HashMap<>();
                            contentData.put("content", EncodingUtils.cleanAndEnsureUtf8(content));
                            contentData.put("timestamp", System.currentTimeMillis());
                            
                            // 使用标准JSON序列化，避免FastJSON的编码问题
                           // ObjectMapper objectMapper = new ObjectMapper();
                            String jsonData = objectMapper.writeValueAsString(contentData);
                            
                            emitter.send(SseEmitter.event()
                                .name("content")
                                .data(jsonData)
                            );
                        } catch (Exception e) {
                            log.warn("Debug - Failed to send content chunk, error: {}", e.getMessage());
                        }
                    },
                    // 完成回调
                    (prdDocument) -> {
                        try {
                            Map<String, Object> completeData = new HashMap<>();
                            completeData.put("prdId", prdDocument.getId());
                            completeData.put("message", EncodingUtils.ensureUtf8("PRD文档生成完成"));
                            completeData.put("timestamp", System.currentTimeMillis());
                            
                            emitter.send(SseEmitter.event()
                                .name("complete")
                                .data(objectMapper.writeValueAsString(completeData))
                            );
                            emitter.complete();
                            log.info("Debug - Stream completed successfully for conversation: {}", finalConversationId);
                        } catch (Exception e) {
                            log.warn("Debug - Failed to send complete event, error: {}", e.getMessage());
                            emitter.completeWithError(e);
                        }
                    },
                    // 错误回调
                    (error) -> {
                        try {
                            Map<String, Object> errorData = new HashMap<>();
                            errorData.put("error", EncodingUtils.ensureUtf8(error.getMessage()));
                            errorData.put("message", EncodingUtils.ensureUtf8("PRD生成失败"));
                            errorData.put("timestamp", System.currentTimeMillis());
                            
                            emitter.send(SseEmitter.event()
                                .name("error")
                                .data(objectMapper.writeValueAsString(errorData))
                            );
                            emitter.completeWithError(error);
                            log.error("Debug - Stream failed for conversation: {}, error: {}", finalConversationId, error.getMessage());
                        } catch (Exception e) {
                            log.error("Debug - Failed to send error event", e);
                            emitter.completeWithError(error);
                        }
                    }
                );

            } catch (Exception e) {
                log.error("Stream PRD generation failed for conversation: {}", finalConversationId, e);
                try {
                    Map<String, Object> errorData = new HashMap<>();
                    errorData.put("error", EncodingUtils.ensureUtf8("PRD生成失败: " + e.getMessage()));
                    errorData.put("message", EncodingUtils.ensureUtf8("PRD生成失败"));
                    errorData.put("timestamp", System.currentTimeMillis());
                    
                    ObjectMapper objectMapper = new ObjectMapper();
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data(objectMapper.writeValueAsString(errorData)));
                } catch (Exception sendError) {
                    log.error("Failed to send error event", sendError);
                }
                emitter.completeWithError(e);
            }
        });

        emitter.onCompletion(() -> {
            log.debug("Stream PRD generation completed for conversation: {}", finalConversationId);
        });

        emitter.onTimeout(() -> {
            log.warn("Stream PRD generation timed out for conversation: {}", finalConversationId);
            emitter.complete();
        });

        emitter.onError((ex) -> {
            log.error("Stream PRD generation error for conversation: {}", finalConversationId, ex);
        });

        return emitter;
    }

    /**
     * 获取用户ID（优先使用URL参数，否则从认证信息获取）
     */
    private String getUserId(String userIdParam) {
        if (StringUtils.isNotBlank(userIdParam)) {
            log.debug("Using userId from URL parameter: {}", userIdParam);
            return userIdParam;
        }
        
        try {
            String userId = getCurrentUserId();
            log.debug("Using userId from authentication: {}", userId);
            return userId;
        } catch (Exception e) {
            String defaultUserId = "web-user-" + System.currentTimeMillis();
            log.warn("Failed to get userId from authentication, using default: {}", defaultUserId);
            return defaultUserId;
        }
    }

    /**
     * 补充信息并重新生成PRD
     */
    @ApiOperation(value = "补充信息重新生成PRD", notes = "用户补充需求信息后重新生成PRD文档")
    @PostMapping("/supplement-regenerate")
    public ResponseResult<PRDDocumentDTO> supplementAndRegeneratePRD(
            @ApiParam(value = "补充信息请求", required = true)
            @Valid @RequestBody SupplementInfoRequest request) {
        try {
            log.info("User {} supplementing info and regenerating PRD: {}", 
                    request.getUserId(), request.getPrdId());

            return prdGeneratorService.supplementAndRegeneratePRD(request);

        } catch (Exception e) {
            log.error("Failed to supplement and regenerate PRD: {}", request.getPrdId(), e);
            return ResponseResult.error("补充信息重新生成PRD失败: " + e.getMessage());
        }
    }

    /**
     * 获取PRD版本历史
     */
    @ApiOperation(value = "获取PRD版本历史", notes = "获取指定对话的所有PRD版本历史")
    @GetMapping("/versions/{conversationId}")
    public ResponseResult<List<PRDVersionDTO>> getPRDVersionHistory(
            @ApiParam(value = "对话ID", required = true) 
            @PathVariable String conversationId,
            @ApiParam(value = "用户ID", required = true) 
            @RequestParam String userId) {
        try {
            log.debug("Getting PRD version history for conversation: {}", conversationId);

            return prdGeneratorService.getPRDVersionHistory(conversationId, userId);

        } catch (Exception e) {
            log.error("Failed to get PRD version history for conversation: {}", conversationId, e);
            return ResponseResult.error("获取版本历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定版本的PRD文档
     */
    @ApiOperation(value = "获取指定版本PRD", notes = "获取特定版本的PRD文档内容")
    @GetMapping("/version/{prdId}")
    public ResponseResult<PRDDocumentDTO> getPRDByVersion(
            @ApiParam(value = "PRD文档ID", required = true) 
            @PathVariable String prdId,
            @ApiParam(value = "用户ID", required = true) 
            @RequestParam String userId) {
        try {
            log.debug("Getting PRD by version: {}", prdId);

            return prdGeneratorService.getPRDByVersion(prdId, userId);

        } catch (Exception e) {
            log.error("Failed to get PRD by version: {}", prdId, e);
            return ResponseResult.error("获取指定版本PRD失败: " + e.getMessage());
        }
    }

    /**
     * 恢复到指定版本
     */
    @ApiOperation(value = "恢复到指定版本", notes = "将指定版本设为当前版本")
    @PostMapping("/restore-version/{prdId}")
    public ResponseResult<PRDDocumentDTO> restoreToVersion(
            @ApiParam(value = "PRD文档ID", required = true) 
            @PathVariable String prdId,
            @ApiParam(value = "用户ID", required = true) 
            @RequestParam String userId) {
        try {
            log.info("User {} restoring PRD to version: {}", userId, prdId);

            return prdGeneratorService.restoreToVersion(prdId, userId);

        } catch (Exception e) {
            log.error("Failed to restore PRD to version: {}", prdId, e);
            return ResponseResult.error("恢复版本失败: " + e.getMessage());
        }
    }
} 