package com.yeepay.ai.main.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yeepay.ai.main.common.exection.BusinessException;
import com.yeepay.ai.main.common.exection.ResponseResult;
import com.yeepay.ai.main.dao.ConversationDao;
import com.yeepay.ai.main.dao.ConversationMessageDao;
import com.yeepay.ai.main.dao.PRDDocumentDao;
import com.yeepay.ai.main.dto.*;
import com.yeepay.ai.main.dto.AsyncMessageResponse;
import com.yeepay.ai.main.entity.Conversation;
import com.yeepay.ai.main.entity.ConversationMessage;
import com.yeepay.ai.main.entity.PRDDocument;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 对话管理服务
 * 
 * 注意事项：
 * 1. 异步方法 (@Async) 不会继承调用者的事务上下文
 * 2. 需要数据库操作的方法必须添加 @Transactional 注解
 * 3. 异步方法内部调用的数据库操作方法应该有自己的事务
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Slf4j
@Service
public class ConversationService {

    @Autowired
    private ConversationDao conversationDao;

    @Autowired
    private ConversationMessageDao conversationMessageDao;

    @Autowired
    private PRDDocumentDao prdDocumentDao;

    @Autowired
    private AIAnalysisService aiAnalysisService;

    @Autowired
    private TitleGenerationService titleGenerationService;
    
    @Autowired
    private AsyncMessageProcessingService asyncProcessingService;

    /**
     * 创建新对话
     *
     * @param userId  用户ID
     * @param request 创建请求
     * @return 对话DTO
     */
    @Transactional
    public ResponseResult<ConversationDTO> createConversation(String userId, CreateConversationRequest request) {
        try {
            log.info("Creating conversation for user: {}, title: {}", userId, request.getTitle());

            // 创建对话记录
            Conversation conversation = new Conversation();
            conversation.setUserId(userId);
            conversation.setTitle(request.getTitle());
            conversation.setStatus(Conversation.STATUS_IN_PROGRESS);
            conversation.setConfidenceScore(0);
            conversation.setCreateTime(LocalDateTime.now());
            conversation.setUpdateTime(LocalDateTime.now());
            conversation.setCreateUser(userId);
            conversation.setUpdateUser(userId);
            conversation.setNonce(0L);

            conversationDao.insert(conversation);

            // 如果有初始内容，保存为第一条消息
            if (StringUtils.isNotBlank(request.getInitialContent())) {
                ConversationMessage message = new ConversationMessage();
                message.setConversationId(conversation.getId());
                message.setSenderType(ConversationMessage.SENDER_TYPE_USER);
                message.setContentType(ConversationMessage.CONTENT_TYPE_TEXT);
                message.setContent(request.getInitialContent());
                message.setCreateTime(LocalDateTime.now());

                conversationMessageDao.insert(message);

                log.info("Initial message saved for conversation: {}", conversation.getId());
            }

            ConversationDTO dto = convertToDTO(conversation);
            log.info("Conversation created successfully: {}", conversation.getId());

            return ResponseResult.success(dto);

        } catch (Exception e) {
            log.error("Failed to create conversation for user: {}", userId, e);
            return ResponseResult.error("创建对话失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户对话列表（默认返回最新的10条）
     *
     * @param userId 用户ID
     * @return 对话列表
     */
    public ResponseResult<List<ConversationDTO>> getConversationList(String userId) {
        try {
            log.debug("Getting conversation list for user: {}", userId);

            QueryWrapper<Conversation> wrapper = new QueryWrapper<>();
            wrapper.eq("user_id", userId)
                    .ne("status", Conversation.STATUS_DELETED)
                    .orderByDesc("update_time")
                    .last("LIMIT 10");  // 限制返回最新的10条对话

            List<Conversation> conversations = conversationDao.selectList(wrapper);

            List<ConversationDTO> dtoList = conversations.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            log.debug("Found {} conversations for user: {} (limited to 10 latest)", dtoList.size(), userId);
            return ResponseResult.success(dtoList);

        } catch (Exception e) {
            log.error("Failed to get conversation list for user: {}", userId, e);
            return ResponseResult.error("获取对话列表失败: " + e.getMessage());
        }
    }

    /**
     * 发送消息（异步处理版本）
     *
     * @param userId         用户ID
     * @param conversationId 对话ID
     * @param request        发送消息请求
     * @return 异步处理响应
     */
    @Transactional
    public ResponseResult<AsyncMessageResponse> sendMessage(String userId, String conversationId, SendMessageRequest request) {
        try {
            log.info("User {} sending message to conversation: {}", userId, conversationId);

            // 验证对话归属
            Conversation conversation = conversationDao.selectById(conversationId);
            if (conversation == null || !userId.equals(conversation.getUserId())) {
                throw new BusinessException("对话不存在或无权限访问");
            }

            // 保存用户消息
            ConversationMessage userMessage = new ConversationMessage();
            userMessage.setConversationId(conversationId);
            userMessage.setSenderType(ConversationMessage.SENDER_TYPE_USER);
            userMessage.setContentType(request.getContentType());
            userMessage.setContent(request.getContent());
            if (request.getFileUrls() != null) {
                userMessage.setFileUrls(com.alibaba.fastjson.JSON.toJSONString(request.getFileUrls()));
            }
            if (request.getMetadata() != null) {
                userMessage.setMetadata(com.alibaba.fastjson.JSON.toJSONString(request.getMetadata()));
            }
            userMessage.setCreateTime(LocalDateTime.now());

            conversationMessageDao.insert(userMessage);

            // 异步生成标题（消息保存后执行，确保时序正确）
            tryGenerateConversationTitleAsync(conversation.getId(), request.getContent(), userId);

            // 创建异步处理任务
            AsyncMessageResponse asyncResponse = asyncProcessingService.createProcessingTask(
                    userMessage.getId(), conversationId);

            // 启动异步AI分析
            aiAnalysisService.analyzeUserMessageAsync(conversationId, request, userMessage.getId());

            // 验证任务状态是否保存成功（调试用）
            try {
                Thread.sleep(200); // 等待200ms
                AsyncMessageResponse verifyResponse = asyncProcessingService.getProcessingStatus(userMessage.getId());
                if (verifyResponse != null) {
                    log.info("Task status verified successfully: {}", userMessage.getId());
                } else {
                    log.warn("Task status verification failed - not found: {}", userMessage.getId());
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Thread interrupted during task verification");
            }

            log.info("Message sent and async processing started for conversation: {}, userMessageId: {}", 
                     conversationId, userMessage.getId());

            return ResponseResult.success(asyncResponse);

        } catch (BusinessException e) {
            log.warn("Business error in sendMessage: {}", e.getMessage());
            return ResponseResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to send message to conversation: {}", conversationId, e);
            return ResponseResult.error("发送消息失败: " + e.getMessage());
        }
    }

    /**
     * 发送消息（同步处理版本，用于兼容）
     *
     * @param userId         用户ID
     * @param conversationId 对话ID
     * @param request        发送消息请求
     * @return AI回复
     */
    @Transactional
    public ResponseResult<MessageDTO> sendMessageSync(String userId, String conversationId, SendMessageRequest request) {
        try {
            log.info("User {} sending message to conversation: {} (sync mode)", userId, conversationId);

            // 验证对话归属
            Conversation conversation = conversationDao.selectById(conversationId);
            if (conversation == null || !userId.equals(conversation.getUserId())) {
                throw new BusinessException("对话不存在或无权限访问");
            }

            // 保存用户消息
            ConversationMessage userMessage = new ConversationMessage();
            userMessage.setConversationId(conversationId);
            userMessage.setSenderType(ConversationMessage.SENDER_TYPE_USER);
            userMessage.setContentType(request.getContentType());
            userMessage.setContent(request.getContent());
            if (request.getFileUrls() != null) {
                userMessage.setFileUrls(com.alibaba.fastjson.JSON.toJSONString(request.getFileUrls()));
            }
            if (request.getMetadata() != null) {
                userMessage.setMetadata(com.alibaba.fastjson.JSON.toJSONString(request.getMetadata()));
            }
            userMessage.setCreateTime(LocalDateTime.now());

            conversationMessageDao.insert(userMessage);

            // 异步生成标题（消息保存后执行，确保时序正确）
            tryGenerateConversationTitleAsync(conversation.getId(), request.getContent(), userId);

            // 调用AI分析（同步）
            ResponseResult<MessageDTO> aiResponse = aiAnalysisService.analyzeUserMessage(conversationId, request);

            if (aiResponse.isSuccess()) {
                // 只更新信心指数和时间，避免覆盖异步任务已更新的标题
                Integer confidenceScore = aiResponse.getData().getConfidenceScore();
                updateConfidenceScore(conversationId, confidenceScore);

                log.info("Message processed successfully for conversation: {}", conversationId);
            }

            return aiResponse;

        } catch (BusinessException e) {
            log.warn("Business error in sendMessageSync: {}", e.getMessage());
            return ResponseResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to send message to conversation: {}", conversationId, e);
            return ResponseResult.error("发送消息失败: " + e.getMessage());
        }
    }

    /**
     * 获取对话消息列表（兼容旧接口，默认返回最新的10条消息）
     *
     * @param userId         用户ID
     * @param conversationId 对话ID
     * @return 消息列表
     */
    public ResponseResult<List<MessageDTO>> getMessages(String userId, String conversationId) {
        try {
            log.debug("Getting messages for conversation: {}", conversationId);

            // 验证对话归属
            Conversation conversation = conversationDao.selectById(conversationId);
            if (conversation == null || !userId.equals(conversation.getUserId())) {
                throw new BusinessException("对话不存在或无权限访问");
            }

            // 默认查询最新的10条消息（按时间正序，最新消息在下）
            List<ConversationMessage> messages = conversationMessageDao.selectByConversationIdWithPage(
                    conversationId, 0, 10);

            List<MessageDTO> dtoList = messages.stream()
                    .map(this::convertMessageToDTO)
                    .collect(Collectors.toList());

            log.debug("Found {} messages for conversation: {}", dtoList.size(), conversationId);
            return ResponseResult.success(dtoList);

        } catch (BusinessException e) {
            log.warn("Business error in getMessages: {}", e.getMessage());
            return ResponseResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to get messages for conversation: {}", conversationId, e);
            return ResponseResult.error("获取消息列表失败: " + e.getMessage());
        }
    }

    /**
     * 分页获取对话消息列表
     *
     * @param userId         用户ID
     * @param conversationId 对话ID
     * @param pageRequest    分页请求参数
     * @return 分页消息列表
     */
    public ResponseResult<PageResult<MessageDTO>> getMessagesByPage(String userId, String conversationId, PageRequest pageRequest) {
        try {
            log.debug("Getting messages by page for conversation: {}, page: {}, size: {}", 
                     conversationId, pageRequest.getPage(), pageRequest.getSize());

            // 验证对话归属
            Conversation conversation = conversationDao.selectById(conversationId);
            if (conversation == null || !userId.equals(conversation.getUserId())) {
                throw new BusinessException("对话不存在或无权限访问");
            }

            // 获取总数
            Integer totalCount = conversationMessageDao.countByConversationId(conversationId);
            if (totalCount == 0) {
                return ResponseResult.success(PageResult.empty(pageRequest.getPage(), pageRequest.getSize()));
            }

            // 分页查询消息（按时间正序，最新消息在下）
            List<ConversationMessage> messages = conversationMessageDao.selectByConversationIdWithPage(
                    conversationId, pageRequest.getOffset(), pageRequest.getSize());

            List<MessageDTO> dtoList = messages.stream()
                    .map(this::convertMessageToDTO)
                    .collect(Collectors.toList());

            PageResult<MessageDTO> pageResult = PageResult.of(dtoList, totalCount.longValue(), 
                                                             pageRequest.getPage(), pageRequest.getSize());

            log.debug("Found {} messages in page {} for conversation: {}", 
                     dtoList.size(), pageRequest.getPage(), conversationId);
            return ResponseResult.success(pageResult);

        } catch (BusinessException e) {
            log.warn("Business error in getMessagesByPage: {}", e.getMessage());
            return ResponseResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to get messages by page for conversation: {}", conversationId, e);
            return ResponseResult.error("获取消息列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取对话信息
     *
     * @param conversationId 对话ID
     * @return 对话DTO
     */
    public Optional<ConversationDTO> getConversationById(String conversationId) {
        try {
            Conversation conversation = conversationDao.selectById(conversationId);
            if (conversation != null) {
                ConversationDTO dto = convertToDTO(conversation);
                log.debug("Found conversation: {}", conversationId);
                return Optional.of(dto);
            } else {
                log.debug("Conversation not found: {}", conversationId);
                return Optional.empty();
            }
        } catch (Exception e) {
            log.error("Failed to get conversation by id: {}", conversationId, e);
            return Optional.empty();
        }
    }

    /**
     * 更新对话信心指数
     *
     * @param conversationId  对话ID
     * @param confidenceScore 信心指数
     */
    public void updateConfidenceScore(String conversationId, Integer confidenceScore) {
        try {
            // 先获取当前对话获取nonce
            Conversation conversation = conversationDao.selectById(conversationId);
            if (conversation != null) {
                conversationDao.updateConfidenceScore(conversationId, confidenceScore, conversation.getNonce());
                log.debug("Updated confidence score for conversation {}: {}", conversationId, confidenceScore);
            } else {
                log.warn("Conversation not found: {}", conversationId);
            }
        } catch (Exception e) {
            log.error("Failed to update confidence score for conversation: {}", conversationId, e);
        }
    }

    /**
     * 重命名对话
     *
     * @param userId         用户ID
     * @param conversationId 对话ID
     * @param newTitle       新标题
     * @return 更新后的对话信息
     */
    @Transactional
    public ResponseResult<ConversationDTO> renameConversation(String userId, String conversationId, String newTitle) {
        try {
            log.info("User {} renaming conversation {} to: {}", userId, conversationId, newTitle);

            // 验证参数
            if (StringUtils.isBlank(newTitle)) {
                return ResponseResult.error("标题不能为空");
            }

            if (newTitle.length() > 200) {
                return ResponseResult.error("标题长度不能超过200字符");
            }

            // 验证对话归属
            Conversation conversation = conversationDao.selectById(conversationId);
            if (conversation == null) {
                return ResponseResult.error("对话不存在");
            }

            if (!userId.equals(conversation.getUserId())) {
                return ResponseResult.error("无权限修改此对话");
            }

            // 更新标题
            int updateResult = conversationDao.updateTitle(conversationId, newTitle.trim(), userId, conversation.getNonce());
            if (updateResult > 0) {
                // 重新查询更新后的对话信息
                Conversation updatedConversation = conversationDao.selectById(conversationId);
                ConversationDTO dto = convertToDTO(updatedConversation);

                log.info("Successfully renamed conversation {} to: {}", conversationId, newTitle);
                return ResponseResult.success(dto);
            } else {
                log.warn("Failed to update conversation title, possibly due to concurrent modification");
                return ResponseResult.error("更新失败，请重试");
            }

        } catch (Exception e) {
            log.error("Failed to rename conversation: {}", conversationId, e);
            return ResponseResult.error("重命名对话失败: " + e.getMessage());
        }
    }

    /**
     * 更新对话标题（内部方法，用于自动标题生成）
     *
     * @param conversationId 对话ID
     * @param newTitle       新标题
     * @param updateUser     更新用户
     * @return 是否更新成功
     */
    @Transactional
    public boolean updateTitle(String conversationId, String newTitle, String updateUser) {
        try {
            if (StringUtils.isBlank(newTitle)) {
                log.warn("Cannot update conversation title with empty title");
                return false;
            }

            // 获取当前对话
            Conversation conversation = conversationDao.selectById(conversationId);
            if (conversation == null) {
                log.warn("Conversation not found: {}", conversationId);
                return false;
            }

            // 更新标题
            int updateResult = conversationDao.updateTitle(conversationId, newTitle.trim(), updateUser, conversation.getNonce());
            if (updateResult > 0) {
                log.debug("Successfully updated title for conversation {}: {}", conversationId, newTitle);
                return true;
            } else {
                log.warn("Failed to update conversation title, possibly due to concurrent modification");
                return false;
            }

        } catch (Exception e) {
            log.error("Failed to update conversation title: {}", conversationId, e);
            return false;
        }
    }

    /**
     * 转换为DTO
     */
    private ConversationDTO convertToDTO(Conversation conversation) {
        // 查询消息数量
        Integer messageCount = conversationMessageDao.countByConversationId(conversation.getId());

        // 查询最后一条消息
        ConversationMessage lastMessage = conversationMessageDao.selectLastMessageByConversationId(conversation.getId());
        String lastMessagePreview = null;
        if (lastMessage != null) {
            lastMessagePreview = lastMessage.getContent();
            if (lastMessagePreview != null && lastMessagePreview.length() > 50) {
                lastMessagePreview = lastMessagePreview.substring(0, 50) + "...";
            }
        }

        // 检查是否已生成PRD
        PRDDocument prdDocument = prdDocumentDao.selectByConversationId(conversation.getId());
        Boolean hasPRD = prdDocument != null;

        return ConversationDTO.builder()
                .id(conversation.getId())
                .userId(conversation.getUserId())
                .title(conversation.getTitle())
                .status(conversation.getStatus())
                .statusDesc(getStatusDesc(conversation.getStatus()))
                .confidenceScore(conversation.getConfidenceScore())
                .messageCount(messageCount)
                .hasPRD(hasPRD)
                .createTime(conversation.getCreateTime())
                .updateTime(conversation.getUpdateTime())
                .lastMessagePreview(lastMessagePreview)
                .build();
    }

    /**
     * 转换消息为DTO
     */
    private MessageDTO convertMessageToDTO(ConversationMessage message) {
        MessageDTO.MessageDTOBuilder builder = MessageDTO.builder()
                .id(message.getId())
                .conversationId(message.getConversationId())
                .senderType(message.getSenderType())
                .senderTypeDesc(getSenderTypeDesc(message.getSenderType()))
                .contentType(message.getContentType())
                .contentTypeDesc(getContentTypeDesc(message.getContentType()))
                .content(message.getContent())
                .createTime(message.getCreateTime());

        // 解析JSON字段
        if (StringUtils.isNotBlank(message.getFileUrls())) {
            builder.fileUrls(com.alibaba.fastjson.JSON.parseArray(message.getFileUrls(), String.class));
        }
        if (StringUtils.isNotBlank(message.getMetadata())) {
            builder.metadata(com.alibaba.fastjson.JSON.parseObject(message.getMetadata()));
        }

        return builder.build();
    }

    /**
     * 获取状态描述
     */
    private String getStatusDesc(Integer status) {
        switch (status) {
            case 1:
                return "进行中";
            case 2:
                return "已完成";
            case 3:
                return "已删除";
            default:
                return "未知";
        }
    }

    /**
     * 获取发送者类型描述
     */
    private String getSenderTypeDesc(Integer senderType) {
        switch (senderType) {
            case 1:
                return "用户";
            case 2:
                return "AI助手";
            default:
                return "未知";
        }
    }

    /**
     * 获取内容类型描述
     */
    private String getContentTypeDesc(Integer contentType) {
        switch (contentType) {
            case 1:
                return "文本";
            case 2:
                return "文件";
            case 3:
                return "图片";
            default:
                return "未知";
        }
    }

    /**
     * 异步尝试为对话生成标题
     * 仅在满足以下条件时生成：
     * 1. 当前标题为默认值（"新对话"）
     * 2. 这是第一条用户消息
     * 3. 消息内容不为空
     */
    @Async("titleGenerationExecutor")
    public void tryGenerateConversationTitleAsync(String conversationId, String userMessage, String userId) {
        try {
            log.debug("Starting async title generation for conversation: {}", conversationId);
            
            // 重新查询对话信息（避免事务问题）
            Conversation conversation = conversationDao.selectById(conversationId);
            if (conversation == null) {
                log.warn("Conversation not found for title generation: {}", conversationId);
                return;
            }

            // 检查是否需要生成标题
            if (!shouldGenerateTitle(conversation, userMessage)) {
                log.debug("Title generation not needed for conversation: {}", conversationId);
                return;
            }

            log.info("Generating title for conversation: {}", conversationId);

            // 生成新标题
            String newTitle = titleGenerationService.generateTitle(userMessage);

            if (StringUtils.isNotBlank(newTitle) && !newTitle.equals("新对话")) {
                // 更新标题
                boolean updated = updateTitle(conversationId, newTitle, userId);
                if (updated) {
                    log.info("Successfully generated and updated title for conversation {}: {}",
                            conversationId, newTitle);
                } else {
                    log.warn("Failed to update generated title for conversation: {}", conversationId);
                }
            } else {
                log.debug("Generated title is empty or default, skipping update for conversation: {}",
                        conversationId);
            }

        } catch (Exception e) {
            log.error("Failed to generate title for conversation: {}", conversationId, e);
            // 异步任务异常不影响主要流程
        }
    }

    /**
     * 判断是否应该生成标题
     * 更严格的条件检查，防止重复生成
     */
    private boolean shouldGenerateTitle(Conversation conversation, String userMessage) {
        // 检查消息内容
        if (StringUtils.isBlank(userMessage) || userMessage.trim().length() < 2) {
            log.debug("Message content too short or empty, skipping title generation");
            return false;
        }

        // 检查当前标题是否为默认值
        String currentTitle = conversation.getTitle();
        if (StringUtils.isBlank(currentTitle) || !"新对话".equals(currentTitle.trim())) {
            log.debug("Title is not default value '新对话', current: '{}', skipping generation", currentTitle);
            return false;
        }

        // 检查消息数量，确保只有第一条用户消息触发标题生成
        Integer totalMessageCount = conversationMessageDao.countByConversationId(conversation.getId());
        if (totalMessageCount == null || totalMessageCount != 1) {
            log.debug("Message count is not 1 (actual: {}), skipping title generation", totalMessageCount);
            return false;
        }

        // 双重检查：查询最新的消息，确保是用户消息
        ConversationMessage lastMessage = conversationMessageDao.selectLastMessageByConversationId(conversation.getId());
        if (lastMessage == null || lastMessage.getSenderType() == null || 
            lastMessage.getSenderType() != ConversationMessage.SENDER_TYPE_USER) {
            log.debug("Last message is not from user, skipping title generation");
            return false;
        }

        log.info("All conditions met for title generation, conversation: {}", conversation.getId());
        return true;
    }
}