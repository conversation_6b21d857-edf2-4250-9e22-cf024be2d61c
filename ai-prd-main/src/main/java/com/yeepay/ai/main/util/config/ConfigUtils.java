/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ai.main.util.config;

import com.yeepay.g3.utils.config.ConfigParam;
import com.yeepay.g3.utils.config.ConfigurationUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * title: <br>
 * description: 统一配置获取工具类<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/24 15:11
 */
@Slf4j
public class ConfigUtils {

    public static <T> T getConfigParam(ConfigKey configKey, Class<T> tClass) {
        return (T) getConfigParam(ConfigurationUtils.CONFIG_TYPE_SYS, configKey);
    }

    private static Object getConfigParam(String configType, ConfigKey configKey) {
        try {
            ConfigParam configParam = ConfigurationUtils.getConfigParam(configType, configKey.getConfigKey());
            if (null != configParam && null != configParam.getValue()) {
                return configParam.getValue();
            } else {
                return configKey.getDefaultValue();
            }
        } catch (Exception e) {
            log.warn("get config param error, configKey:{}", configKey.getConfigKey(), e);
            return configKey.getDefaultValue();
        }

    }
}