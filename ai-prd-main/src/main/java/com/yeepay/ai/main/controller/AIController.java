package com.yeepay.ai.main.controller;

import com.yeepay.ai.main.common.exection.ResponseResult;
import com.yeepay.ai.main.dto.ConversationDTO;
import com.yeepay.ai.main.dto.MessageDTO;
import com.yeepay.ai.main.dto.SendMessageRequest;
import com.yeepay.ai.main.service.AIAnalysisService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * AI分析控制器
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Api(tags = "AI分析", description = "AI需求分析、信心指数计算等相关接口")
@Slf4j
@RestController
@RequestMapping("/api/ai")
public class AIController extends BaseController {

    @Autowired
    private AIAnalysisService aiAnalysisService;
    
    @Autowired
    private com.yeepay.ai.main.service.ConversationService conversationService;

    /**
     * AI需求分析
     *
     * @param request AI分析请求
     * @return AI分析结果
     */
    @ApiOperation(value = "AI需求分析", notes = "对用户输入的内容进行AI分析，提取需求信息并返回分析结果")
    @ApiResponses({
            @ApiResponse(code = 200, message = "分析成功"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 404, message = "对话不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/analyze")
    public ResponseResult<MessageDTO> analyzeRequirement(
            @ApiParam(value = "AI分析请求参数", required = true) 
            @Valid @RequestBody AIAnalyzeRequest request) {
        try {
            log.info("AI analyzing requirement for conversation: {}", request.getConversationId());

            // 构建发送消息请求
            SendMessageRequest sendMessageRequest = new SendMessageRequest();
            sendMessageRequest.setContent(request.getContent());
            sendMessageRequest.setContentType(1); // 1-文本类型

            // 调用AI分析服务
            ResponseResult<MessageDTO> result = aiAnalysisService.analyzeUserMessage(
                    request.getConversationId(), sendMessageRequest);

            log.debug("AI analysis completed for conversation: {}, success: {}", 
                    request.getConversationId(), result.isSuccess());

            return result;

        } catch (Exception e) {
            log.error("Failed to analyze requirement for conversation: {}", 
                    request.getConversationId(), e);
            return ResponseResult.error("AI分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取对话信心指数
     *
     * @param conversationId 对话ID
     * @return 信心指数
     */
    @ApiOperation(value = "获取信心指数", notes = "获取指定对话的当前理解信心指数")
    @ApiResponses({
            @ApiResponse(code = 200, message = "获取成功"),
            @ApiResponse(code = 404, message = "对话不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/confidence/{conversationId}")
    public ResponseResult<Integer> getConfidenceScore(
            @ApiParam(value = "对话ID", required = true) 
            @PathVariable String conversationId) {
        try {
            log.debug("Getting confidence score for conversation: {}", conversationId);

            // 这里可以扩展实现获取最新的信心指数逻辑
            // 目前返回一个占位值，后续可以从数据库或缓存中获取
            // 获取对话的实际信心指数（由AI评估）
        int confidenceScore = conversationService.getConversationById(conversationId)
            .map(ConversationDTO::getConfidenceScore)
            .orElse(0); // 如果没有找到对话或没有信心值，返回0

            return ResponseResult.success(confidenceScore);

        } catch (Exception e) {
            log.error("Failed to get confidence score for conversation: {}", conversationId, e);
            return ResponseResult.error("获取信心指数失败: " + e.getMessage());
        }
    }

    /**
     * AI分析请求DTO
     */
    @ApiModel(description = "AI分析请求参数")
    public static class AIAnalyzeRequest {
        
        @ApiModelProperty(value = "对话ID", required = true, example = "1234567890")
        private String conversationId;
        
        @ApiModelProperty(value = "要分析的内容", required = true, example = "我想开发一个在线购物应用")
        private String content;

        public String getConversationId() {
            return conversationId;
        }

        public void setConversationId(String conversationId) {
            this.conversationId = conversationId;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }
} 