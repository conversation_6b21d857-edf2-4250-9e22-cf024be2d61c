package com.yeepay.ai.main.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 创建对话请求
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@ApiModel(description = "创建对话请求参数")
@Data
public class CreateConversationRequest implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 对话标题
     */
    @ApiModelProperty(value = "对话标题", required = true, example = "电商系统PRD需求讨论")
    @NotBlank(message = "对话标题不能为空")
    @Size(max = 200, message = "对话标题长度不能超过200字符")
    private String title;

    /**
     * 初始需求描述（可选）
     */
    @ApiModelProperty(value = "初始需求描述", required = false, example = "我想开发一个B2C电商平台，需要包括用户管理、商品管理、订单管理等功能")
    @Size(max = 5000, message = "初始需求描述长度不能超过5000字符")
    private String initialContent;
} 