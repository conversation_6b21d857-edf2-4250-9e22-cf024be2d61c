package com.yeepay.ai.main.client.ai;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.ai.main.util.config.ConfigEnum;
import com.yeepay.ai.main.util.config.ConfigUtils;
import com.yeepay.g3.utils.common.encrypt.AES;
import com.yeepay.g3.utils.common.encrypt.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * AI配置管理服务
 * 负责从统一配置中解析AI相关参数
 */
@Service
public class AIConfigService {
    
    private static final Logger logger = LogManager.getLogger(AIConfigService.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 获取AI配置
     * @return AI配置信息
     */
    public AIConfig getAIConfig() {
        try {
            // 从配置中获取AI配置MAP
            Object configValue = ConfigUtils.getConfigParam(ConfigEnum.AWESOME_AI_CONFIG,Map.class);
            if (configValue == null) {
                logger.warn("AI_CONFIG not found, using default configuration");
                return getDefaultConfig();
            }
            
            // 解析JSON配置
            Map<String, Object> configMap;
            if (configValue instanceof String) {
                configMap = objectMapper.readValue((String) configValue, new TypeReference<Map<String, Object>>() {});
            } else if (configValue instanceof Map) {
                configMap = (Map<String, Object>) configValue;
            } else {
                logger.warn("Invalid AI_CONFIG format, using default configuration");
                return getDefaultConfig();
            }
            
            return parseAIConfig(configMap);
            
        } catch (Exception e) {
            logger.error("Failed to parse AI configuration", e);
            return getDefaultConfig();
        }
    }
    
    /**
     * 解析AI配置MAP
     */
    private AIConfig parseAIConfig(Map<String, Object> configMap) throws Exception {
        AIConfig config = new AIConfig();
        
        // 获取提供商类型
        String provider = getStringValue(configMap, AIConstants.CONFIG_KEYS.PROVIDER, AIConstants.PROVIDERS.DEEPSEEK);
        config.setProvider(provider);
        
        // 根据提供商设置默认值
        String defaultBaseUrl;
        String defaultModel;
        int defaultMaxTokens;
        double defaultTemperature;
        double defaultTopP;
        
        switch (provider.toLowerCase()) {
            case AIConstants.PROVIDERS.GEMINI:
                defaultBaseUrl = AIConstants.Defaults.GEMINI_BASE_URL;
                defaultModel = AIConstants.Defaults.GEMINI_MODEL;
                defaultMaxTokens = AIConstants.Defaults.GEMINI_MAX_TOKENS;
                defaultTemperature = AIConstants.Defaults.GEMINI_TEMPERATURE;
                defaultTopP = AIConstants.Defaults.GEMINI_TOP_P;
                break;
            case AIConstants.PROVIDERS.OPENAI:
                defaultBaseUrl = AIConstants.Defaults.OPENAI_BASE_URL;
                defaultModel = AIConstants.Defaults.OPENAI_MODEL;
                defaultMaxTokens = AIConstants.Defaults.OPENAI_MAX_TOKENS;
                defaultTemperature = AIConstants.Defaults.OPENAI_TEMPERATURE;
                defaultTopP = AIConstants.Defaults.OPENAI_TOP_P;
                break;
            case AIConstants.PROVIDERS.DEEPSEEK:
            default:
                defaultBaseUrl = AIConstants.Defaults.DEEPSEEK_BASE_URL;
                defaultModel = AIConstants.Defaults.DEEPSEEK_MODEL;
                defaultMaxTokens = AIConstants.Defaults.DEEPSEEK_MAX_TOKENS;
                defaultTemperature = AIConstants.Defaults.DEEPSEEK_TEMPERATURE;
                defaultTopP = AIConstants.Defaults.DEEPSEEK_TOP_P;
                break;
        }
        
        // 基础配置
        config.setBaseUrl(getStringValue(configMap, AIConstants.CONFIG_KEYS.BASE_URL, defaultBaseUrl));
        config.setModel(getStringValue(configMap, AIConstants.CONFIG_KEYS.MODEL, defaultModel));
        
        // API密钥解密
        String encryptedApiKey = getStringValue(configMap, AIConstants.CONFIG_KEYS.API_KEY, null);
        if (encryptedApiKey != null && !encryptedApiKey.trim().isEmpty()) {
            try {
                String decryptedKey = decryptValue(encryptedApiKey);
                config.setApiKey(decryptedKey);
            } catch (Exception e) {
                logger.error("Failed to decrypt API key", e);
                throw new RuntimeException("Failed to decrypt API key", e);
            }
        }
        
        // 参数配置
        config.setMaxTokens(getIntValue(configMap, AIConstants.CONFIG_KEYS.MAX_TOKENS, defaultMaxTokens));
        config.setTemperature(getDoubleValue(configMap, AIConstants.CONFIG_KEYS.TEMPERATURE, defaultTemperature));
        config.setTopP(getDoubleValue(configMap, AIConstants.CONFIG_KEYS.TOP_P, defaultTopP));
        
        logger.debug("Parsed AI config: provider={}, model={}", config.getProvider(), config.getModel());
        return config;
    }

    private String decryptValue(String encryptedValue) {
        return AES.decryptWithKeyBase64(encryptedValue, Base64.encode("I am a fool, OK?"));
    }
    
    /**
     * 获取默认配置
     */
    public AIConfig getDefaultConfig() {
        AIConfig config = new AIConfig();
        config.setProvider(AIConstants.PROVIDERS.DEEPSEEK);
        config.setBaseUrl("https://api.deepseek.com/v1");
        config.setModel("deepseek-chat");
        config.setMaxTokens(8000);
        config.setTemperature(1.0);
        config.setTopP(0.95);
        // API密钥为空，需要在配置中设置
        logger.warn("Using default AI configuration, API key may not be available");
        return config;
    }
    
    private String getStringValue(Map<String, Object> map, String key, String defaultValue) {
        Object value = map.get(key);
        return value != null ? value.toString() : defaultValue;
    }
    
    private int getIntValue(Map<String, Object> map, String key, int defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                logger.warn("Invalid integer value for {}: {}, using default: {}", key, value, defaultValue);
            }
        }
        return defaultValue;
    }
    
    private double getDoubleValue(Map<String, Object> map, String key, double defaultValue) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                logger.warn("Invalid double value for {}: {}, using default: {}", key, value, defaultValue);
            }
        }
        return defaultValue;
    }
    
    /**
     * AI配置数据类
     */
    public static class AIConfig {
        private String provider;
        private String baseUrl;
        private String model;
        private String apiKey;
        private int maxTokens;
        private double temperature;
        private double topP;
        
        // Getters and Setters
        public String getProvider() { return provider; }
        public void setProvider(String provider) { this.provider = provider; }
        
        public String getBaseUrl() { return baseUrl; }
        public void setBaseUrl(String baseUrl) { this.baseUrl = baseUrl; }
        
        public String getModel() { return model; }
        public void setModel(String model) { this.model = model; }
        
        public String getApiKey() { return apiKey; }
        public void setApiKey(String apiKey) { this.apiKey = apiKey; }
        
        public int getMaxTokens() { return maxTokens; }
        public void setMaxTokens(int maxTokens) { this.maxTokens = maxTokens; }
        
        public double getTemperature() { return temperature; }
        public void setTemperature(double temperature) { this.temperature = temperature; }
        
        public double getTopP() { return topP; }
        public void setTopP(double topP) { this.topP = topP; }
    }
} 