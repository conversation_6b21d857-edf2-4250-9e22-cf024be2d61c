package com.yeepay.ai.main.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.ai.main.client.ai.AIClient;
import com.yeepay.ai.main.client.ai.AIClientFactory;
import com.yeepay.ai.main.client.ai.AIConfigService;

import com.yeepay.ai.main.client.ai.model.ChatMessage;
import com.yeepay.ai.main.client.ai.model.ChatResponse;
import com.yeepay.ai.main.common.exection.ResponseResult;
import com.yeepay.ai.main.dao.ConversationMessageDao;
import com.yeepay.ai.main.dto.ClarificationQuestionDTO;
import com.yeepay.ai.main.dto.MessageDTO;
import com.yeepay.ai.main.dto.MessageProcessingStatus;
import com.yeepay.ai.main.dto.SendMessageRequest;
import com.yeepay.ai.main.entity.ConversationMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * AI分析服务 - 核心AI处理模块
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Slf4j
@Service
public class AIAnalysisService {

    @Autowired
    private ConversationMessageDao conversationMessageDao;

    @Autowired
    private StringRedisTemplate redisTemplate;
    
    @Autowired
    private AIConfigService aiConfigService;
    
    @Autowired
    private PromptService promptService;
    
    @Autowired
    private AIClientFactory aiClientFactory;
    
    @Autowired
    @Lazy
    private ConversationService conversationService;
    
    @Autowired
    private AsyncMessageProcessingService asyncProcessingService;

    @Value("${ai.confidence.threshold:80}")
    private Integer confidenceThreshold;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 分析用户消息并生成AI回复（同步版本）
     */
    public ResponseResult<MessageDTO> analyzeUserMessage(String conversationId, SendMessageRequest request) {
        try {
            log.info("Analyzing user message for conversation: {}", conversationId);

            // 1. 获取对话历史
            List<ConversationMessage> messageHistory = getMessageHistory(conversationId);

            // 2. 进行需求分析
            AIAnalysisResult analysisResult = performRequirementAnalysis(messageHistory, request);

            // 3. 计算信心指数
            int confidenceScore = calculateConfidenceScore(analysisResult, messageHistory);

            // 4. 生成AI回复内容
            String aiReplyContent = generateAIReply(analysisResult, confidenceScore);

            // 5. 保存AI回复消息
            ConversationMessage aiMessage = new ConversationMessage();
            aiMessage.setConversationId(conversationId);
            aiMessage.setSenderType(ConversationMessage.SENDER_TYPE_AI);
            aiMessage.setContentType(ConversationMessage.CONTENT_TYPE_TEXT);
            aiMessage.setContent(aiReplyContent);
            aiMessage.setCreateTime(LocalDateTime.now());

            conversationMessageDao.insert(aiMessage);

            // 6. 更新会话的信心值到数据库
            try {
                conversationService.updateConfidenceScore(conversationId, confidenceScore);
                log.debug("Updated confidence score in database for conversation: {}, score: {}", 
                        conversationId, confidenceScore);
            } catch (Exception e) {
                log.warn("Failed to update confidence score in database for conversation: {}, score: {}", 
                        conversationId, confidenceScore, e);
                // 不影响主要流程，继续执行
            }

            // 7. 构建返回DTO
            MessageDTO responseDto = MessageDTO.builder()
                    .id(aiMessage.getId())
                    .conversationId(conversationId)
                    .senderType(ConversationMessage.SENDER_TYPE_AI)
                    .senderTypeDesc("AI助手")
                    .contentType(ConversationMessage.CONTENT_TYPE_TEXT)
                    .contentTypeDesc("文本")
                    .content(aiReplyContent)
                    .createTime(aiMessage.getCreateTime())
                    .isAnalysisResult(true)
                    .confidenceScore(confidenceScore)
                    .missingInfo(analysisResult.getMissingInfo())
                    .clarificationQuestions(convertToClarificationQuestionDTOs(analysisResult.getClarificationQuestions()))
                    .build();

            log.info("AI analysis completed for conversation: {}, confidence: {}", 
                    conversationId, confidenceScore);

            return ResponseResult.success(responseDto);

        } catch (Exception e) {
            log.error("Failed to analyze user message for conversation: {}", conversationId, e);
            return ResponseResult.error("AI分析失败: " + e.getMessage());
        }
    }

    /**
     * 异步分析用户消息并生成AI回复
     */
    @Async("aiAnalysisExecutor")
    public void analyzeUserMessageAsync(String conversationId, SendMessageRequest request, String userMessageId) {
        try {
            log.info("Starting async AI analysis for message: {}, conversation: {}", userMessageId, conversationId);
            
            // 立即更新状态确保任务已创建（防止前端轮询时找不到）
            asyncProcessingService.updateProcessingStatus(userMessageId, 
                    MessageProcessingStatus.PENDING, 5, "AI分析任务已启动，正在准备处理...");
            
            // 更新状态：开始分析
            asyncProcessingService.updateProcessingStatus(userMessageId, 
                    MessageProcessingStatus.ANALYZING, 10, "正在获取对话历史...");

            // 1. 获取对话历史
            List<ConversationMessage> messageHistory = getMessageHistory(conversationId);
            
            // 更新状态：分析需求
            asyncProcessingService.updateProcessingStatus(userMessageId, 
                    MessageProcessingStatus.ANALYZING, 30, "正在进行AI需求分析...");

            // 2. 进行需求分析
            AIAnalysisResult analysisResult = performRequirementAnalysis(messageHistory, request);

            // 更新状态：计算信心指数
            asyncProcessingService.updateProcessingStatus(userMessageId, 
                    MessageProcessingStatus.ANALYZING, 60, "正在计算信心指数...");

            // 3. 计算信心指数
            int confidenceScore = calculateConfidenceScore(analysisResult, messageHistory);

            // 更新状态：生成回复
            asyncProcessingService.updateProcessingStatus(userMessageId, 
                    MessageProcessingStatus.GENERATING, 80, "正在生成AI回复...");

            // 4. 生成AI回复内容
            String aiReplyContent = generateAIReply(analysisResult, confidenceScore);

            // 5. 保存AI回复消息
            ConversationMessage aiMessage = new ConversationMessage();
            aiMessage.setConversationId(conversationId);
            aiMessage.setSenderType(ConversationMessage.SENDER_TYPE_AI);
            aiMessage.setContentType(ConversationMessage.CONTENT_TYPE_TEXT);
            aiMessage.setContent(aiReplyContent);
            aiMessage.setCreateTime(LocalDateTime.now());

            conversationMessageDao.insert(aiMessage);

            // 6. 更新会话的信心值到数据库
            try {
                conversationService.updateConfidenceScore(conversationId, confidenceScore);
                log.debug("Updated confidence score in database for conversation: {}, score: {}", 
                        conversationId, confidenceScore);
            } catch (Exception e) {
                log.warn("Failed to update confidence score in database for conversation: {}, score: {}", 
                        conversationId, confidenceScore, e);
                // 不影响主要流程，继续执行
            }

            // 7. 构建返回DTO
            MessageDTO responseDto = MessageDTO.builder()
                    .id(aiMessage.getId())
                    .conversationId(conversationId)
                    .senderType(ConversationMessage.SENDER_TYPE_AI)
                    .senderTypeDesc("AI助手")
                    .contentType(ConversationMessage.CONTENT_TYPE_TEXT)
                    .contentTypeDesc("文本")
                    .content(aiReplyContent)
                    .createTime(aiMessage.getCreateTime())
                    .isAnalysisResult(true)
                    .confidenceScore(confidenceScore)
                    .missingInfo(analysisResult.getMissingInfo())
                    .clarificationQuestions(convertToClarificationQuestionDTOs(analysisResult.getClarificationQuestions()))
                    .build();

            // 8. 设置处理完成
            asyncProcessingService.setProcessingCompleted(userMessageId, responseDto);

            log.info("Async AI analysis completed for message: {}, conversation: {}, confidence: {}", 
                    userMessageId, conversationId, confidenceScore);

        } catch (Exception e) {
            log.error("Failed to analyze user message async for message: {}, conversation: {}", 
                    userMessageId, conversationId, e);
            
            // 设置处理失败状态
            asyncProcessingService.setProcessingFailed(userMessageId, "AI分析失败: " + e.getMessage());
        }
    }

    /**
     * 获取对话历史消息
     */
    private List<ConversationMessage> getMessageHistory(String conversationId) {
        QueryWrapper<ConversationMessage> wrapper = new QueryWrapper<>();
        wrapper.eq("conversation_id", conversationId)
                .orderByAsc("create_time");
        return conversationMessageDao.selectList(wrapper);
    }

    /**
     * 执行需求分析（真实AI分析逻辑）
     */
    private AIAnalysisResult performRequirementAnalysis(List<ConversationMessage> messageHistory, 
                                                       SendMessageRequest request) {
        try {
            // 获取AI配置和客户端  
            AIClient aiClient = aiClientFactory.createClient();
            
            // 获取系统提示词
            String systemPrompt = promptService.getRequirementAnalysisSystemPrompt();
            
            // 构建正确的messages数组：系统提示词 + 对话历史 + 当前用户输入
            List<ChatMessage> messages = buildChatMessagesForAnalysis(systemPrompt, messageHistory, request.getContent());
            
            ChatResponse response = aiClient.chat(messages);
            String aiResponseContent = extractResponseContent(response);
            
            log.debug("AI需求分析响应: {}", aiResponseContent);
            
            // 解析AI响应
            AIAnalysisResult result = parseAIAnalysisResponse(aiResponseContent);
            
            // 关闭客户端
            aiClient.close();
            
            return result;
            
        } catch (Exception e) {
            log.error("AI需求分析失败，回退到基础分析", e);
            // 回退到基础分析逻辑
            return performBasicAnalysis(messageHistory, request);
        }
    }

    /**
     * 获取AI评估的信心指数
     * 完全依赖AI自己的评估，不进行外部计算
     */
    private int calculateConfidenceScore(AIAnalysisResult analysisResult, List<ConversationMessage> messageHistory) {
        // 完全依赖AI提供的信心指数
        int aiConfidenceScore = analysisResult.getConfidenceScore();
        if (aiConfidenceScore > 0) {
            log.debug("Using AI-evaluated confidence score: {}", aiConfidenceScore);
            return aiConfidenceScore;
        }
        
        // 如果AI未提供信心值，记录警告并返回默认值
        log.warn("AI did not provide confidence score, this should not happen in normal flow");
        return 50; // 中等信心值作为默认
    }
    


    /**
     * 生成AI回复内容
     */
    private String generateAIReply(AIAnalysisResult analysisResult, int confidenceScore) {
        // 如果AI已经生成了回复内容，直接使用
        if (analysisResult.getAiReply() != null && !analysisResult.getAiReply().trim().isEmpty()) {
            return analysisResult.getAiReply() + String.format("\n\n📊 **当前理解程度：%d%%**", confidenceScore);
        }
        
        // 回退到基础回复生成逻辑
        return generateBasicReply(analysisResult, confidenceScore);
    }
    
    /**
     * 基础回复生成逻辑
     */
    private String generateBasicReply(AIAnalysisResult analysisResult, int confidenceScore) {
        StringBuilder reply = new StringBuilder();
        
        if (confidenceScore < 40) {
            reply.append("感谢您的描述！为了更好地理解您的需求，我需要了解一些关键信息：\n\n");
            reply.append(generateClarificationQuestions(analysisResult));
        } else if (confidenceScore < confidenceThreshold) {
            reply.append("很好！我已经理解了您需求的主要部分。让我们进一步完善一些细节：\n\n");
            reply.append(generateDetailQuestions(analysisResult));
        } else {
            reply.append("太棒了！基于我们的对话，我已经充分理解了您的产品需求。\n\n");
            reply.append("**需求理解总结：**\n");
            reply.append(generateRequirementSummary(analysisResult));
            reply.append("\n\n🎉 **需求信息已充分，我将为您生成PRD草稿！**");
        }
        
        reply.append(String.format("\n\n📊 **当前理解程度：%d%%**", confidenceScore));
        
        return reply.toString();
    }

    /**
     * 构建需求分析的ChatMessage数组
     * 格式：[系统提示词, 历史对话消息..., 当前用户输入]
     */
    private List<ChatMessage> buildChatMessagesForAnalysis(String systemPrompt, List<ConversationMessage> messageHistory, String currentUserInput) {
        List<ChatMessage> chatMessages = new ArrayList<>();
        
        // 1. 添加系统提示词
        chatMessages.add(ChatMessage.system(systemPrompt));
        
        // 2. 添加对话历史消息
        for (ConversationMessage message : messageHistory) {
            if (Integer.valueOf(ConversationMessage.SENDER_TYPE_USER).equals(message.getSenderType())) {
                chatMessages.add(ChatMessage.user(message.getContent()));
            } else {
                chatMessages.add(ChatMessage.assistant(message.getContent()));
            }
        }
        
        // 3. 添加当前用户输入
        chatMessages.add(ChatMessage.user(currentUserInput));
        
        return chatMessages;
    }
    
    /**
     * 提取AI响应内容
     */
    private String extractResponseContent(ChatResponse response) {
        if (response.getChoices() != null && !response.getChoices().isEmpty()) {
            ChatResponse.Choice choice = response.getChoices().get(0);
            if (choice.getMessage() != null && choice.getMessage().getContent() != null) {
                return choice.getMessage().getContent();
            }
        }
        throw new RuntimeException("AI响应格式异常");
    }
    
    /**
     * 解析AI分析响应
     */
    private AIAnalysisResult parseAIAnalysisResponse(String aiResponse) {
        try {
            // 尝试从JSON中解析
            if (aiResponse.contains("{") && aiResponse.contains("}")) {
                // 提取JSON部分
                int startIndex = aiResponse.indexOf("{");
                int endIndex = aiResponse.lastIndexOf("}") + 1;
                String jsonPart = aiResponse.substring(startIndex, endIndex);
                
                // 解析JSON响应
                com.fasterxml.jackson.databind.JsonNode jsonNode = objectMapper.readTree(jsonPart);
                
                AIAnalysisResult result = new AIAnalysisResult();
                result.setProductBackground(getStringFromJson(jsonNode, "product_background"));
                result.setTargetUsers(getStringFromJson(jsonNode, "target_users"));
                result.setCoreFeatures(getListFromJson(jsonNode, "core_features"));
                result.setBusinessFlow(getStringFromJson(jsonNode, "business_flow"));
                result.setMissingInfo(getListFromJson(jsonNode, "missing_info"));
                result.setClarificationQuestions(getClarificationQuestionsFromJson(jsonNode, "clarification_questions"));
                // AI必须提供信心值，如果没有则记录警告
                int confidenceFromAI = getIntFromJson(jsonNode, "confidence_score", -1);
                if (confidenceFromAI == -1) {
                    log.warn("AI response missing confidence_score field, this indicates AI prompt may need adjustment");
                    result.setConfidenceScore(50); // 默认中等信心值
                } else {
                    result.setConfidenceScore(confidenceFromAI);
                    log.debug("AI provided confidence score: {}", confidenceFromAI);
                }
                result.setAiReply(getStringFromJson(jsonNode, "ai_reply"));
                
                return result;
            }
        } catch (Exception e) {
            log.warn("解析AI JSON响应失败，使用文本解析: {}", e.getMessage());
        }
        
        // 回退到基础文本解析
        return parseAIResponseAsText(aiResponse);
    }
    
    /**
     * 基础分析逻辑（作为AI分析的回退方案）
     * 注意：这里的信心值设为较低值，因为没有AI智能评估
     */
    private AIAnalysisResult performBasicAnalysis(List<ConversationMessage> messageHistory, SendMessageRequest request) {
        AIAnalysisResult result = new AIAnalysisResult();
        String content = request.getContent();
        
        // 基础信息提取
        result.setProductBackground(extractProductBackground(content));
        result.setTargetUsers(extractTargetUsers(content));
        result.setCoreFeatures(extractCoreFeatures(content));
        result.setBusinessFlow(extractBusinessFlow(content));
        // 回退方案的信心值设为较低，因为没有AI智能评估
        result.setConfidenceScore(30);
        result.setAiReply("系统分析生成的回复内容（AI服务暂不可用）");
        
        log.warn("Using basic analysis fallback, confidence score set to low value due to lack of AI evaluation");
        return result;
    }
    
    // 辅助方法
    private String getStringFromJson(com.fasterxml.jackson.databind.JsonNode jsonNode, String fieldName) {
        return jsonNode.has(fieldName) ? jsonNode.get(fieldName).asText() : null;
    }
    
    private List<String> getListFromJson(com.fasterxml.jackson.databind.JsonNode jsonNode, String fieldName) {
        List<String> result = new ArrayList<>();
        if (jsonNode.has(fieldName) && jsonNode.get(fieldName).isArray()) {
            jsonNode.get(fieldName).forEach(item -> result.add(item.asText()));
        }
        return result;
    }
    
    private int getIntFromJson(com.fasterxml.jackson.databind.JsonNode jsonNode, String fieldName, int defaultValue) {
        return jsonNode.has(fieldName) ? jsonNode.get(fieldName).asInt(defaultValue) : defaultValue;
    }
    
    private List<ClarificationQuestion> getClarificationQuestionsFromJson(com.fasterxml.jackson.databind.JsonNode jsonNode, String fieldName) {
        List<ClarificationQuestion> result = new ArrayList<>();
        if (jsonNode.has(fieldName) && jsonNode.get(fieldName).isArray()) {
            jsonNode.get(fieldName).forEach(questionNode -> {
                String question = questionNode.has("question") ? questionNode.get("question").asText() : null;
                List<String> options = new ArrayList<>();
                if (questionNode.has("options") && questionNode.get("options").isArray()) {
                    questionNode.get("options").forEach(option -> options.add(option.asText()));
                }
                if (question != null && !options.isEmpty()) {
                    result.add(new ClarificationQuestion(question, options));
                }
            });
        }
        return result;
    }
    
    /**
     * 转换澄清问题到DTO格式
     */
    private List<ClarificationQuestionDTO> convertToClarificationQuestionDTOs(List<ClarificationQuestion> clarificationQuestions) {
        if (clarificationQuestions == null || clarificationQuestions.isEmpty()) {
            return null;
        }
        
        return clarificationQuestions.stream()
                .map(q -> ClarificationQuestionDTO.builder()
                        .question(q.getQuestion())
                        .options(q.getOptions())
                        .build())
                .collect(Collectors.toList());
    }
    
    /**
     * 基础文本解析（当AI没有返回标准JSON格式时的回退方案）
     */
    private AIAnalysisResult parseAIResponseAsText(String aiResponse) {
        AIAnalysisResult result = new AIAnalysisResult();
        result.setAiReply(aiResponse);
        // 因为无法从非JSON响应中获取AI评估的信心值，设为较低值
        result.setConfidenceScore(40);
        log.warn("AI response not in expected JSON format, confidence score set to default low value");
        return result;
    }
    
    // 基础信息提取方法
    private String extractProductBackground(String content) {
        if (content.contains("产品") || content.contains("应用") || content.contains("系统")) {
            return "基于用户描述的产品概念";
        }
        return null;
    }

    private String extractTargetUsers(String content) {
        if (content.contains("用户") || content.contains("客户") || content.contains("人群")) {
            return "基于用户描述的目标用户群体";
        }
        return null;
    }

    private List<String> extractCoreFeatures(String content) {
        List<String> features = new ArrayList<>();
        if (content.contains("功能") || content.contains("特性")) {
            features.add("功能1");
            features.add("功能2");
        }
        return features;
    }

    private String extractBusinessFlow(String content) {
        if (content.contains("流程") || content.contains("步骤")) {
            return "基于用户描述的业务流程";
        }
        return null;
    }

    private String generateClarificationQuestions(AIAnalysisResult analysisResult) {
        StringBuilder questions = new StringBuilder();
        
        if (analysisResult.getProductBackground() == null || analysisResult.getProductBackground().isEmpty()) {
            questions.append("1. 您的产品要解决什么具体问题？目标场景是什么？\n");
        }
        
        if (analysisResult.getTargetUsers() == null || analysisResult.getTargetUsers().isEmpty()) {
            questions.append("2. 您的目标用户群体是谁？他们有什么特征？\n");
        }
        
        if (analysisResult.getCoreFeatures() == null || analysisResult.getCoreFeatures().isEmpty()) {
            questions.append("3. 您希望产品具备哪些核心功能？请列举2-3个最重要的功能。\n");
        }
        
        return questions.toString();
    }

    private String generateDetailQuestions(AIAnalysisResult analysisResult) {
        return "1. 在核心功能方面，您希望优先实现哪个功能？\n" +
               "2. 您预期的用户使用流程是怎样的？\n" +
               "3. 有什么特殊的性能或安全要求吗？";
    }

    private String generateRequirementSummary(AIAnalysisResult analysisResult) {
        StringBuilder summary = new StringBuilder();
        
        if (analysisResult.getProductBackground() != null) {
            summary.append("- **产品背景**：").append(analysisResult.getProductBackground()).append("\n");
        }
        
        if (analysisResult.getTargetUsers() != null) {
            summary.append("- **目标用户**：").append(analysisResult.getTargetUsers()).append("\n");
        }
        
        if (analysisResult.getCoreFeatures() != null && !analysisResult.getCoreFeatures().isEmpty()) {
            summary.append("- **核心功能**：").append(String.join("、", analysisResult.getCoreFeatures())).append("\n");
        }
        
        return summary.toString();
    }

    /**
     * AI分析结果内部类
     */
    public static class AIAnalysisResult {
        private String productBackground;
        private String targetUsers;
        private List<String> coreFeatures;
        private String businessFlow;
        private int confidenceScore;
        private String aiReply;
        private List<String> missingInfo;
        private List<ClarificationQuestion> clarificationQuestions;

        public String getProductBackground() { return productBackground; }
        public void setProductBackground(String productBackground) { this.productBackground = productBackground; }

        public String getTargetUsers() { return targetUsers; }
        public void setTargetUsers(String targetUsers) { this.targetUsers = targetUsers; }

        public List<String> getCoreFeatures() { return coreFeatures; }
        public void setCoreFeatures(List<String> coreFeatures) { this.coreFeatures = coreFeatures; }

        public String getBusinessFlow() { return businessFlow; }
        public void setBusinessFlow(String businessFlow) { this.businessFlow = businessFlow; }
        
        public int getConfidenceScore() { return confidenceScore; }
        public void setConfidenceScore(int confidenceScore) { this.confidenceScore = confidenceScore; }
        
        public String getAiReply() { return aiReply; }
        public void setAiReply(String aiReply) { this.aiReply = aiReply; }
        
        public List<String> getMissingInfo() { return missingInfo; }
        public void setMissingInfo(List<String> missingInfo) { this.missingInfo = missingInfo; }
        
        public List<ClarificationQuestion> getClarificationQuestions() { return clarificationQuestions; }
        public void setClarificationQuestions(List<ClarificationQuestion> clarificationQuestions) { this.clarificationQuestions = clarificationQuestions; }
    }
    
    /**
     * 澄清问题内部类
     */
    public static class ClarificationQuestion {
        private String question;
        private List<String> options;
        
        public ClarificationQuestion() {}
        
        public ClarificationQuestion(String question, List<String> options) {
            this.question = question;
            this.options = options;
        }
        
        public String getQuestion() { return question; }
        public void setQuestion(String question) { this.question = question; }
        
        public List<String> getOptions() { return options; }
        public void setOptions(List<String> options) { this.options = options; }
    }
} 