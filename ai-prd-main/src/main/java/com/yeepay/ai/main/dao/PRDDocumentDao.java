package com.yeepay.ai.main.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yeepay.ai.main.entity.PRDDocument;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * PRD文档DAO接口
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Mapper
public interface PRDDocumentDao extends BaseMapper<PRDDocument> {

    /**
     * 根据对话ID查询PRD文档
     *
     * @param conversationId 对话ID
     * @return PRD文档
     */
    PRDDocument selectByConversationId(@Param("conversationId") String conversationId);

    /**
     * 根据分享Token查询PRD文档
     *
     * @param shareToken 分享Token
     * @return PRD文档
     */
    PRDDocument selectByShareToken(@Param("shareToken") String shareToken);

    /**
     * 根据用户ID查询PRD文档数量
     *
     * @param userId 用户ID
     * @return 文档数量
     */
    Integer countByUserId(@Param("userId") String userId);
} 