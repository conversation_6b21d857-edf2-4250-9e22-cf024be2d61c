package com.yeepay.ai.main.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 提示词服务
 * 负责加载和管理AI提示词模板
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
@Slf4j
@Service
public class PromptService {
    
    private static final String PROMPT_BASE_PATH = "prompts/";
    private final Map<String, String> promptCache = new ConcurrentHashMap<>();
    
    /**
     * 获取需求分析系统提示词
     */
    public String getRequirementAnalysisSystemPrompt() {
        return loadPrompt("requirement_analysis.txt");
    }
    
    /**
     * 获取PRD生成系统提示词
     */
    public String getPRDGenerationSystemPrompt() {
        return loadPrompt("prd_generation.txt");
    }

    /**
     * 获取标题生成系统提示词
     */
    public String getTitleGenerationSystemPrompt() {
        return loadPrompt("title_generation.txt");
    }

    /**
     * 获取架构设计系统提示词
     */
    public String getArchitectureDesignSystemPrompt() {
        return loadPrompt("architecture_design.txt");
    }
    
    /**
     * 加载提示词模板
     */
    private String loadPrompt(String fileName) {
        // 先从缓存中获取
        if (promptCache.containsKey(fileName)) {
            return promptCache.get(fileName);
        }
        
        try {
            ClassPathResource resource = new ClassPathResource(PROMPT_BASE_PATH + fileName);
            String content = readInputStream(resource.getInputStream());
            
            // 缓存提示词
            promptCache.put(fileName, content);
            log.debug("已加载提示词模板: {}", fileName);
            
            return content;
            
        } catch (IOException e) {
            log.error("加载提示词模板失败: {}", fileName, e);
            throw new RuntimeException("提示词模板加载失败: " + fileName, e);
        }
    }
    
    /**
     * 清除提示词缓存
     */
    public void clearCache() {
        promptCache.clear();
        log.info("提示词缓存已清空");
    }
    
    /**
     * 重新加载指定提示词
     */
    public void reloadPrompt(String fileName) {
        promptCache.remove(fileName);
        loadPrompt(fileName);
        log.info("已重新加载提示词: {}", fileName);
    }
    
    /**
     * 读取InputStream内容（Java 8兼容）
     */
    private String readInputStream(InputStream inputStream) throws IOException {
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        while ((length = inputStream.read(buffer)) != -1) {
            result.write(buffer, 0, length);
        }
        return result.toString(StandardCharsets.UTF_8.name());
    }
} 