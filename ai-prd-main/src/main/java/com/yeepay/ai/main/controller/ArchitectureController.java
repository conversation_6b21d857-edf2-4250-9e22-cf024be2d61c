package com.yeepay.ai.main.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.ai.main.common.exection.ResponseResult;
import com.yeepay.ai.main.dto.ArchitectureDocumentDTO;
import com.yeepay.ai.main.service.ArchitectureDesignService;
import com.yeepay.ai.main.util.EncodingUtils;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 架构设计控制器
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Api(tags = "架构设计管理", description = "架构设计文档生成、查看、编辑、分享、导出等相关接口")
@Slf4j
@RestController
@RequestMapping("/api/architecture")
public class ArchitectureController extends BaseController {

    @Autowired
    private ArchitectureDesignService architectureDesignService;

    private final ExecutorService executor = Executors.newCachedThreadPool();

    /**
     * 流式生成架构设计文档
     *
     * @param prdId PRD文档ID
     * @return SSE流式响应
     */
    @ApiOperation(value = "流式生成架构设计文档", notes = "基于PRD文档内容流式生成系统架构设计，实时显示生成进度")
    @GetMapping(value = "/stream/generate/{prdId}", produces = "text/event-stream; charset=utf-8")
    public SseEmitter streamGenerateArchitectureDesign(
            @ApiParam(value = "PRD文档ID", required = true) @PathVariable String prdId,
            @ApiParam(value = "用户ID", required = false) @RequestParam(value = "userId", required = false) String userIdParam,
            HttpServletResponse response) {
        
        // 设置响应编码和头部
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Type", "text/event-stream; charset=utf-8");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Headers", "Cache-Control");
        
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        
        // 优先使用URL参数中的userId，如果没有则尝试从认证信息获取
        final String userId = getUserId(userIdParam);
        
        log.debug("Using userId: {}", userId);
        
        final String finalPrdId = prdId;
        log.info("User {} starting stream architecture design generation for prd: {}", userId, finalPrdId);

        executor.execute(() -> {
            try {
                // 发送开始事件
                Map<String, Object> startData = new HashMap<>();
                startData.put("message", EncodingUtils.ensureUtf8("开始生成架构设计文档..."));
                startData.put("progress", 0);
                startData.put("timestamp", System.currentTimeMillis());
                
                ObjectMapper objectMapper = new ObjectMapper();
                emitter.send(SseEmitter.event()
                    .name("start")
                    .data(objectMapper.writeValueAsString(startData))
                );

                // 从PRD ID获取对话ID
                String conversationId = getConversationIdFromPrdId(finalPrdId);

                architectureDesignService.generateArchitectureDesignStream(conversationId, finalPrdId, userId,
                    // 进度回调
                    (progress, message) -> {
                        try {
                            Map<String, Object> progressData = new HashMap<>();
                            progressData.put("progress", progress);
                            progressData.put("message", EncodingUtils.ensureUtf8(message));
                            progressData.put("timestamp", System.currentTimeMillis());

                            emitter.send(SseEmitter.event()
                                .name("progress")
                                .data(objectMapper.writeValueAsString(progressData))
                            );
                        } catch (Exception e) {
                            log.warn("Debug - Failed to send progress event, error: {}", e.getMessage());
                        }
                    },
                    // 内容回调
                    (content) -> {
                        try {
                            // 发送内容事件 - 确保UTF-8编码正确处理
                            Map<String, Object> contentData = new HashMap<>();
                            contentData.put("content", EncodingUtils.cleanAndEnsureUtf8(content));
                            contentData.put("timestamp", System.currentTimeMillis());
                            
                            String jsonData = objectMapper.writeValueAsString(contentData);
                            
                            emitter.send(SseEmitter.event()
                                .name("content")
                                .data(jsonData)
                            );
                        } catch (Exception e) {
                            log.warn("Debug - Failed to send content chunk, error: {}", e.getMessage());
                        }
                    },
                    // 完成回调
                    (architectureDocument) -> {
                        try {
                            Map<String, Object> completeData = new HashMap<>();
                            completeData.put("architectureId", architectureDocument.getId());
                            completeData.put("message", EncodingUtils.ensureUtf8("架构设计文档生成完成"));
                            completeData.put("timestamp", System.currentTimeMillis());
                            
                            emitter.send(SseEmitter.event()
                                .name("complete")
                                .data(objectMapper.writeValueAsString(completeData))
                            );
                            emitter.complete();
                            log.info("Debug - Stream completed successfully for prd: {}", finalPrdId);
                        } catch (Exception e) {
                            log.warn("Debug - Failed to send complete event, error: {}", e.getMessage());
                            emitter.completeWithError(e);
                        }
                    },
                    // 错误回调
                    (error) -> {
                        try {
                            Map<String, Object> errorData = new HashMap<>();
                            errorData.put("error", EncodingUtils.ensureUtf8(error.getMessage()));
                            errorData.put("message", EncodingUtils.ensureUtf8("架构设计生成失败"));
                            errorData.put("timestamp", System.currentTimeMillis());
                            
                            emitter.send(SseEmitter.event()
                                .name("error")
                                .data(objectMapper.writeValueAsString(errorData))
                            );
                            emitter.completeWithError(error);
                            log.error("Debug - Stream failed for prd: {}, error: {}", finalPrdId, error.getMessage());
                        } catch (Exception e) {
                            log.error("Debug - Failed to send error event", e);
                            emitter.completeWithError(error);
                        }
                    }
                );

            } catch (Exception e) {
                log.error("Stream architecture design generation failed for prd: {}", finalPrdId, e);
                try {
                    Map<String, Object> errorData = new HashMap<>();
                    errorData.put("error", EncodingUtils.ensureUtf8("架构设计生成失败: " + e.getMessage()));
                    errorData.put("message", EncodingUtils.ensureUtf8("架构设计生成失败"));
                    errorData.put("timestamp", System.currentTimeMillis());
                    
                    ObjectMapper objectMapper = new ObjectMapper();
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data(objectMapper.writeValueAsString(errorData)));
                } catch (Exception sendError) {
                    log.error("Failed to send error event", sendError);
                }
                emitter.completeWithError(e);
            }
        });

        emitter.onCompletion(() -> {
            log.debug("Stream architecture design generation completed for prd: {}", finalPrdId);
        });

        emitter.onTimeout(() -> {
            log.warn("Stream architecture design generation timed out for prd: {}", finalPrdId);
            emitter.complete();
        });

        emitter.onError((ex) -> {
            log.error("Stream architecture design generation error for prd: {}", finalPrdId, ex);
        });

        return emitter;
    }

    /**
     * 根据PRD ID获取架构设计文档
     *
     * @param prdId PRD文档ID
     * @return 架构设计文档
     */
    @ApiOperation(value = "获取架构设计文档", notes = "根据PRD ID获取对应的架构设计文档")
    @GetMapping("/prd/{prdId}")
    public ResponseResult<ArchitectureDocumentDTO> getArchitectureDocumentByPrdId(@PathVariable String prdId) {
        try {
            String userId = getCurrentUserId();
            return architectureDesignService.getArchitectureDocumentByPrdId(prdId, userId);
        } catch (Exception e) {
            log.error("Failed to get architecture document for prd: {}", prdId, e);
            return ResponseResult.error("获取架构设计文档失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户ID（优先使用URL参数，否则从认证信息获取）
     */
    private String getUserId(String userIdParam) {
        if (StringUtils.hasText(userIdParam)) {
            log.debug("Using userId from URL parameter: {}", userIdParam);
            return userIdParam;
        }
        
        try {
            String userId = getCurrentUserId();
            log.debug("Using userId from authentication: {}", userId);
            return userId;
        } catch (Exception e) {
            String defaultUserId = "web-user-" + System.currentTimeMillis();
            log.warn("Failed to get userId from authentication, using default: {}", defaultUserId);
            return defaultUserId;
        }
    }

    @Autowired
    private com.yeepay.ai.main.dao.PRDDocumentDao prdDocumentDao;

    /**
     * 从PRD ID获取对话ID
     * 这里需要调用PRD服务来获取对话ID
     */
    private String getConversationIdFromPrdId(String prdId) {
        try {
            com.yeepay.ai.main.entity.PRDDocument prdDocument = prdDocumentDao.selectById(prdId);
            if (prdDocument != null) {
                return prdDocument.getConversationId();
            }
            throw new RuntimeException("PRD文档不存在: " + prdId);
        } catch (Exception e) {
            log.error("Failed to get conversation ID from PRD ID: {}", prdId, e);
            throw new RuntimeException("获取对话ID失败: " + e.getMessage());
        }
    }

    /**
     * 手动重新生成架构设计文档
     *
     * @param prdId PRD文档ID
     * @param userIdParam 用户ID
     * @return SSE流式响应
     */
    @ApiOperation(value = "重新生成架构设计文档", notes = "基于最新的PRD文档重新生成系统架构设计，创建新版本")
    @PostMapping(value = "/regenerate/{prdId}", produces = "text/event-stream; charset=utf-8")
    public SseEmitter regenerateArchitectureDesign(
            @ApiParam(value = "PRD文档ID", required = true) @PathVariable String prdId,
            @ApiParam(value = "用户ID", required = false) @RequestParam(value = "userId", required = false) String userIdParam,
            HttpServletResponse response) {

        // 设置响应编码和头部
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Type", "text/event-stream; charset=utf-8");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Headers", "Cache-Control");

        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);

        final String userId = getUserId(userIdParam);
        log.info("User {} manually regenerating architecture design for prd: {}", userId, prdId);

        executor.execute(() -> {
            try {
                // 发送开始事件
                Map<String, Object> startData = new HashMap<>();
                startData.put("message", EncodingUtils.ensureUtf8("开始重新生成架构设计文档..."));
                startData.put("progress", 0);
                startData.put("timestamp", System.currentTimeMillis());

                ObjectMapper objectMapper = new ObjectMapper();
                emitter.send(SseEmitter.event()
                        .name("start")
                        .data(objectMapper.writeValueAsString(startData))
                );

                architectureDesignService.regenerateArchitectureDesignStream(prdId, userId,
                        com.yeepay.ai.main.entity.ArchitectureDocument.TRIGGER_MANUAL_REGENERATE,
                        // 进度回调
                        (progress, message) -> {
                            try {
                                Map<String, Object> progressData = new HashMap<>();
                                progressData.put("progress", progress);
                                progressData.put("message", EncodingUtils.ensureUtf8(message));
                                progressData.put("timestamp", System.currentTimeMillis());

                                emitter.send(SseEmitter.event()
                                        .name("progress")
                                        .data(objectMapper.writeValueAsString(progressData))
                                );
                            } catch (Exception e) {
                                log.warn("Debug - Failed to send progress event, error: {}", e.getMessage());
                            }
                        },
                        // 内容回调
                        (content) -> {
                            try {
                                Map<String, Object> contentData = new HashMap<>();
                                contentData.put("content", EncodingUtils.cleanAndEnsureUtf8(content));
                                contentData.put("timestamp", System.currentTimeMillis());

                                String jsonData = objectMapper.writeValueAsString(contentData);

                                emitter.send(SseEmitter.event()
                                        .name("content")
                                        .data(jsonData)
                                );
                            } catch (Exception e) {
                                log.warn("Debug - Failed to send content chunk, error: {}", e.getMessage());
                            }
                        },
                        // 完成回调
                        (architectureDTO) -> {
                            try {
                                Map<String, Object> completeData = new HashMap<>();
                                completeData.put("message", EncodingUtils.ensureUtf8("架构设计文档重新生成完成"));
                                completeData.put("progress", 100);
                                completeData.put("architectureDocument", architectureDTO);
                                completeData.put("timestamp", System.currentTimeMillis());

                                emitter.send(SseEmitter.event()
                                        .name("complete")
                                        .data(objectMapper.writeValueAsString(completeData))
                                );
                                emitter.complete();
                            } catch (Exception e) {
                                log.warn("Debug - Failed to send complete event, error: {}", e.getMessage());
                                emitter.completeWithError(e);
                            }
                        },
                        // 错误回调
                        (error) -> {
                            try {
                                Map<String, Object> errorData = new HashMap<>();
                                errorData.put("message", EncodingUtils.ensureUtf8("重新生成失败: " + error.getMessage()));
                                errorData.put("timestamp", System.currentTimeMillis());

                                emitter.send(SseEmitter.event()
                                        .name("error")
                                        .data(objectMapper.writeValueAsString(errorData))
                                );
                                emitter.completeWithError(error);
                            } catch (Exception e) {
                                log.warn("Debug - Failed to send error event, error: {}", e.getMessage());
                                emitter.completeWithError(error);
                            }
                        }
                );

            } catch (Exception e) {
                log.error("Failed to start regenerate architecture design for prd: {}", prdId, e);
                try {
                    Map<String, Object> errorData = new HashMap<>();
                    errorData.put("message", EncodingUtils.ensureUtf8("启动重新生成失败: " + e.getMessage()));
                    errorData.put("timestamp", System.currentTimeMillis());

                    ObjectMapper objectMapper = new ObjectMapper();
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data(objectMapper.writeValueAsString(errorData))
                    );
                } catch (Exception ex) {
                    log.warn("Debug - Failed to send startup error event, error: {}", ex.getMessage());
                }
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }

    /**
     * 检查是否需要重新生成架构文档
     *
     * @param prdId PRD文档ID
     * @return 检查结果
     */
    @ApiOperation(value = "检查是否需要重新生成", notes = "检查PRD文档是否有更新，是否需要重新生成架构设计")
    @GetMapping("/check-regenerate/{prdId}")
    public ResponseResult<Map<String, Object>> checkNeedRegenerate(@PathVariable String prdId) {
        try {
            boolean shouldRegenerate = architectureDesignService.shouldRegenerateForPrdUpdate(prdId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("shouldRegenerate", shouldRegenerate);
            result.put("message", shouldRegenerate ? "PRD文档有更新，建议重新生成架构设计" : "架构设计文档是最新的");
            result.put("prdId", prdId);
            
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("Failed to check regenerate need for prd: {}", prdId, e);
            return ResponseResult.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取PRD对应的所有版本架构文档
     *
     * @param prdId PRD文档ID
     * @return 所有版本架构文档列表
     */
    @ApiOperation(value = "获取所有版本架构文档", notes = "获取PRD对应的所有版本架构设计文档")
    @GetMapping("/versions/{prdId}")
    public ResponseResult<java.util.List<ArchitectureDocumentDTO>> getAllVersions(@PathVariable String prdId) {
        String userId = getUserId(null);
        log.debug("User {} retrieving all architecture versions for prd: {}", userId, prdId);
        return architectureDesignService.getAllVersionsByPrdId(prdId, userId);
    }
} 