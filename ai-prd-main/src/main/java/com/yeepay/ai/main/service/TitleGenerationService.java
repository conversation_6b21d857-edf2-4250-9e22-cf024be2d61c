package com.yeepay.ai.main.service;

import com.yeepay.ai.main.client.ai.AIClient;
import com.yeepay.ai.main.client.ai.AIClientFactory;
import com.yeepay.ai.main.client.ai.model.ChatMessage;
import com.yeepay.ai.main.client.ai.model.ChatResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 对话标题生成服务
 * 基于用户输入的内容自动生成简洁、描述性的对话标题
 * 
 * <AUTHOR> PRD Team
 * @since 2024-12-29
 */
@Slf4j
@Service
public class TitleGenerationService {

    @Autowired
    private AIClientFactory aiClientFactory;

    @Autowired
    private PromptService promptService;

    /**
     * 基于用户消息内容生成对话标题
     * 
     * @param userMessage 用户输入的消息内容
     * @return 生成的标题，如果生成失败则返回基于规则的回退标题
     */
    public String generateTitle(String userMessage) {
        if (!StringUtils.hasText(userMessage)) {
            return "新对话";
        }

        try {
            log.info("Generating title for user message: {}", userMessage.substring(0, Math.min(50, userMessage.length())));
            
            // 首先尝试AI生成标题
            String aiGeneratedTitle = generateTitleWithAI(userMessage);
            if (StringUtils.hasText(aiGeneratedTitle)) {
                String cleanedTitle = cleanAndValidateTitle(aiGeneratedTitle);
                if (StringUtils.hasText(cleanedTitle)) {
                    log.info("AI generated title: {}", cleanedTitle);
                    return cleanedTitle;
                }
            }
            
            // AI生成失败，使用规则回退
            String fallbackTitle = generateTitleWithRules(userMessage);
            log.info("Using fallback title: {}", fallbackTitle);
            return fallbackTitle;
            
        } catch (Exception e) {
            log.error("Failed to generate title for message", e);
            return generateTitleWithRules(userMessage);
        }
    }

    /**
     * 使用AI生成标题
     */
    private String generateTitleWithAI(String userMessage) {
        try {
            AIClient aiClient = aiClientFactory.createClient();
            
            // 获取标题生成提示词
            String systemPrompt = promptService.getTitleGenerationSystemPrompt();
            
            // 构建消息
            List<ChatMessage> messages = new ArrayList<>();
            messages.add(new ChatMessage("system", systemPrompt));
            messages.add(new ChatMessage("user", userMessage));
            
            ChatResponse response = aiClient.chat(messages);
            String aiResponse = extractResponseContent(response);
            
            aiClient.close();
            
            return aiResponse;
            
        } catch (Exception e) {
            log.warn("AI title generation failed", e);
            return null;
        }
    }

    /**
     * 使用规则生成标题（回退机制）
     */
    private String generateTitleWithRules(String userMessage) {
        if (!StringUtils.hasText(userMessage)) {
            return "新对话";
        }

        // 清理消息内容
        String cleanMessage = userMessage.trim();
        
        // 提取关键词的规则
        String title = extractKeywordsFromMessage(cleanMessage);
        
        // 确保标题长度合适
        if (title.length() > 20) {
            title = title.substring(0, 20);
        }
        
        // 如果提取不到有意义的标题，使用默认规则
        if (title.length() < 2) {
            return "产品需求讨论";
        }
        
        return title;
    }

    /**
     * 从消息中提取关键词生成标题
     */
    private String extractKeywordsFromMessage(String message) {
        // 常见的产品类型关键词
        String[] productKeywords = {
            "APP", "应用", "系统", "平台", "网站", "小程序", "软件", "工具",
            "电商", "社交", "教育", "医疗", "金融", "游戏", "直播", "短视频",
            "管理", "CRM", "ERP", "OA", "BI", "数据", "分析", "监控",
            "支付", "钱包", "理财", "保险", "借贷", "投资",
            "外卖", "打车", "旅游", "酒店", "票务", "预订",
            "购物", "商城", "超市", "零售", "批发", "供应链",
            "直播", "视频", "音乐", "阅读", "新闻", "资讯",
            "健身", "运动", "美食", "菜谱", "宠物", "母婴"
        };

        // 功能关键词
        String[] functionKeywords = {
            "管理", "系统", "平台", "服务", "功能", "模块", "组件",
            "注册", "登录", "认证", "权限", "用户", "会员",
            "订单", "支付", "结算", "发货", "物流", "退款",
            "搜索", "推荐", "评价", "评论", "分享", "收藏",
            "消息", "通知", "提醒", "聊天", "客服", "反馈"
        };

        // 尝试匹配产品类型
        for (String keyword : productKeywords) {
            if (message.contains(keyword)) {
                // 尝试提取更完整的描述
                String context = extractContext(message, keyword);
                if (StringUtils.hasText(context)) {
                    return context;
                }
                return keyword + "需求";
            }
        }

        // 尝试匹配功能关键词
        for (String keyword : functionKeywords) {
            if (message.contains(keyword)) {
                return keyword + "功能";
            }
        }

        // 如果包含"我想"、"需要"等表达，尝试提取后面的内容
        Pattern intentPattern = Pattern.compile("(我想|需要|希望|计划|打算)(.{1,15})");
        Matcher matcher = intentPattern.matcher(message);
        if (matcher.find()) {
            String intent = matcher.group(2).trim();
            if (intent.length() > 1) {
                return intent;
            }
        }

        // 提取前15个字符作为标题
        if (message.length() > 15) {
            return message.substring(0, 15);
        }
        
        return message;
    }

    /**
     * 提取关键词周围的上下文
     */
    private String extractContext(String message, String keyword) {
        int index = message.indexOf(keyword);
        if (index == -1) {
            return null;
        }

        // 提取关键词前后的内容
        int start = Math.max(0, index - 5);
        int end = Math.min(message.length(), index + keyword.length() + 10);
        
        String context = message.substring(start, end).trim();
        
        // 清理上下文
        context = context.replaceAll("[，。！？；：、]", "");
        
        if (context.length() > 2 && context.length() <= 20) {
            return context;
        }
        
        return null;
    }

    /**
     * 清理和验证生成的标题
     */
    private String cleanAndValidateTitle(String title) {
        if (!StringUtils.hasText(title)) {
            return null;
        }

        // 移除引号和多余的空格
        String cleaned = title.trim()
                .replaceAll("^[\"'`]|[\"'`]$", "")
                .replaceAll("\\s+", " ")
                .trim();

        // 验证标题长度
        if (cleaned.length() < 2 || cleaned.length() > 50) {
            return null;
        }

        // 确保不是无意义的标题
        if (cleaned.equals("新对话") || cleaned.equals("对话") || cleaned.equals("标题")) {
            return null;
        }

        // 限制标题长度为合理范围
        if (cleaned.length() > 20) {
            cleaned = cleaned.substring(0, 20);
        }

        return cleaned;
    }

    /**
     * 从AI响应中提取内容
     */
    private String extractResponseContent(ChatResponse response) {
        if (response == null || response.getChoices() == null || response.getChoices().isEmpty()) {
            return null;
        }

        return response.getChoices().get(0).getMessage().getContent();
    }
}
