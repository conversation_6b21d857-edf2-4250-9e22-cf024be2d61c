package com.yeepay.ai.main.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 解析待确认事项响应DTO
 * 用于返回从PRD中解析出的待确认事项列表
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ParsePendingItemsResponse implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * PRD文档ID
     */
    private String prdId;

    /**
     * 待确认事项列表
     */
    private List<PendingItemDTO> pendingItems;

    /**
     * 是否有待确认事项
     */
    private Boolean hasPendingItems;

    /**
     * 解析状态消息
     */
    private String message;

    /**
     * 原始待确认事项文本
     * 用于调试和验证
     */
    private String originalPendingText;
} 