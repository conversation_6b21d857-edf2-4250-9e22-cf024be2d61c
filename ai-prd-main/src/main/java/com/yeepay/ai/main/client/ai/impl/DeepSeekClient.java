package com.yeepay.ai.main.client.ai.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.ai.main.client.ai.AIClient;
import com.yeepay.ai.main.client.ai.AIConstants;
import com.yeepay.ai.main.client.ai.model.ChatMessage;
import com.yeepay.ai.main.client.ai.model.ChatRequest;
import com.yeepay.ai.main.client.ai.model.ChatResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import okhttp3.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * DeepSeek AI客户端实现
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
public class DeepSeekClient implements AIClient {
    
    private static final Logger logger = LogManager.getLogger(DeepSeekClient.class);
    
    // 超时配置常量
    private static final int CONNECT_TIMEOUT = 30;
    private static final int READ_TIMEOUT = 60;
    private static final int WRITE_TIMEOUT = 30;
    
    // 配置字段
    private String apiKey;
    private String baseUrl;
    private String model;
    private int maxTokens;
    private double temperature;
    private double topP;
    
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    
    public DeepSeekClient() {
        this.objectMapper = new ObjectMapper();
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                .build();
    }
    
    @Override
    public ChatResponse chat(List<ChatMessage> messages) {
        try {
            // 构建请求
            ChatRequest chatRequest = buildChatRequest(messages);
            String requestBody = objectMapper.writeValueAsString(chatRequest);
            
            logger.debug("DeepSeek API request: {}", requestBody);
            
            Request request = new Request.Builder()
                    .url(baseUrl + "/chat/completions")
                    .header("Authorization", "Bearer " + apiKey)
                    .header("Content-Type", "application/json")
                    .post(RequestBody.create(requestBody, MediaType.get("application/json")))
                    .build();
            
            // 发送请求
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String errorBody = response.body() != null ? response.body().string() : "No response body";
                    logger.error("DeepSeek API request failed: code={}, body={}", response.code(), errorBody);
                    throw new RuntimeException("DeepSeek API request failed: " + response.code() + " " + errorBody);
                }
                
                String responseBody = response.body().string();
                logger.debug("DeepSeek API response: {}", responseBody);
                
                return objectMapper.readValue(responseBody, ChatResponse.class);
            }
            
        } catch (Exception e) {
            logger.error("DeepSeek API call failed", e);
            throw new RuntimeException("DeepSeek API call failed", e);
        }
    }
    
    /**
     * 构建聊天请求
     */
    private ChatRequest buildChatRequest(List<ChatMessage> messages) {
        ChatRequest chatRequest = new ChatRequest(model, messages);
        chatRequest.setMaxTokens(maxTokens);
        chatRequest.setTemperature(temperature);
        chatRequest.setTopP(topP);
        chatRequest.setStream(false); // 非流式响应
        return chatRequest;
    }
    
    @Override
    public String getProvider() {
        return AIConstants.PROVIDERS.DEEPSEEK;
    }
    
    @Override
    public String getModel() {
        return model;
    }
    
    @Override
    public void close() {
        httpClient.dispatcher().executorService().shutdown();
        httpClient.connectionPool().evictAll();
    }
    
    // Setter方法
    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }
    
    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }
    
    public void setModel(String model) {
        this.model = model;
    }
    
    public void setMaxTokens(int maxTokens) {
        this.maxTokens = maxTokens;
    }
    
    public void setTemperature(double temperature) {
        this.temperature = temperature;
    }
    
    public void setTopP(double topP) {
        this.topP = topP;
    }
} 