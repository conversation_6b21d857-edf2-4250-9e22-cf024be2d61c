package com.yeepay.ai.main.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yeepay.ai.main.entity.ConversationMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 对话消息DAO接口
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Mapper
public interface ConversationMessageDao extends BaseMapper<ConversationMessage> {

    /**
     * 根据对话ID查询消息列表
     *
     * @param conversationId 对话ID
     * @return 消息列表
     */
    List<ConversationMessage> selectByConversationId(@Param("conversationId") String conversationId);

    /**
     * 根据对话ID查询消息数量
     *
     * @param conversationId 对话ID
     * @return 消息数量
     */
    Integer countByConversationId(@Param("conversationId") String conversationId);

    /**
     * 根据对话ID查询最后一条消息
     *
     * @param conversationId 对话ID
     * @return 最后一条消息
     */
    ConversationMessage selectLastMessageByConversationId(@Param("conversationId") String conversationId);

    /**
     * 分页查询对话消息列表（按时间正序）
     *
     * @param conversationId 对话ID
     * @param offset         偏移量
     * @param pageSize       页大小
     * @return 消息列表
     */
    List<ConversationMessage> selectByConversationIdWithPage(@Param("conversationId") String conversationId,
                                                             @Param("offset") int offset,
                                                             @Param("pageSize") int pageSize);
} 