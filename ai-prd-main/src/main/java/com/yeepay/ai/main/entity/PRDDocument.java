package com.yeepay.ai.main.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * PRD文档实体
 * PRD内容直接存储在数据库中
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 * @updated 2024-12-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("prd_document")
public class PRDDocument {

    /**
     * PRD文档ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 对话ID
     */
    private String conversationId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 文档标题
     */
    private String title;

    /**
     * PRD文档内容(Markdown格式)
     */
    private String content;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 状态:1-草稿,2-已发布
     */
    private Integer status;

    /**
     * 分享token
     */
    private String shareToken;

    /**
     * 分享过期时间
     */
    private LocalDateTime shareExpireTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 是否为当前版本
     */
    private Boolean isCurrentVersion;

    /**
     * 生成触发方式：INITIAL/SUPPLEMENT/MANUAL_EDIT
     */
    private String generationTrigger;

    /**
     * 触发生成的消息ID（用于关联补充信息）
     */
    private String baseMessageId;

    /**
     * 乐观锁
     */
    private Long nonce;

    // 状态常量
    public static final int STATUS_DRAFT = 1;
    public static final int STATUS_PUBLISHED = 2;

    // 生成触发方式常量
    public static final String TRIGGER_INITIAL = "INITIAL";
    public static final String TRIGGER_SUPPLEMENT = "SUPPLEMENT";
    public static final String TRIGGER_MANUAL_EDIT = "MANUAL_EDIT";
} 