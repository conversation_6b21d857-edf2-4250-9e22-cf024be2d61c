package com.yeepay.ai.main.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yeepay.ai.main.entity.Conversation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 对话DAO接口
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Mapper
public interface ConversationDao extends BaseMapper<Conversation> {

    /**
     * 根据用户ID查询对话列表
     *
     * @param userId 用户ID
     * @return 对话列表
     */
    List<Conversation> selectByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID和状态查询对话列表
     *
     * @param userId 用户ID
     * @param status 状态
     * @return 对话列表
     */
    List<Conversation> selectByUserIdAndStatus(@Param("userId") String userId, @Param("status") Integer status);

    /**
     * 更新对话的信心指数
     *
     * @param conversationId  对话ID
     * @param confidenceScore 信心指数
     * @param nonce          版本号
     * @return 更新行数
     */
    int updateConfidenceScore(@Param("conversationId") String conversationId, 
                             @Param("confidenceScore") Integer confidenceScore,
                             @Param("nonce") Long nonce);

    /**
     * 更新对话标题
     *
     * @param conversationId 对话ID
     * @param title         新标题
     * @param updateUser    更新用户
     * @param nonce         版本号
     * @return 更新行数
     */
    int updateTitle(@Param("conversationId") String conversationId,
                   @Param("title") String title,
                   @Param("updateUser") String updateUser,
                   @Param("nonce") Long nonce);
} 