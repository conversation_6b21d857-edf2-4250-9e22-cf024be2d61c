package com.yeepay.ai.main.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 澄清问题DTO
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClarificationQuestionDTO implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 问题内容
     */
    private String question;

    /**
     * 选项列表
     */
    private List<String> options;
} 