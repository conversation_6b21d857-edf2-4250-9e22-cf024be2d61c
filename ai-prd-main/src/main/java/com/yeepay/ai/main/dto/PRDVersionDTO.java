package com.yeepay.ai.main.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * PRD版本信息DTO
 * 用于返回PRD版本历史列表
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PRDVersionDTO implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * PRD文档ID
     */
    private String id;

    /**
     * 对话ID
     */
    private String conversationId;

    /**
     * 文档标题
     */
    private String title;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否为当前版本
     */
    private Boolean isCurrentVersion;

    /**
     * 生成触发方式：INITIAL/SUPPLEMENT/MANUAL_EDIT
     */
    private String generationTrigger;

    /**
     * 生成触发方式描述
     */
    private String generationTriggerDesc;

    /**
     * 触发消息ID（关联到补充信息的消息）
     */
    private String baseMessageId;

    /**
     * 触发消息内容（用于版本说明）
     */
    private String triggerMessageContent;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 内容预览（前200个字符）
     */
    private String contentPreview;

    /**
     * 状态描述
     */
    private String statusDesc;

    // 生成触发方式常量
    public static final String TRIGGER_INITIAL = "INITIAL";
    public static final String TRIGGER_SUPPLEMENT = "SUPPLEMENT";
    public static final String TRIGGER_MANUAL_EDIT = "MANUAL_EDIT";
} 