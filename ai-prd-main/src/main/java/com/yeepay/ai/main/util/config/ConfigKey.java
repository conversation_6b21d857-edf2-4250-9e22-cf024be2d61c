/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ai.main.util.config;

/**
 * title: <br>
 * description: 统一配置键接口<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/24 11:01
 */
public interface ConfigKey {
    /**
     * 获取配置键
     *
     * @return
     */
    String getConfigKey();

    /**
     * 获取默认值
     *
     * @return
     */
    Object getDefaultValue();
}