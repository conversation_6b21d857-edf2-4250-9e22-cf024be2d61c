package com.yeepay.ai.main.client.ai;

/**
 * AI供应商配置基类
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
public abstract class AIProviderConfig {
    
    public enum Provider {
        DEEPSEEK("deepseek"),
        GEMINI("gemini"),
        OPENAI("openai");
        
        private final String name;
        
        Provider(String name) {
            this.name = name;
        }
        
        public String getName() {
            return name;
        }
        
        public static Provider fromString(String name) {
            for (Provider provider : Provider.values()) {
                if (provider.name.equalsIgnoreCase(name)) {
                    return provider;
                }
            }
            throw new IllegalArgumentException("Unsupported AI provider: " + name);
        }
    }
    
    // 连接超时时间（秒）
    public static final int CONNECT_TIMEOUT = 30;
    
    // 读取超时时间（秒）
    public static final int READ_TIMEOUT = 120;
    
    // 写入超时时间（秒）
    public static final int WRITE_TIMEOUT = 120;
    
    protected String apiKey;
    protected String model;
    protected int maxTokens;
    protected double temperature;
    protected double topP;
    
    public AIProviderConfig() {
        // 默认值将在子类中设置
    }
    
    public AIProviderConfig(String apiKey) {
        this.apiKey = apiKey;
    }
    
    // 抽象方法，子类需要实现
    public abstract Provider getProvider();
    public abstract String getBaseUrl();
    public abstract String getChatEndpoint();
    public abstract String getDefaultModel();
    
    // Getters and Setters
    public String getApiKey() {
        return apiKey;
    }
    
    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }
    
    public String getModel() {
        return model != null ? model : getDefaultModel();
    }
    
    public void setModel(String model) {
        this.model = model;
    }
    
    public int getMaxTokens() {
        return maxTokens;
    }
    
    public void setMaxTokens(int maxTokens) {
        this.maxTokens = maxTokens;
    }
    
    public double getTemperature() {
        return temperature;
    }
    
    public void setTemperature(double temperature) {
        this.temperature = temperature;
    }
    
    public double getTopP() {
        return topP;
    }
    
    public void setTopP(double topP) {
        this.topP = topP;
    }
} 