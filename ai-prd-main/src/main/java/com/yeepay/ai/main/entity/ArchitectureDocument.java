package com.yeepay.ai.main.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 架构设计文档实体
 * 架构设计内容直接存储在数据库中
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("architecture_document")
public class ArchitectureDocument {

    /**
     * 架构设计文档ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 对话ID
     */
    private String conversationId;

    /**
     * PRD文档ID
     */
    private String prdId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 文档标题
     */
    private String title;

    /**
     * 架构设计文档内容(Markdown格式)
     */
    private String content;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 状态:1-草稿,2-已发布
     */
    private Integer status;

    /**
     * 分享token
     */
    private String shareToken;

    /**
     * 分享过期时间
     */
    private LocalDateTime shareExpireTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 乐观锁
     */
    private Long nonce;

    /**
     * 是否最新版本: 1-是, 0-否
     */
    private Integer isLatest;

    /**
     * 生成触发方式: 1-初次生成, 2-手动重新生成, 3-PRD更新触发
     */
    private Integer generationTrigger;

    /**
     * PRD文档最后更新时间(生成时记录)
     */
    private LocalDateTime prdUpdateTime;

    // 状态常量
    public static final int STATUS_DRAFT = 1;
    public static final int STATUS_PUBLISHED = 2;

    // 生成触发方式常量
    public static final int TRIGGER_INITIAL = 1;           // 初次生成
    public static final int TRIGGER_MANUAL_REGENERATE = 2; // 手动重新生成
    public static final int TRIGGER_PRD_UPDATE = 3;        // PRD更新触发

    // 版本标记常量
    public static final int IS_LATEST_YES = 1;  // 是最新版本
    public static final int IS_LATEST_NO = 0;   // 非最新版本
} 