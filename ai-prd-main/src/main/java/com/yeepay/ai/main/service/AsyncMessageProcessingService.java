package com.yeepay.ai.main.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.ai.main.dto.AsyncMessageResponse;
import com.yeepay.ai.main.dto.MessageDTO;
import com.yeepay.ai.main.dto.MessageProcessingStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 异步消息处理状态管理服务
 * 
 * <AUTHOR> PRD Team
 * @since 2024-12-20
 */
@Slf4j
@Service
public class AsyncMessageProcessingService {
    
    private static final String PROCESSING_KEY_PREFIX = "ai_processing:";
    private static final int PROCESSING_TIMEOUT_MINUTES = 30; // 处理超时时间：30分钟
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    private final ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new com.fasterxml.jackson.datatype.jsr310.JavaTimeModule())
            .disable(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    
    /**
     * 创建消息处理任务
     */
    public AsyncMessageResponse createProcessingTask(String userMessageId, String conversationId) {
        AsyncMessageResponse response = AsyncMessageResponse.builder()
                .userMessageId(userMessageId)
                .conversationId(conversationId)
                .status(MessageProcessingStatus.PENDING)
                .statusDescription(MessageProcessingStatus.PENDING.getDescription())
                .progress(0)
                .currentStep("正在初始化AI分析任务...")
                .createTime(LocalDateTime.now())
                .build();
        
        saveProcessingStatus(userMessageId, response);
        
        // 等待一小段时间确保Redis写入成功
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Thread interrupted while waiting for Redis write");
        }
        
        log.info("Created processing task for message: {}", userMessageId);
        
        return response;
    }
    
    /**
     * 更新处理状态
     */
    public void updateProcessingStatus(String userMessageId, MessageProcessingStatus status, 
                                     Integer progress, String currentStep) {
        AsyncMessageResponse response = getProcessingStatus(userMessageId);
        if (response != null) {
            response.setStatus(status);
            response.setStatusDescription(status.getDescription());
            response.setProgress(progress);
            response.setCurrentStep(currentStep);
            
            if (status == MessageProcessingStatus.COMPLETED || status == MessageProcessingStatus.FAILED) {
                response.setCompleteTime(LocalDateTime.now());
            }
            
            saveProcessingStatus(userMessageId, response);
            log.debug("Updated processing status for message: {}, status: {}, progress: {}%", 
                     userMessageId, status.getName(), progress);
        }
    }
    
    /**
     * 设置处理完成
     */
    public void setProcessingCompleted(String userMessageId, MessageDTO aiMessage) {
        AsyncMessageResponse response = getProcessingStatus(userMessageId);
        if (response != null) {
            response.setStatus(MessageProcessingStatus.COMPLETED);
            response.setStatusDescription(MessageProcessingStatus.COMPLETED.getDescription());
            response.setProgress(100);
            response.setCurrentStep("AI回复已生成完成");
            response.setCompleteTime(LocalDateTime.now());
            response.setAiMessage(aiMessage);
            response.setAiMessageId(aiMessage.getId());
            
            saveProcessingStatus(userMessageId, response);
            log.info("Processing completed for message: {}, AI message: {}", userMessageId, aiMessage.getId());
        }
    }
    
    /**
     * 设置处理失败
     */
    public void setProcessingFailed(String userMessageId, String errorMessage) {
        AsyncMessageResponse response = getProcessingStatus(userMessageId);
        if (response != null) {
            response.setStatus(MessageProcessingStatus.FAILED);
            response.setStatusDescription(MessageProcessingStatus.FAILED.getDescription());
            response.setProgress(0);
            response.setCurrentStep("处理失败");
            response.setErrorMessage(errorMessage);
            response.setCompleteTime(LocalDateTime.now());
            
            saveProcessingStatus(userMessageId, response);
            log.warn("Processing failed for message: {}, error: {}", userMessageId, errorMessage);
        }
    }
    
    /**
     * 获取处理状态
     */
    public AsyncMessageResponse getProcessingStatus(String userMessageId) {
        try {
            String key = PROCESSING_KEY_PREFIX + userMessageId;
            String value = redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                return objectMapper.readValue(value, AsyncMessageResponse.class);
            }
            
            return null;
            
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize processing status for message: {}", userMessageId, e);
            return null;
        }
    }
    
    /**
     * 删除处理状态（用于清理已完成的任务）
     */
    public void removeProcessingStatus(String userMessageId) {
        String key = PROCESSING_KEY_PREFIX + userMessageId;
        redisTemplate.delete(key);
        log.debug("Removed processing status for message: {}", userMessageId);
    }
    
    /**
     * 保存处理状态到Redis
     */
    private void saveProcessingStatus(String userMessageId, AsyncMessageResponse response) {
        try {
            String key = PROCESSING_KEY_PREFIX + userMessageId;
            String value = objectMapper.writeValueAsString(response);
            
            // 设置过期时间，避免Redis积累过多数据
            redisTemplate.opsForValue().set(key, value, PROCESSING_TIMEOUT_MINUTES, TimeUnit.MINUTES);
            
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize processing status for message: {}", userMessageId, e);
        }
    }
} 