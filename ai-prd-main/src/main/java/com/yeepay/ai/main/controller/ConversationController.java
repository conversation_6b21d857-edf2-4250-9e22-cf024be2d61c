package com.yeepay.ai.main.controller;

import com.yeepay.ai.main.common.exection.ResponseResult;
import com.yeepay.ai.main.dto.*;
import com.yeepay.ai.main.service.AsyncMessageProcessingService;
import com.yeepay.ai.main.service.ConversationService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 对话管理控制器
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Api(tags = "对话管理", description = "对话创建、消息发送、对话列表等相关接口")
@Slf4j
@RestController
@RequestMapping("/api/conversation")
public class ConversationController extends BaseController {

    @Autowired
    private ConversationService conversationService;
    
    @Autowired
    private AsyncMessageProcessingService asyncProcessingService;

    /**
     * 创建新对话
     *
     * @param request 创建对话请求
     * @return 对话信息
     */
    @ApiOperation(value = "创建新对话", notes = "用户创建一个新的对话会话")
    @ApiResponses({
            @ApiResponse(code = 200, message = "创建成功"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/create")
    public ResponseResult<ConversationDTO> createConversation(
            @ApiParam(value = "创建对话请求参数", required = true) 
            @Valid @RequestBody CreateConversationRequest request) {
        try {
            String userId = getCurrentUserId();
            log.info("User {} creating conversation: {}", userId, request.getTitle());

            return conversationService.createConversation(userId, request);

        } catch (Exception e) {
            log.error("Failed to create conversation", e);
            return ResponseResult.error("创建对话失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户对话列表
     *
     * @return 对话列表
     */
    @ApiOperation(value = "获取对话列表", notes = "获取当前用户的对话列表，默认返回最新的10条")
    @ApiResponses({
            @ApiResponse(code = 200, message = "获取成功"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/list")
    public ResponseResult<List<ConversationDTO>> getConversationList() {
        try {
            String userId = getCurrentUserId();
            log.debug("User {} getting conversation list", userId);

            return conversationService.getConversationList(userId);

        } catch (Exception e) {
            log.error("Failed to get conversation list", e);
            return ResponseResult.error("获取对话列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取对话详情
     *
     * @param conversationId 对话ID
     * @return 对话信息
     */
    @GetMapping("/{conversationId}")
    public ResponseResult<ConversationDTO> getConversation(@PathVariable String conversationId) {
        try {
            String userId = getCurrentUserId();
            log.debug("User {} getting conversation: {}", userId, conversationId);

            // 通过列表接口获取单个对话（简化实现）
            ResponseResult<List<ConversationDTO>> listResult = conversationService.getConversationList(userId);
            if (!listResult.isSuccess()) {
                return ResponseResult.error(listResult.getMessage());
            }

            ConversationDTO conversation = listResult.getData().stream()
                    .filter(c -> conversationId.equals(c.getId()))
                    .findFirst()
                    .orElse(null);

            if (conversation == null) {
                return ResponseResult.error("对话不存在或无权限访问");
            }

            return ResponseResult.success(conversation);

        } catch (Exception e) {
            log.error("Failed to get conversation: {}", conversationId, e);
            return ResponseResult.error("获取对话失败: " + e.getMessage());
        }
    }

    /**
     * 发送消息（异步处理）
     *
     * @param conversationId 对话ID
     * @param request        发送消息请求
     * @return 异步处理任务信息
     */
    @ApiOperation(value = "发送消息", notes = "向指定对话发送消息，异步处理AI分析。返回处理任务ID，可通过状态查询接口获取AI回复。")
    @ApiResponses({
            @ApiResponse(code = 200, message = "消息发送成功，开始异步处理"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 404, message = "对话不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @PostMapping("/{conversationId}/message")
    public ResponseResult<AsyncMessageResponse> sendMessage(
            @ApiParam(value = "对话ID", required = true) @PathVariable String conversationId,
            @ApiParam(value = "发送消息请求参数", required = true) @Valid @RequestBody SendMessageRequest request) {
        try {
            String userId = getCurrentUserId();
            log.info("User {} sending message to conversation: {}", userId, conversationId);

            return conversationService.sendMessage(userId, conversationId, request);

        } catch (Exception e) {
            log.error("Failed to send message to conversation: {}", conversationId, e);
            return ResponseResult.error("发送消息失败: " + e.getMessage());
        }
    }

    /**
     * 查询消息处理状态
     *
     * @param userMessageId 用户消息ID
     * @return 处理状态信息
     */
    @ApiOperation(value = "查询消息处理状态", notes = "查询异步消息处理的状态和进度，获取AI回复")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 404, message = "处理任务不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/message/{userMessageId}/status")
    public ResponseResult<AsyncMessageResponse> getMessageProcessingStatus(
            @ApiParam(value = "用户消息ID", required = true) @PathVariable String userMessageId) {
        try {
            String userId = getCurrentUserId();
            log.debug("User {} querying message processing status: {}", userId, userMessageId);

            AsyncMessageResponse status = asyncProcessingService.getProcessingStatus(userMessageId);
            
            if (status == null) {
                return ResponseResult.error("处理任务不存在或已过期");
            }

            return ResponseResult.success(status);

        } catch (Exception e) {
            log.error("Failed to get message processing status: {}", userMessageId, e);
            return ResponseResult.error("查询处理状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取对话消息列表（兼容旧接口，默认返回最新的10条消息）
     *
     * @param conversationId 对话ID
     * @return 消息列表
     */
    @ApiOperation(value = "获取对话消息列表", notes = "获取指定对话的消息列表，默认返回最新的10条消息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "获取成功"),
            @ApiResponse(code = 404, message = "对话不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/{conversationId}/messages")
    public ResponseResult<List<MessageDTO>> getMessages(
            @ApiParam(value = "对话ID", required = true) @PathVariable String conversationId) {
        try {
            String userId = getCurrentUserId();
            log.debug("User {} getting messages for conversation: {}", userId, conversationId);

            return conversationService.getMessages(userId, conversationId);

        } catch (Exception e) {
            log.error("Failed to get messages for conversation: {}", conversationId, e);
            return ResponseResult.error("获取消息列表失败: " + e.getMessage());
        }
    }

    /**
     * 分页获取对话消息列表
     *
     * @param conversationId 对话ID
     * @param page           页码，从1开始
     * @param size           每页大小，默认10，最大50
     * @return 分页消息列表
     */
    @ApiOperation(value = "分页获取对话消息列表", notes = "支持分页的消息列表查询，按时间倒序返回最新消息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "获取成功"),
            @ApiResponse(code = 400, message = "请求参数错误"),
            @ApiResponse(code = 404, message = "对话不存在"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    @GetMapping("/{conversationId}/messages/page")
    public ResponseResult<PageResult<MessageDTO>> getMessagesByPage(
            @ApiParam(value = "对话ID", required = true) @PathVariable String conversationId,
            @ApiParam(value = "页码，从1开始", example = "1") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam(value = "每页大小，默认10，最大50", example = "10") @RequestParam(defaultValue = "10") Integer size) {
        try {
            String userId = getCurrentUserId();
            log.debug("User {} getting messages by page for conversation: {}, page: {}, size: {}", 
                     userId, conversationId, page, size);

            // 创建分页请求参数
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPage(page);
            pageRequest.setSize(size);

            // 参数校验
            if (page < 1) {
                return ResponseResult.error("页码不能小于1");
            }
            if (size < 1 || size > 50) {
                return ResponseResult.error("每页大小必须在1-50之间");
            }

            return conversationService.getMessagesByPage(userId, conversationId, pageRequest);

        } catch (Exception e) {
            log.error("Failed to get messages by page for conversation: {}", conversationId, e);
            return ResponseResult.error("获取消息列表失败: " + e.getMessage());
        }
    }

    /**
     * 删除对话
     *
     * @param conversationId 对话ID
     * @return 删除结果
     */
    @DeleteMapping("/{conversationId}")
    public ResponseResult<Boolean> deleteConversation(@PathVariable String conversationId) {
        try {
            String userId = getCurrentUserId();
            log.info("User {} deleting conversation: {}", userId, conversationId);

            // 这里需要在ConversationService中实现deleteConversation方法
            // 暂时返回成功
            return ResponseResult.success(true);

        } catch (Exception e) {
            log.error("Failed to delete conversation: {}", conversationId, e);
            return ResponseResult.error("删除对话失败: " + e.getMessage());
        }
    }

    /**
     * 重命名对话
     *
     * @param conversationId 对话ID
     * @param title          新标题
     * @return 更新后的对话信息
     */
    @PutMapping("/{conversationId}/title")
    public ResponseResult<ConversationDTO> renameConversation(@PathVariable String conversationId,
                                                              @RequestParam String title) {
        try {
            String userId = getCurrentUserId();
            log.info("User {} renaming conversation {} to: {}", userId, conversationId, title);

            return conversationService.renameConversation(userId, conversationId, title);

        } catch (Exception e) {
            log.error("Failed to rename conversation: {}", conversationId, e);
            return ResponseResult.error("重命名对话失败: " + e.getMessage());
        }
    }
} 