package com.yeepay.ai.main.client.ai;

import com.yeepay.ai.main.client.ai.model.ChatMessage;
import com.yeepay.ai.main.client.ai.model.ChatResponse;

import java.util.Arrays;
import java.util.List;

/**
 * AI客户端接口
 * 
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface AIClient {
    
    /**
     * 发送聊天请求
     * 
     * @param messages 消息列表
     * @return 聊天响应
     */
    ChatResponse chat(List<ChatMessage> messages);
    
    /**
     * 发送单条消息
     * 
     * @param message 消息内容
     * @return 聊天响应
     */
    default ChatResponse chat(String message) {
        return chat(Arrays.asList(ChatMessage.user(message)));
    }
    
    /**
     * 获取提供商类型
     * 
     * @return 提供商名称
     */
    String getProvider();
    
    /**
     * 获取模型名称
     * 
     * @return 模型名称
     */
    String getModel();
    
    /**
     * 关闭客户端
     */
    void close();
} 