package com.yeepay.ai.main.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步配置类
 * 为异步任务配置专用线程池
 * 
 * <AUTHOR> PRD Team
 * @since 2024-12-29
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncConfig {

    /**
     * 标题生成专用线程池
     * 用于对话标题的异步生成，避免阻塞主要业务流程
     */
    @Bean("titleGenerationExecutor")
    public Executor titleGenerationExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数：保持2个线程常驻
        executor.setCorePoolSize(2);
        
        // 最大线程数：高峰期最多4个线程
        executor.setMaxPoolSize(4);
        
        // 队列容量：最多排队20个任务
        executor.setQueueCapacity(20);
        
        // 线程空闲时间：60秒后回收
        executor.setKeepAliveSeconds(60);
        
        // 线程名前缀
        executor.setThreadNamePrefix("TitleGen-");
        
        // 拒绝策略：由调用者线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 关闭时等待任务完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(10);
        
        executor.initialize();
        
        log.info("Title generation thread pool initialized: core={}, max={}, queue={}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }

    /**
     * 通用异步任务线程池
     * 用于其他非关键异步任务
     */
    @Bean("commonAsyncExecutor")
    public Executor commonAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        executor.setCorePoolSize(3);
        executor.setMaxPoolSize(8);
        executor.setQueueCapacity(50);
        executor.setKeepAliveSeconds(120);
        executor.setThreadNamePrefix("CommonAsync-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(15);
        
        executor.initialize();
        
        log.info("Common async thread pool initialized: core={}, max={}, queue={}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }

    /**
     * AI分析专用异步执行器
     * 用于处理AI分析相关的异步任务，针对AI服务的长响应时间优化
     */
    @Bean("aiAnalysisExecutor")
    public Executor aiAnalysisExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数：根据AI服务的并发能力调整
        executor.setCorePoolSize(3);
        
        // 最大线程数：考虑到AI服务的响应时间较长，设置合理的最大线程数
        executor.setMaxPoolSize(8);
        
        // 队列容量：允许一定数量的任务排队等待
        executor.setQueueCapacity(50);
        
        // 线程存活时间：AI分析任务耗时较长，设置较长的空闲时间
        executor.setKeepAliveSeconds(300);
        
        // 线程名前缀
        executor.setThreadNamePrefix("AI-Analysis-");
        
        // 拒绝策略：当线程池满时，在调用者线程中执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 设置线程池关闭的等待时间：AI任务可能较长，给更多时间
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        
        log.info("AI Analysis Executor initialized: corePoolSize={}, maxPoolSize={}, queueCapacity={}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }
} 