package com.yeepay.ai.main.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * 分页请求DTO
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Data
@ApiModel("分页请求参数")
public class PageRequest implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 页码，从1开始
     */
    @ApiModelProperty(value = "页码，从1开始", example = "1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小，最大50", example = "10")
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 50, message = "每页大小不能超过50")
    private Integer size = 10;

    /**
     * 获取偏移量
     *
     * @return 偏移量
     */
    public int getOffset() {
        return (page - 1) * size;
    }
} 