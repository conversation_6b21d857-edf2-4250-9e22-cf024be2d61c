package com.yeepay.ai.main.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 异步消息处理响应DTO
 * 
 * <AUTHOR> PRD Team
 * @since 2024-12-20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsyncMessageResponse implements Serializable {
    private static final long serialVersionUID = -1L;
    
    /**
     * 用户消息ID
     */
    private String userMessageId;
    
    /**
     * AI消息ID（处理完成后才有）
     */
    private String aiMessageId;
    
    /**
     * 对话ID
     */
    private String conversationId;
    
    /**
     * 处理状态
     */
    private MessageProcessingStatus status;
    
    /**
     * 处理状态描述
     */
    private String statusDescription;
    
    /**
     * 进度百分比（0-100）
     */
    private Integer progress;
    
    /**
     * 当前处理阶段信息
     */
    private String currentStep;
    
    /**
     * 错误信息（如果处理失败）
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 完成时间（如果已完成）
     */
    private LocalDateTime completeTime;
    
    /**
     * AI回复内容（处理完成后）
     */
    private MessageDTO aiMessage;
} 