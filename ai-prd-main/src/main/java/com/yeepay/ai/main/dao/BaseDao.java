package com.yeepay.ai.main.dao;

import lombok.extern.slf4j.Slf4j;

/**
 * 基础DAO类
 * 提供数据访问层的通用功能和规范
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Slf4j
public abstract class BaseDao {

    /**
     * 记录SQL操作日志
     *
     * @param operation 操作名称
     * @param params 操作参数
     */
    protected void logSqlOperation(String operation, Object... params) {
        log.debug("DAO operation: {} with params: {}", operation, params);
    }

    /**
     * 记录SQL错误日志
     *
     * @param operation 操作名称
     * @param e 异常信息
     */
    protected void logSqlError(String operation, Exception e) {
        log.warn("DAO operation failed: {} - {}", operation, e.getMessage(), e);
    }
} 