package com.yeepay.ai.main.util;

import java.nio.charset.StandardCharsets;

/**
 * 编码工具类
 * 提供UTF-8编码相关的工具方法
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
public class EncodingUtils {

    /**
     * 确保字符串使用正确的UTF-8编码
     * 这个方法通过字节转换来修复可能的编码问题
     *
     * @param text 原始文本
     * @return UTF-8编码正确的文本
     */
    public static String ensureUtf8(String text) {
        if (text == null) {
            return null;
        }
        
        try {
            // 将字符串转换为UTF-8字节，然后再转回字符串，确保编码正确
            byte[] utf8Bytes = text.getBytes(StandardCharsets.UTF_8);
            return new String(utf8Bytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            // 如果转换失败，返回原始文本
            return text;
        }
    }

    /**
     * 清理和确保UTF-8编码
     * 除了编码修复，还会清理一些可能导致问题的字符
     *
     * @param text 原始文本
     * @return 清理并修复编码后的文本
     */
    public static String cleanAndEnsureUtf8(String text) {
        if (text == null) {
            return null;
        }
        
        // 先清理可能的问题字符
        String cleaned = text
            .replace("\uFFFD", "")  // 去除替换字符（乱码标识）
            .replaceAll("[\\p{Cntrl}&&[^\r\n\t]]", ""); // 去除控制字符，保留换行符、制表符和回车符
        
        // 然后确保UTF-8编码
        return ensureUtf8(cleaned);
    }
} 