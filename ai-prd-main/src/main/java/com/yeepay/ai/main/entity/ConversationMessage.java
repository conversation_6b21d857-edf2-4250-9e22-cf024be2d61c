package com.yeepay.ai.main.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 对话消息实体
 * 
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("conversation_message")
public class ConversationMessage {

    /**
     * 消息ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 对话ID
     */
    private String conversationId;

    /**
     * 发送者类型:1-用户,2-AI
     */
    private Integer senderType;

    /**
     * 内容类型:1-文本,2-文件,3-图片
     */
    private Integer contentType;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 文件URL数组(JSON格式)
     */
    private String fileUrls;

    /**
     * 文件元信息(JSON格式)
     */
    private String metadata;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    // 发送者类型常量
    public static final int SENDER_TYPE_USER = 1;
    public static final int SENDER_TYPE_AI = 2;

    // 内容类型常量
    public static final int CONTENT_TYPE_TEXT = 1;
    public static final int CONTENT_TYPE_FILE = 2;
    public static final int CONTENT_TYPE_IMAGE = 3;
} 