package com.yeepay.ai.main.common.exection;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 统一响应结果类
 * 
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResponseResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 请求追踪ID
     */
    private String traceId;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 成功响应码
     */
    public static final String SUCCESS_CODE = "000000";

    /**
     * 系统错误响应码
     */
    public static final String ERROR_CODE = "999999";

    /**
     * 成功消息
     */
    public static final String SUCCESS_MESSAGE = "SUCCESS";

    /**
     * 系统错误消息
     */
    public static final String ERROR_MESSAGE = "SYSTEM_ERROR";

    public ResponseResult() {
    }

    public ResponseResult(String code, String message, String traceId, T data) {
        this.code = code;
        this.message = message;
        this.traceId = traceId;
        this.data = data;
    }

    /**
     * 成功响应(无数据)
     */
    public static <T> ResponseResult<T> success() {
        return success(null);
    }

    /**
     * 成功响应(带数据)
     */
    public static <T> ResponseResult<T> success(T data) {
        return success(SUCCESS_MESSAGE, data);
    }

    /**
     * 成功响应(自定义消息)
     */
    public static <T> ResponseResult<T> success(String message, T data) {
        return new ResponseResult<>(SUCCESS_CODE, message, getTraceId(), data);
    }

    /**
     * 失败响应(默认消息)
     */
    public static <T> ResponseResult<T> error() {
        return error(ERROR_MESSAGE);
    }

    /**
     * 失败响应(自定义消息)
     */
    public static <T> ResponseResult<T> error(String message) {
        return error(ERROR_CODE, message);
    }

    /**
     * 失败响应(自定义错误码和消息)
     */
    public static <T> ResponseResult<T> error(String code, String message) {
        return new ResponseResult<>(code, message, getTraceId(), null);
    }

    /**
     * 失败响应(带数据)
     */
    public static <T> ResponseResult<T> error(String code, String message, T data) {
        return new ResponseResult<>(code, message, getTraceId(), data);
    }

    /**
     * 生成追踪ID
     */
    private static String getTraceId() {
        return java.util.UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return SUCCESS_CODE.equals(this.code);
    }

} 