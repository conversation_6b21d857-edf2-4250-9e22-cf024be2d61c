package com.yeepay.ai.main.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 架构设计文档DTO
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Data
@Builder
public class ArchitectureDocumentDTO implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 架构设计文档ID
     */
    private String id;

    /**
     * 对话ID
     */
    private String conversationId;

    /**
     * PRD文档ID
     */
    private String prdId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 文档标题
     */
    private String title;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 状态:1-草稿,2-已发布
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 分享token
     */
    private String shareToken;

    /**
     * 分享链接
     */
    private String shareUrl;

    /**
     * 分享过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime shareExpireTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 架构设计内容（仅在查看详情时返回）
     */
    private String content;

    /**
     * 是否最新版本: 1-是, 0-否
     */
    private Integer isLatest;

    /**
     * 是否最新版本描述
     */
    private String isLatestDesc;

    /**
     * 生成触发方式: 1-初次生成, 2-手动重新生成, 3-PRD更新触发
     */
    private Integer generationTrigger;

    /**
     * 生成触发方式描述
     */
    private String generationTriggerDesc;

    /**
     * PRD文档最后更新时间(生成时记录)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime prdUpdateTime;
} 