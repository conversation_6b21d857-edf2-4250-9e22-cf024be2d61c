/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.ai.main.config;

import com.yeepay.g3.core.yuia.yuiacommons.patron.interceptors.DefaultAuthenticationInterceptor;
import com.yeepay.g3.core.yuia.yuiacommons.patron.interceptors.DefaultAuthorizationInterceptor;
import com.yeepay.g3.core.yuia.yuiacommons.patron.interceptors.DefaultResourceInterceptor;
import com.yeepay.g3.core.yuia.yuiacommons.patron.interceptors.PatronContextInterceptor;
import com.yeepay.g3.core.yuia.yuiacommons.patron.interceptors.WebRequestLogInterceptor;
import com.yeepay.g3.core.yuia.yuiacommons.patronclient.DefaultPatronConfiguration;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * title: <br>
 * description: <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/24 16:09
 */
@EnableWebMvc
@Configuration
@RequiredArgsConstructor
@Import(DefaultPatronConfiguration.class)
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private PatronContextInterceptor patronContextInterceptor;

    @Autowired
    private DefaultResourceInterceptor defaultResourceInterceptor;

    @Autowired
    private DefaultAuthenticationInterceptor defaultAuthenticationInterceptor;

    @Autowired
    private DefaultAuthorizationInterceptor defaultAuthorizationInterceptor;

    @Autowired
    private WebRequestLogInterceptor webRequestLogInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(patronContextInterceptor);
        registry.addInterceptor(defaultResourceInterceptor);
        
        // 权限拦截器排除Swagger相关路径
        registry.addInterceptor(defaultAuthenticationInterceptor)
                .excludePathPatterns(
                        "/swagger-ui/**",
                        "/swagger-ui.html",
                        "/swagger-resources/**",
                        "/v2/api-docs",
                        "/v3/api-docs",
                        "/v3/api-docs/**",
                        "/webjars/**",
                        "/api/health/check"  // 健康检查接口也不需要权限验证
                );
        
        registry.addInterceptor(defaultAuthorizationInterceptor)
                .excludePathPatterns(
                        "/swagger-ui/**",
                        "/swagger-ui.html", 
                        "/swagger-resources/**",
                        "/v2/api-docs",
                        "/v3/api-docs",
                        "/v3/api-docs/**",
                        "/webjars/**",
                        "/api/health/check",
                        "/api/prd/share/**"  // PRD分享接口无需权限验证
                );
        
        registry.addInterceptor(webRequestLogInterceptor);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置Swagger UI静态资源映射
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/springfox-swagger-ui/")
                .resourceChain(false);
                
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }

}