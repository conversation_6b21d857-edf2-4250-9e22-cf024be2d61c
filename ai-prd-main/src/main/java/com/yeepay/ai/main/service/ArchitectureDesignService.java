package com.yeepay.ai.main.service;

import com.yeepay.ai.main.client.ai.AIClient;
import com.yeepay.ai.main.client.ai.AIClientFactory;
import com.yeepay.ai.main.client.ai.AIConfigService;
import com.yeepay.ai.main.client.ai.model.ChatMessage;
import com.yeepay.ai.main.client.ai.model.ChatResponse;
import com.yeepay.ai.main.common.exection.BusinessException;
import com.yeepay.ai.main.common.exection.ResponseResult;
import com.yeepay.ai.main.dao.ArchitectureDocumentDao;
import com.yeepay.ai.main.dao.ConversationDao;
import com.yeepay.ai.main.dao.ConversationMessageDao;
import com.yeepay.ai.main.dao.PRDDocumentDao;
import com.yeepay.ai.main.dto.ArchitectureDocumentDTO;
import com.yeepay.ai.main.dto.PRDDocumentDTO;
import com.yeepay.ai.main.entity.ArchitectureDocument;
import com.yeepay.ai.main.entity.Conversation;
import com.yeepay.ai.main.entity.ConversationMessage;
import com.yeepay.ai.main.entity.PRDDocument;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * 架构设计生成服务
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Slf4j
@Service
public class ArchitectureDesignService {

    @Autowired
    private ConversationDao conversationDao;

    @Autowired
    private ConversationMessageDao conversationMessageDao;

    @Autowired
    private PRDDocumentDao prdDocumentDao;

    @Autowired
    private ArchitectureDocumentDao architectureDocumentDao;

    @Autowired
    private AIConfigService aiConfigService;

    @Autowired
    private PromptService promptService;

    @Autowired
    private AIClientFactory aiClientFactory;

    /**
     * 流式生成架构设计文档
     */
    @Transactional
    public void generateArchitectureDesignStream(String conversationId, String prdId, String userId,
                                                BiConsumer<Integer, String> onProgress,
                                                Consumer<String> onContent,
                                                Consumer<ArchitectureDocumentDTO> onComplete,
                                                Consumer<Exception> onError) {
        try {
            log.info("Starting stream architecture design generation for conversation: {} prd: {} by user: {}", 
                    conversationId, prdId, userId);

            // 1. 验证对话是否存在
            onProgress.accept(10, "验证对话信息...");
            Conversation conversation = conversationDao.selectById(conversationId);
            if (conversation == null) {
                onError.accept(new BusinessException("对话不存在"));
                return;
            }

            // 2. 验证PRD文档是否存在
            onProgress.accept(20, "验证PRD文档...");
            PRDDocument prdDocument = prdDocumentDao.selectById(prdId);
            if (prdDocument == null) {
                onError.accept(new BusinessException("PRD文档不存在"));
                return;
            }

            // 3. 检查是否已经存在架构设计文档，如果存在则调用重新生成逻辑
            onProgress.accept(30, "检查现有架构设计文档...");
            ArchitectureDocument existingArchitecture = architectureDocumentDao.selectLatestByPrdId(prdId);
            if (existingArchitecture != null) {
                log.info("Architecture document already exists for prd: {}, regenerating...", prdId);
                regenerateArchitectureDesignStream(prdId, userId, ArchitectureDocument.TRIGGER_MANUAL_REGENERATE,
                        onProgress, onContent, onComplete, onError);
                return;
            }

            // 4. 流式生成架构设计内容
            onProgress.accept(50, "开始生成架构设计...");
            StringBuilder architectureContentBuilder = new StringBuilder();

            generateArchitectureDesignContentStream(prdDocument, onContent, (contentChunk) -> {
                architectureContentBuilder.append(contentChunk);
            });

            // 5. 等待生成完成并保存
            String finalArchitectureContent = architectureContentBuilder.toString();
            if (finalArchitectureContent.isEmpty()) {
                onError.accept(new BusinessException("架构设计内容生成失败"));
                return;
            }

            onProgress.accept(90, "保存架构设计文档...");

            // 6. 保存架构设计文档信息和内容到数据库（首次生成）
            ArchitectureDocument architectureDocument = new ArchitectureDocument();
            architectureDocument.setConversationId(conversationId);
            architectureDocument.setPrdId(prdId);
            architectureDocument.setUserId(userId);
            architectureDocument.setTitle(prdDocument.getTitle() + " - 架构设计");
            architectureDocument.setContent(finalArchitectureContent);
            architectureDocument.setVersion(1);
            architectureDocument.setStatus(ArchitectureDocument.STATUS_DRAFT);
            architectureDocument.setIsLatest(ArchitectureDocument.IS_LATEST_YES);
            architectureDocument.setGenerationTrigger(ArchitectureDocument.TRIGGER_INITIAL);
            architectureDocument.setPrdUpdateTime(prdDocument.getUpdateTime());
            architectureDocument.setNonce(0L);
            architectureDocument.setCreateTime(LocalDateTime.now());
            architectureDocument.setUpdateTime(LocalDateTime.now());
            architectureDocument.setCreateUser(userId);
            architectureDocument.setUpdateUser(userId);

            architectureDocumentDao.insert(architectureDocument);

            // 7. 转换为DTO并包含内容
            ArchitectureDocumentDTO architectureDocumentDTO = convertToDTO(architectureDocument, finalArchitectureContent);

            log.info("Stream architecture design generated successfully for prd: {}", prdId);
            onComplete.accept(architectureDocumentDTO);

        } catch (Exception e) {
            log.error("Failed to generate stream architecture design for prd: {}", prdId, e);
            onError.accept(e);
        }
    }

    /**
     * 流式生成架构设计内容
     */
    private void generateArchitectureDesignContentStream(PRDDocument prdDocument,
                                                        Consumer<String> onContent,
                                                        Consumer<String> onChunk) {
        try {
            // 获取AI配置
            AIConfigService.AIConfig config = aiConfigService.getAIConfig();
            log.info("开始生成架构设计内容，使用AI提供商: {}, 模型: {}", config.getProvider(), config.getModel());

            // 检查是否启用真实流式生成（仅支持deepseek）
            boolean enableRealStream = isRealStreamEnabled(config);

            if (enableRealStream) {
                // 尝试使用真实AI流式生成（只有deepseek支持）
                try {
                    log.info("使用真实AI流式生成架构设计内容，提供商: {}", config.getProvider());
                    generateStreamWithAI(prdDocument, onContent, onChunk);
                    return;
                } catch (Exception aiError) {
                    log.warn("真实AI流式生成失败，提供商: {}, 错误: {}, 降级到增强模拟流式", 
                            config.getProvider(), aiError.getMessage());
                    // 降级到增强模拟流式生成
                    generateEnhancedStreamMocked(prdDocument, onContent, onChunk);
                    return;
                }
            }

            // 使用增强模拟流式生成（支持所有AI提供商）
            log.info("使用增强模拟流式生成架构设计内容（支持所有AI提供商，先AI生成再模拟流式）");
            generateEnhancedStreamMocked(prdDocument, onContent, onChunk);

        } catch (Exception e) {
            log.error("架构设计内容生成失败", e);
            // 最终降级方案
            log.info("使用最终降级方案生成架构设计内容");
            generateStreamMocked(prdDocument, onContent, onChunk);
        }
    }

    /**
     * 增强模拟流式生成（更接近真实效果）
     * 参考PRD生成服务的逻辑：使用真实AI生成内容，然后模拟流式发送
     */
    private void generateEnhancedStreamMocked(PRDDocument prdDocument,
                                              Consumer<String> onContent,
                                              Consumer<String> onChunk) {
        try {
            log.info("使用增强模拟流式生成架构设计内容");

            // 获取系统提示词
            String systemPrompt = promptService.getArchitectureDesignSystemPrompt();

            // 构建消息
            List<ChatMessage> messages = Arrays.asList(
                new ChatMessage("system", systemPrompt),
                new ChatMessage("user", "请基于以下PRD文档生成系统架构设计：\n\n" + prdDocument.getContent())
            );

            // 使用AI生成完整内容（支持所有AI提供商）
            AIClient aiClient = aiClientFactory.createClient();
            ChatResponse response = aiClient.chat(messages);
            String fullContent = extractResponseContent(response);
            aiClient.close();

            // 清理内容
            fullContent = cleanAIContent(fullContent);

            // 增强的流式发送效果（模拟真实AI生成节奏）
            sendContentWithEnhancedStream(fullContent, onContent, onChunk);

        } catch (Exception e) {
            log.error("增强模拟流式生成失败，降级到智能模板生成", e);
            // 降级到智能模板流式生成
            generateStreamMocked(prdDocument, onContent, onChunk);
        }
    }

    /**
     * 增强的流式内容发送（模拟更真实的AI生成节奏）
     * 参考PRD生成服务的实现
     */
    private void sendContentWithEnhancedStream(String fullContent,
                                               Consumer<String> onContent,
                                               Consumer<String> onChunk) {
        // 按语义单元分割（句子、段落等）
        String[] sentences = fullContent.split("(?<=[。！？；：])|(?<=\\n\\n)");

        for (String sentence : sentences) {
            if (sentence.trim().isEmpty()) continue;

            // 再按词语分割，模拟AI逐词生成
            String[] words = sentence.split("(?<=\\s)|(?<=[，、])|(?<=\\p{P})");

            for (String word : words) {
                if (word.trim().isEmpty()) continue;

                // 发送单词
                onContent.accept(word);
                onChunk.accept(word);

                // 动态延迟（模拟AI思考时间）
                try {
                    int delay = calculateDelay(word);
                    Thread.sleep(delay);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            // 句子间稍长停顿
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    /**
     * 计算发送延迟（模拟真实AI生成节奏）
     * 参考PRD生成服务的实现
     */
    private int calculateDelay(String word) {
        // 基础延迟
        int baseDelay = 30;

        // 根据内容长度调整
        int lengthDelay = word.length() * 5;

        // 特殊内容延迟调整
        if (word.contains("##") || word.contains("###")) {
            // 标题生成稍慢
            lengthDelay += 50;
        } else if (word.contains("```") || word.contains("|")) {
            // 代码块和表格生成稍慢
            lengthDelay += 30;
        } else if (word.matches(".*[0-9]+.*")) {
            // 数字内容稍快
            lengthDelay = lengthDelay / 2;
        }

        // 随机波动（模拟AI不稳定的生成速度）
        int randomFactor = (int) (Math.random() * 20 - 10); // -10到+10的随机数

        return Math.max(10, Math.min(150, baseDelay + lengthDelay + randomFactor));
    }

    /**
     * 检查是否启用真实流式生成
     * 注意：这里保持与PRD生成服务相同的逻辑，只有deepseek支持真实流式
     */
    private boolean isRealStreamEnabled(AIConfigService.AIConfig config) {
        // 检查AI提供商是否支持流式（目前只有deepseek支持真实流式）
        if (!"deepseek".equalsIgnoreCase(config.getProvider())) {
            log.debug("AI提供商 {} 不支持真实流式生成，使用增强模拟流式", config.getProvider());
            return false;
        }

        // 检查API密钥是否配置
        String apiKey = config.getApiKey();
        if (apiKey == null || apiKey.trim().isEmpty()) {
            log.warn("AI提供商 {} 的API密钥未配置，降级到增强模拟流式", config.getProvider());
            return false;
        }

        // 可以通过环境变量或配置文件控制
        String enableRealStream = System.getProperty("ai.architecture.enable.real.stream", "true");
        boolean enabled = "true".equalsIgnoreCase(enableRealStream);

        log.debug("真实流式生成开关: {} (提供商: {})", enabled ? "启用" : "禁用", config.getProvider());
        return enabled;
    }

    /**
     * 使用真实AI流式生成架构设计（仅支持deepseek）
     * 注意：这里应该实现真正的流式AI调用，目前是简化实现
     */
    private void generateStreamWithAI(PRDDocument prdDocument,
                                     Consumer<String> onContent,
                                     Consumer<String> onChunk) {
        log.info("开始真实AI流式生成架构设计内容");

        try {
            // 获取AI配置
            AIConfigService.AIConfig config = aiConfigService.getAIConfig();

            // 检查是否支持流式生成（目前只有deepseek支持）
            if (!"deepseek".equalsIgnoreCase(config.getProvider())) {
                throw new UnsupportedOperationException("当前AI提供商不支持流式生成: " + config.getProvider());
            }

            // 获取系统提示词
            String systemPrompt = promptService.getArchitectureDesignSystemPrompt();

            // 构建消息
            List<ChatMessage> messages = Arrays.asList(
                new ChatMessage("system", systemPrompt),
                new ChatMessage("user", "请基于以下PRD文档生成系统架构设计：\n\n" + prdDocument.getContent())
            );

            // TODO: 这里应该实现真正的流式AI调用，类似PRD生成服务的StreamAIClient
            // 目前使用简化实现：先生成完整内容，再模拟流式发送
            AIClient aiClient = aiClientFactory.createClient();
            ChatResponse response = aiClient.chat(messages);
            String content = extractResponseContent(response);
            aiClient.close();

            // 清理内容
            content = cleanAIContent(content);

            // 使用增强流式发送（与增强模拟保持一致的发送效果）
            sendContentWithEnhancedStream(content, onContent, onChunk);

        } catch (Exception e) {
            log.error("真实AI流式生成失败", e);
            throw e;
        }
    }

    /**
     * 模拟流式生成架构设计（使用智能模板）
     */
    private void generateStreamMocked(PRDDocument prdDocument,
                                     Consumer<String> onContent,
                                     Consumer<String> onChunk) {
        String architectureContent = generateBasicArchitectureTemplate(prdDocument);
        sendContentWithEnhancedStream(architectureContent, onContent, onChunk);
    }

    /**
     * 生成基础架构设计模板
     */
    private String generateBasicArchitectureTemplate(PRDDocument prdDocument) {
        StringBuilder template = new StringBuilder();
        
        String prdContent = prdDocument.getContent() != null ? prdDocument.getContent() : "";
        String title = prdDocument.getTitle() != null ? prdDocument.getTitle() : "未知项目";

        template.append("# ").append(title).append(" - 系统架构设计\n\n");

        // 基于PRD内容分析系统类型和目标
        String systemType = analyzeSystemType(prdContent, title);
        String coreObjective = analyzeCoreObjective(prdContent, title);
        
        template.append("## 架构概述\n\n");
        template.append("### 系统定位\n");
        template.append("基于PRD需求，本系统定位为").append(systemType).append("，旨在").append(coreObjective).append("。\n\n");

        template.append("### 架构原则\n");
        template.append("- 高可用性：确保系统稳定运行\n");
        template.append("- 可扩展性：支持业务快速增长\n");
        template.append("- 安全性：保障数据和用户安全\n");
        template.append("- 可维护性：便于开发和运维\n\n");

        template.append("## 系统架构设计\n\n");
        template.append("### 整体架构图\n");
        
        // 基于PRD内容生成差异化的架构图
        String architectureDiagram = generateArchitectureDiagram(prdContent, title);
        template.append(architectureDiagram);

        template.append("### 分层架构\n");
        template.append("- **表现层**：用户界面和交互\n");
        template.append("- **业务层**：核心业务逻辑处理\n");
        template.append("- **数据层**：数据存储和访问\n\n");

        template.append("## 技术架构设计\n\n");
        template.append("### 技术栈选择\n");
        
        // 基于PRD内容建议技术栈
        String techStack = suggestTechStack(prdContent, title);
        template.append(techStack);

        template.append("## 数据库设计\n\n");
        template.append("### 数据模型\n");
        
        // 基于PRD内容生成数据模型
        String dataModel = generateDataModel(prdContent, title);
        template.append(dataModel);

        template.append("## 核心功能模块\n\n");
        
        // 基于PRD内容识别核心功能模块
        String coreModules = identifyCoreModules(prdContent, title);
        template.append(coreModules);

        template.append("## 待确认的架构决策\n\n");
        
        // 基于PRD内容生成针对性的决策点
        String architectureDecisions = generateArchitectureDecisions(prdContent, title);
        template.append(architectureDecisions);

        return template.toString();
    }

    /**
     * 分析系统类型
     */
    private String analyzeSystemType(String prdContent, String title) {
        String content = (prdContent + " " + title).toLowerCase();
        
        if (content.contains("管理") || content.contains("后台") || content.contains("admin")) {
            return "企业级管理系统";
        } else if (content.contains("电商") || content.contains("商城") || content.contains("购物")) {
            return "电商交易平台";
        } else if (content.contains("社交") || content.contains("聊天") || content.contains("通讯")) {
            return "社交通讯平台";
        } else if (content.contains("数据") || content.contains("分析") || content.contains("报表")) {
            return "数据分析平台";
        } else if (content.contains("内容") || content.contains("文章") || content.contains("博客")) {
            return "内容管理系统";
        } else if (content.contains("学习") || content.contains("教育") || content.contains("课程")) {
            return "在线教育平台";
        } else if (content.contains("金融") || content.contains("支付") || content.contains("财务")) {
            return "金融服务系统";
        } else {
            return "业务应用系统";
        }
    }

    /**
     * 分析核心目标
     */
    private String analyzeCoreObjective(String prdContent, String title) {
        String content = (prdContent + " " + title).toLowerCase();
        
        if (content.contains("效率") || content.contains("自动化")) {
            return "提升业务运营效率，实现流程自动化";
        } else if (content.contains("用户体验") || content.contains("体验")) {
            return "优化用户体验，提供便捷的服务";
        } else if (content.contains("数据") || content.contains("分析")) {
            return "实现数据驱动的业务决策支持";
        } else if (content.contains("协作") || content.contains("沟通")) {
            return "促进团队协作，提升沟通效率";
        } else {
            return "满足核心业务需求，提供稳定可靠的服务";
        }
    }

    /**
     * 生成架构图
     */
    private String generateArchitectureDiagram(String prdContent, String title) {
        StringBuilder diagram = new StringBuilder();
        diagram.append("```mermaid\n");
        diagram.append("graph TB\n");
        
        String content = (prdContent + " " + title).toLowerCase();
        
        if (content.contains("移动") || content.contains("app") || content.contains("手机")) {
            diagram.append("    A[移动端App] --> B[API网关]\n");
            diagram.append("    C[Web管理端] --> B\n");
        } else {
            diagram.append("    A[Web前端] --> B[负载均衡器]\n");
        }
        
        diagram.append("    B --> D[应用服务集群]\n");
        
        if (content.contains("微服务") || content.contains("分布式")) {
            diagram.append("    D --> E[用户服务]\n");
            diagram.append("    D --> F[业务服务]\n");
            diagram.append("    D --> G[数据服务]\n");
            diagram.append("    E --> H[MySQL数据库]\n");
            diagram.append("    F --> H\n");
            diagram.append("    G --> H\n");
        } else {
            diagram.append("    D --> E[业务处理层]\n");
            diagram.append("    E --> F[数据访问层]\n");
            diagram.append("    F --> G[MySQL数据库]\n");
        }
        
        if (content.contains("缓存") || content.contains("redis") || content.contains("性能")) {
            diagram.append("    D --> I[Redis缓存]\n");
        }
        
        if (content.contains("文件") || content.contains("上传") || content.contains("存储")) {
            diagram.append("    D --> J[文件存储]\n");
        }
        
        diagram.append("```\n\n");
        return diagram.toString();
    }

    /**
     * 建议技术栈
     */
    private String suggestTechStack(String prdContent, String title) {
        StringBuilder techStack = new StringBuilder();
        String content = (prdContent + " " + title).toLowerCase();
        
        // 前端技术栈
        if (content.contains("移动") || content.contains("app")) {
            techStack.append("- **移动端**：React Native / Flutter\n");
        }
        if (content.contains("管理") || content.contains("后台")) {
            techStack.append("- **管理端**：Vue.js + Element UI / React + Ant Design\n");
        } else {
            techStack.append("- **前端**：Vue.js / React + TypeScript\n");
        }
        
        // 后端技术栈（固定使用公司标准）
        techStack.append("- **后端**：Spring Boot + Java 1.8\n");
        
        // 数据存储
        if (content.contains("大数据") || content.contains("分析")) {
            techStack.append("- **数据库**：MySQL + MongoDB + Redis\n");
        } else {
            techStack.append("- **数据库**：MySQL + Redis\n");
        }
        
        // 中间件
        if (content.contains("消息") || content.contains("异步") || content.contains("队列")) {
            techStack.append("- **消息队列**：RabbitMQ / Apache Kafka\n");
        }
        
        techStack.append("- **部署**：Docker + Kubernetes\n");
        techStack.append("- **监控**：Prometheus + Grafana\n\n");
        
        return techStack.toString();
    }

    /**
     * 生成数据模型
     */
    private String generateDataModel(String prdContent, String title) {
        StringBuilder dataModel = new StringBuilder();
        dataModel.append("```mermaid\n");
        dataModel.append("erDiagram\n");
        
        String content = (prdContent + " " + title).toLowerCase();
        
        // 基础用户实体
        dataModel.append("    User {\n");
        dataModel.append("        varchar id PK\n");
        dataModel.append("        varchar username\n");
        dataModel.append("        varchar email\n");
        dataModel.append("        datetime create_time\n");
        dataModel.append("    }\n");
        
        // 根据内容生成相关实体
        if (content.contains("订单") || content.contains("商品") || content.contains("电商")) {
            dataModel.append("    Product {\n");
            dataModel.append("        varchar id PK\n");
            dataModel.append("        varchar name\n");
            dataModel.append("        decimal price\n");
            dataModel.append("        int stock\n");
            dataModel.append("    }\n");
            dataModel.append("    Order {\n");
            dataModel.append("        varchar id PK\n");
            dataModel.append("        varchar user_id FK\n");
            dataModel.append("        decimal total_amount\n");
            dataModel.append("        int status\n");
            dataModel.append("    }\n");
            dataModel.append("    User ||--o{ Order : places\n");
            dataModel.append("    Order }o--|| Product : contains\n");
        } else if (content.contains("文章") || content.contains("内容") || content.contains("博客")) {
            dataModel.append("    Article {\n");
            dataModel.append("        varchar id PK\n");
            dataModel.append("        varchar title\n");
            dataModel.append("        text content\n");
            dataModel.append("        varchar author_id FK\n");
            dataModel.append("        datetime publish_time\n");
            dataModel.append("    }\n");
            dataModel.append("    User ||--o{ Article : writes\n");
        } else {
            dataModel.append("    Business_Entity {\n");
            dataModel.append("        varchar id PK\n");
            dataModel.append("        varchar name\n");
            dataModel.append("        varchar user_id FK\n");
            dataModel.append("        datetime create_time\n");
            dataModel.append("    }\n");
            dataModel.append("    User ||--o{ Business_Entity : owns\n");
        }
        
        dataModel.append("```\n\n");
        return dataModel.toString();
    }

    /**
     * 识别核心功能模块
     */
    private String identifyCoreModules(String prdContent, String title) {
        StringBuilder modules = new StringBuilder();
        String content = (prdContent + " " + title).toLowerCase();
        
        // 基础模块
        modules.append("### 基础功能模块\n");
        modules.append("- **用户管理模块**：用户注册、登录、权限管理\n");
        modules.append("- **系统管理模块**：配置管理、日志管理、监控\n\n");
        
        // 业务模块
        modules.append("### 核心业务模块\n");
        
        if (content.contains("订单") || content.contains("商品") || content.contains("电商")) {
            modules.append("- **商品管理模块**：商品信息、库存管理\n");
            modules.append("- **订单管理模块**：订单创建、支付、配送\n");
            modules.append("- **购物车模块**：商品收藏、购物车管理\n");
        } else if (content.contains("内容") || content.contains("文章")) {
            modules.append("- **内容管理模块**：文章发布、编辑、审核\n");
            modules.append("- **分类管理模块**：内容分类、标签管理\n");
        } else if (content.contains("数据") || content.contains("分析")) {
            modules.append("- **数据采集模块**：数据源接入、数据清洗\n");
            modules.append("- **数据分析模块**：统计分析、报表生成\n");
        } else {
            modules.append("- **业务处理模块**：核心业务逻辑处理\n");
            modules.append("- **数据管理模块**：数据的增删改查操作\n");
        }
        
        modules.append("\n");
        return modules.toString();
    }

    /**
     * 生成架构决策点
     */
    private String generateArchitectureDecisions(String prdContent, String title) {
        StringBuilder decisions = new StringBuilder();
        String content = (prdContent + " " + title).toLowerCase();
        
        if (content.contains("高并发") || content.contains("大量") || content.contains("性能")) {
            decisions.append("- 是否采用微服务架构以支持高并发？\n");
            decisions.append("- 缓存策略选择（Redis集群 vs 单节点）\n");
            decisions.append("- 是否需要引入消息队列处理异步任务？\n");
        } else {
            decisions.append("- 是否采用单体架构还是微服务架构？\n");
            decisions.append("- 缓存策略选择（Redis vs Memcached）\n");
        }
        
        if (content.contains("移动") || content.contains("app")) {
            decisions.append("- 移动端技术选型（原生开发 vs 跨平台）\n");
        }
        
        if (content.contains("数据") || content.contains("分析")) {
            decisions.append("- 数据仓库架构设计（OLTP vs OLAP）\n");
            decisions.append("- 大数据处理技术选型\n");
        }
        
        decisions.append("- 部署方式（云服务 vs 自建机房）\n");
        decisions.append("- 监控和日志策略选择\n\n");
        
        return decisions.toString();
    }

    /**
     * 提取AI响应内容
     */
    private String extractResponseContent(ChatResponse response) {
        if (response != null && response.getChoices() != null && !response.getChoices().isEmpty()) {
            return response.getChoices().get(0).getMessage().getContent();
        }
        return "";
    }

    /**
     * 清理AI生成的内容
     */
    private String cleanAIContent(String content) {
        if (!StringUtils.hasText(content)) {
            return "";
        }

        return content.trim()
                .replaceAll("```markdown\n", "")
                .replaceAll("\n```$", "")
                .replaceAll("```$", "");
    }

    /**
     * 根据PRD ID获取架构设计文档
     */
    public ResponseResult<ArchitectureDocumentDTO> getArchitectureDocumentByPrdId(String prdId, String userId) {
        try {
            log.debug("Getting architecture document for PRD: {}", prdId);

            // 默认获取最新版本的架构文档
            ArchitectureDocument architectureDocument = architectureDocumentDao.selectLatestByPrdId(prdId);
            if (architectureDocument == null) {
                return ResponseResult.error("架构设计文档不存在");
            }

            ArchitectureDocumentDTO dto = convertToDTO(architectureDocument, architectureDocument.getContent());
            return ResponseResult.success(dto);

        } catch (Exception e) {
            log.error("Failed to get architecture document for PRD: {}", prdId, e);
            return ResponseResult.error("获取架构设计文档失败: " + e.getMessage());
        }
    }

    /**
     * 转换为DTO
     */
    private ArchitectureDocumentDTO convertToDTO(ArchitectureDocument architectureDocument, String content) {
        return ArchitectureDocumentDTO.builder()
                .id(architectureDocument.getId())
                .conversationId(architectureDocument.getConversationId())
                .prdId(architectureDocument.getPrdId())
                .userId(architectureDocument.getUserId())
                .title(architectureDocument.getTitle())
                .version(architectureDocument.getVersion())
                .status(architectureDocument.getStatus())
                .statusDesc(getStatusDesc(architectureDocument.getStatus()))
                .shareToken(architectureDocument.getShareToken())
                .shareExpireTime(architectureDocument.getShareExpireTime())
                .createTime(architectureDocument.getCreateTime())
                .updateTime(architectureDocument.getUpdateTime())
                .content(content)
                .isLatest(architectureDocument.getIsLatest())
                .isLatestDesc(getIsLatestDesc(architectureDocument.getIsLatest()))
                .generationTrigger(architectureDocument.getGenerationTrigger())
                .generationTriggerDesc(getGenerationTriggerDesc(architectureDocument.getGenerationTrigger()))
                .prdUpdateTime(architectureDocument.getPrdUpdateTime())
                .build();
    }

    /**
     * 获取状态描述
     */
    private String getStatusDesc(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case ArchitectureDocument.STATUS_DRAFT:
                return "草稿";
            case ArchitectureDocument.STATUS_PUBLISHED:
                return "已发布";
            default:
                return "未知";
        }
    }

    /**
     * 获取是否最新版本描述
     */
    private String getIsLatestDesc(Integer isLatest) {
        if (isLatest == null) {
            return "未知";
        }
        switch (isLatest) {
            case ArchitectureDocument.IS_LATEST_YES:
                return "最新版本";
            case ArchitectureDocument.IS_LATEST_NO:
                return "历史版本";
            default:
                return "未知";
        }
    }

    /**
     * 获取生成触发方式描述
     */
    private String getGenerationTriggerDesc(Integer generationTrigger) {
        if (generationTrigger == null) {
            return "未知";
        }
        switch (generationTrigger) {
            case ArchitectureDocument.TRIGGER_INITIAL:
                return "初次生成";
            case ArchitectureDocument.TRIGGER_MANUAL_REGENERATE:
                return "手动重新生成";
            case ArchitectureDocument.TRIGGER_PRD_UPDATE:
                return "PRD更新触发";
            default:
                return "未知";
        }
    }

    /**
     * 重新生成架构设计文档 - 流式处理
     */
    @Transactional
    public void regenerateArchitectureDesignStream(String prdId, String userId, Integer triggerType,
                                                  BiConsumer<Integer, String> onProgress,
                                                  Consumer<String> onContent,
                                                  Consumer<ArchitectureDocumentDTO> onComplete,
                                                  Consumer<Exception> onError) {
        try {
            log.info("Starting regenerate architecture design for prd: {} by user: {}, trigger: {}", 
                    prdId, userId, triggerType);

            // 1. 验证PRD文档是否存在
            onProgress.accept(10, "验证PRD文档...");
            PRDDocument prdDocument = prdDocumentDao.selectById(prdId);
            if (prdDocument == null) {
                onError.accept(new BusinessException("PRD文档不存在"));
                return;
            }

            // 2. 获取现有的最新版本架构文档
            onProgress.accept(20, "检查现有架构设计版本...");
            Integer nextVersion = getNextVersionForPrd(prdId);

            // 3. 标记所有旧版本为非最新
            onProgress.accept(30, "更新版本状态...");
            architectureDocumentDao.markAllVersionsAsNotLatest(prdId);

            // 4. 流式生成新的架构设计内容
            onProgress.accept(50, "开始重新生成架构设计...");
            StringBuilder architectureContentBuilder = new StringBuilder();

            generateArchitectureDesignContentStream(prdDocument, onContent, (contentChunk) -> {
                architectureContentBuilder.append(contentChunk);
            });

            // 5. 等待生成完成并保存新版本
            String finalArchitectureContent = architectureContentBuilder.toString();
            if (finalArchitectureContent.isEmpty()) {
                onError.accept(new BusinessException("架构设计内容重新生成失败"));
                return;
            }

            onProgress.accept(90, "保存新版本架构设计文档...");

            // 6. 保存新版本架构设计文档
            ArchitectureDocument newArchitectureDocument = new ArchitectureDocument();
            newArchitectureDocument.setConversationId(prdDocument.getConversationId());
            newArchitectureDocument.setPrdId(prdId);
            newArchitectureDocument.setUserId(userId);
            newArchitectureDocument.setTitle(prdDocument.getTitle() + " - 架构设计 v" + nextVersion);
            newArchitectureDocument.setContent(finalArchitectureContent);
            newArchitectureDocument.setVersion(nextVersion);
            newArchitectureDocument.setStatus(ArchitectureDocument.STATUS_DRAFT);
            newArchitectureDocument.setIsLatest(ArchitectureDocument.IS_LATEST_YES);
            newArchitectureDocument.setGenerationTrigger(triggerType);
            newArchitectureDocument.setPrdUpdateTime(prdDocument.getUpdateTime());
            newArchitectureDocument.setNonce(0L);
            newArchitectureDocument.setCreateTime(LocalDateTime.now());
            newArchitectureDocument.setUpdateTime(LocalDateTime.now());
            newArchitectureDocument.setCreateUser(userId);
            newArchitectureDocument.setUpdateUser(userId);

            architectureDocumentDao.insert(newArchitectureDocument);

            // 7. 转换为DTO并包含内容
            ArchitectureDocumentDTO architectureDocumentDTO = convertToDTO(newArchitectureDocument, finalArchitectureContent);

            log.info("Architecture design regenerated successfully for prd: {}, new version: {}", 
                    prdId, nextVersion);
            onComplete.accept(architectureDocumentDTO);

        } catch (Exception e) {
            log.error("Failed to regenerate architecture design for prd: {}", prdId, e);
            onError.accept(e);
        }
    }

    /**
     * 检查是否需要重新生成架构文档（PRD有更新）
     */
    public boolean shouldRegenerateForPrdUpdate(String prdId) {
        try {
            PRDDocument prdDocument = prdDocumentDao.selectById(prdId);
            if (prdDocument == null) {
                return false;
            }

            return architectureDocumentDao.shouldRegenerateForPrdUpdate(prdId, prdDocument.getUpdateTime());
        } catch (Exception e) {
            log.warn("Failed to check if should regenerate for prd: {}, error: {}", prdId, e.getMessage());
            return false;
        }
    }

    /**
     * 获取PRD对应的下一个版本号
     */
    private Integer getNextVersionForPrd(String prdId) {
        Integer maxVersion = architectureDocumentDao.getMaxVersionByPrdId(prdId);
        return maxVersion + 1;
    }

    /**
     * 根据PRD ID获取最新版本的架构设计文档
     */
    public ResponseResult<ArchitectureDocumentDTO> getLatestArchitectureDocumentByPrdId(String prdId, String userId) {
        try {
            ArchitectureDocument architectureDocument = architectureDocumentDao.selectLatestByPrdId(prdId);
            if (architectureDocument == null) {
                return ResponseResult.error("架构设计文档不存在");
            }

            ArchitectureDocumentDTO dto = convertToDTO(architectureDocument, architectureDocument.getContent());
            return ResponseResult.success(dto);
        } catch (Exception e) {
            log.error("Failed to get latest architecture document by prd id: {}", prdId, e);
            return ResponseResult.error("获取架构设计文档失败: " + e.getMessage());
        }
    }

    /**
     * 获取PRD对应的所有版本架构文档
     */
    public ResponseResult<java.util.List<ArchitectureDocumentDTO>> getAllVersionsByPrdId(String prdId, String userId) {
        try {
            java.util.List<ArchitectureDocument> documents = architectureDocumentDao.selectAllVersionsByPrdId(prdId);
            if (documents.isEmpty()) {
                return ResponseResult.error("未找到架构设计文档");
            }

            java.util.List<ArchitectureDocumentDTO> dtoList = documents.stream()
                    .map(doc -> convertToDTO(doc, doc.getContent()))
                    .collect(java.util.stream.Collectors.toList());

            return ResponseResult.success(dtoList);
        } catch (Exception e) {
            log.error("Failed to get all versions by prd id: {}", prdId, e);
            return ResponseResult.error("获取架构设计文档版本列表失败: " + e.getMessage());
        }
    }
} 