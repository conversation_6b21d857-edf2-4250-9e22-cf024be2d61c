package com.yeepay.ai.main.common.exection;

/**
 * 业务异常类
 * 
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
public class BusinessException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private String code;

    public BusinessException(String message) {
        super(message);
        this.code = ResponseResult.ERROR_CODE;
    }

    public BusinessException(String code, String message) {
        super(message);
        this.code = code;
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = ResponseResult.ERROR_CODE;
    }

    public BusinessException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
} 