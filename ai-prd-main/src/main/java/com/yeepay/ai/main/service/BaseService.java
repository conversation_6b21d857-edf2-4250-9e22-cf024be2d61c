package com.yeepay.ai.main.service;

import lombok.extern.slf4j.Slf4j;

/**
 * 基础Service类
 * 提供Service层的通用功能和规范
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Slf4j
public abstract class BaseService {

    /**
     * 记录操作日志
     *
     * @param operation 操作名称
     * @param params 操作参数
     */
    protected void logOperation(String operation, Object... params) {
        log.debug("Service operation: {} with params: {}", operation, params);
    }

    /**
     * 记录错误日志
     *
     * @param operation 操作名称
     * @param e 异常信息
     */
    protected void logError(String operation, Exception e) {
        log.warn("Service operation failed: {} - {}", operation, e.getMessage(), e);
    }
} 