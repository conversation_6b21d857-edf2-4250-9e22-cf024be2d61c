package com.yeepay.ai.main.service;

import com.yeepay.ai.main.common.exection.BusinessException;
import com.yeepay.ai.main.common.exection.ResponseResult;
import com.yeepay.ai.main.util.storage.StorageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 文件处理服务
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Slf4j
@Service
public class FileService {

    @Value("${ai-prd.file.upload-path:/tmp/ai-prd/uploads}")
    private String uploadBasePath;

    @Value("${ai-prd.file.max-size:10485760}")  // 10MB
    private long maxFileSize;

    @Value("${ai-prd.file.allowed-types:txt,md,docx,pdf,png,jpg,jpeg}")
    private String allowedFileTypes;

    // 支持的文档文件类型
    private static final Set<String> DOCUMENT_TYPES = new HashSet<>(Arrays.asList("txt", "md", "docx", "pdf"));
    
    // 支持的图片文件类型
    private static final Set<String> IMAGE_TYPES = new HashSet<>(Arrays.asList("png", "jpg", "jpeg", "gif"));

    /**
     * 上传文件
     *
     * @param file   上传的文件
     * @param userId 用户ID
     * @return 文件信息
     */
    public ResponseResult<FileInfo> uploadFile(MultipartFile file, String userId) {
        try {
            log.info("Uploading file: {} for user: {}", file.getOriginalFilename(), userId);

            // 验证文件
            validateFile(file);

            // 生成文件路径
            String fileName = generateFileName(file.getOriginalFilename());
            String relativePath = generateRelativePath(userId, fileName);

            // 使用云存储组件上传文件
            String fileUrl = StorageUtils.upload(relativePath, file.getInputStream());

            // 构建文件信息
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileName(file.getOriginalFilename());
            fileInfo.setStoredFileName(fileName);
            fileInfo.setRelativePath(relativePath);
            fileInfo.setAbsolutePath(fileUrl); // 云存储URL作为绝对路径
            fileInfo.setFileSize(file.getSize());
            fileInfo.setContentType(file.getContentType());
            fileInfo.setFileType(getFileType(file.getOriginalFilename()));
            fileInfo.setUploadTime(new Date());
            fileInfo.setUserId(userId);

            log.info("File uploaded successfully to cloud storage: {} -> {}", file.getOriginalFilename(), fileUrl);
            return ResponseResult.success(fileInfo);

        } catch (BusinessException e) {
            log.warn("Business error in uploadFile: {}", e.getMessage());
            return ResponseResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to upload file: {}", file.getOriginalFilename(), e);
            return ResponseResult.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 读取文件内容
     *
     * @param relativePath 相对路径
     * @param userId       用户ID
     * @return 文件内容
     */
    public ResponseResult<String> readFileContent(String relativePath, String userId) {
        try {
            log.debug("Reading file content: {} for user: {}", relativePath, userId);

            // 验证路径安全性
            if (!isValidPath(relativePath, userId)) {
                throw new BusinessException("无权限访问该文件");
            }

            // 从云存储下载文件
            InputStream inputStream = StorageUtils.download(relativePath);
            if (inputStream == null) {
                throw new BusinessException("文件不存在");
            }

            // 只读取文档类型文件
            String fileName = relativePath.substring(relativePath.lastIndexOf("/") + 1);
            String extension = FilenameUtils.getExtension(fileName).toLowerCase();
            if (!DOCUMENT_TYPES.contains(extension)) {
                throw new BusinessException("不支持读取该类型文件");
            }

            String content = readTextFromInputStream(inputStream, extension);
            log.debug("File content read successfully from cloud storage: {}", relativePath);

            return ResponseResult.success(content);

        } catch (BusinessException e) {
            log.warn("Business error in readFileContent: {}", e.getMessage());
            return ResponseResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to read file content: {}", relativePath, e);
            return ResponseResult.error("读取文件失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件
     *
     * @param relativePath 相对路径
     * @param userId       用户ID
     * @return 文件字节数组
     */
    public ResponseResult<byte[]> downloadFile(String relativePath, String userId) {
        try {
            log.info("Downloading file: {} for user: {}", relativePath, userId);

            // 验证路径安全性
            if (!isValidPath(relativePath, userId)) {
                throw new BusinessException("无权限访问该文件");
            }

            // 从云存储下载文件
            InputStream inputStream = StorageUtils.download(relativePath);
            if (inputStream == null) {
                throw new BusinessException("文件不存在");
            }

            byte[] fileBytes = readInputStream(inputStream);
            log.info("File downloaded successfully from cloud storage: {}", relativePath);

            return ResponseResult.success(fileBytes);

        } catch (BusinessException e) {
            log.warn("Business error in downloadFile: {}", e.getMessage());
            return ResponseResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to download file: {}", relativePath, e);
            return ResponseResult.error("下载文件失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     *
     * @param relativePath 相对路径
     * @param userId       用户ID
     * @return 删除结果
     */
    public ResponseResult<Boolean> deleteFile(String relativePath, String userId) {
        try {
            log.info("Deleting file: {} for user: {}", relativePath, userId);

            // 验证路径安全性
            if (!isValidPath(relativePath, userId)) {
                throw new BusinessException("无权限删除该文件");
            }

            // 从云存储删除文件
            StorageUtils.delete(relativePath);
            log.info("File deleted successfully from cloud storage: {}", relativePath);

            return ResponseResult.success(true);

        } catch (BusinessException e) {
            log.warn("Business error in deleteFile: {}", e.getMessage());
            return ResponseResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("Failed to delete file: {}", relativePath, e);
            return ResponseResult.error("删除文件失败: " + e.getMessage());
        }
    }

    /**
     * 导出PRD为Markdown文件
     *
     * @param content  PRD内容
     * @param fileName 文件名
     * @param userId   用户ID
     * @return 文件信息
     */
    public ResponseResult<FileInfo> exportPRDAsMarkdown(String content, String fileName, String userId) {
        try {
            log.info("Exporting PRD as markdown: {} for user: {}", fileName, userId);

            // 生成文件名和路径
            String markdownFileName = fileName.endsWith(".md") ? fileName : fileName + ".md";
            String sanitizedFileName = sanitizeFileName(markdownFileName);
            String relativePath = generateExportPath(userId, sanitizedFileName);

            // 上传到云存储
            byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
            InputStream inputStream = new ByteArrayInputStream(contentBytes);
            String fileUrl = StorageUtils.upload(relativePath, inputStream);

            // 构建文件信息
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileName(sanitizedFileName);
            fileInfo.setStoredFileName(sanitizedFileName);
            fileInfo.setRelativePath(relativePath);
            fileInfo.setAbsolutePath(fileUrl); // 云存储URL作为绝对路径
            fileInfo.setFileSize((long) contentBytes.length);
            fileInfo.setContentType("text/markdown");
            fileInfo.setFileType("document");
            fileInfo.setUploadTime(new Date());
            fileInfo.setUserId(userId);

            log.info("PRD exported successfully to cloud storage: {} -> {}", relativePath, fileUrl);
            return ResponseResult.success(fileInfo);

        } catch (Exception e) {
            log.error("Failed to export PRD as markdown: {}", fileName, e);
            return ResponseResult.error("导出PRD失败: " + e.getMessage());
        }
    }

    /**
     * 验证上传文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("上传文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > maxFileSize) {
            throw new BusinessException("文件大小超过限制，最大允许: " + formatFileSize(maxFileSize));
        }

        // 检查文件类型
        String originalFileName = file.getOriginalFilename();
        if (originalFileName == null || originalFileName.trim().isEmpty()) {
            throw new BusinessException("文件名不能为空");
        }

        String extension = FilenameUtils.getExtension(originalFileName).toLowerCase();
        if (!getAllowedFileTypes().contains(extension)) {
            throw new BusinessException("不支持的文件类型，允许的类型: " + allowedFileTypes);
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String originalFileName) {
        String extension = FilenameUtils.getExtension(originalFileName);
        String baseName = FilenameUtils.getBaseName(originalFileName);
        String timestamp = String.valueOf(System.currentTimeMillis());
        String randomStr = UUID.randomUUID().toString().substring(0, 8);
        
        return String.format("%s_%s_%s.%s", 
                sanitizeFileName(baseName), timestamp, randomStr, extension);
    }

    /**
     * 生成相对路径
     */
    private String generateRelativePath(String userId, String fileName) {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        return String.format("users/%s/%s/%s", userId, dateStr, fileName);
    }

    /**
     * 生成导出路径
     */
    private String generateExportPath(String userId, String fileName) {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        return String.format("exports/%s/%s/%s", userId, dateStr, fileName);
    }

    /**
     * 验证路径安全性
     */
    private boolean isValidPath(String relativePath, String userId) {
        // 检查路径是否包含用户ID（简单的安全检查）
        return relativePath != null && 
               !relativePath.contains("..") && 
               (relativePath.startsWith("users/" + userId + "/") || 
                relativePath.startsWith("exports/" + userId + "/"));
    }

    /**
     * 获取文件类型
     */
    private String getFileType(String fileName) {
        String extension = FilenameUtils.getExtension(fileName).toLowerCase();
        if (DOCUMENT_TYPES.contains(extension)) {
            return "document";
        } else if (IMAGE_TYPES.contains(extension)) {
            return "image";
        }
        return "other";
    }

    /**
     * 读取文本文件内容
     */
    private String readTextFile(File file, String extension) throws IOException {
        switch (extension) {
            case "txt":
            case "md":
                // Java 8兼容的方式读取文件内容
                return new String(readFileBytes(file), StandardCharsets.UTF_8);
            case "docx":
                return readDocxFile(file);
            case "pdf":
                return readPdfFile(file);
            default:
                throw new BusinessException("不支持读取该类型文件");
        }
    }

    /**
     * 读取DOCX文件内容（简化实现）
     */
    private String readDocxFile(File file) {
        // 这里需要使用Apache POI库来读取DOCX文件
        // 为了简化，先返回提示信息
        return "DOCX文件内容解析功能需要集成Apache POI库。文件名: " + file.getName();
    }

    /**
     * 读取PDF文件内容（简化实现）
     */
    private String readPdfFile(File file) {
        // 这里需要使用iText或PDFBox库来读取PDF文件
        // 为了简化，先返回提示信息
        return "PDF文件内容解析功能需要集成PDF处理库。文件名: " + file.getName();
    }

    /**
     * 清理文件名，移除特殊字符
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null) return "unnamed";
        return fileName.replaceAll("[^a-zA-Z0-9\\.\\-_\\u4e00-\\u9fa5]", "_");
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long fileSize) {
        String[] units = {"B", "KB", "MB", "GB"};
        int unitIndex = 0;
        double size = fileSize;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", size, units[unitIndex]);
    }

    /**
     * 获取允许的文件类型集合
     */
    private Set<String> getAllowedFileTypes() {
        return new HashSet<>(Arrays.asList(allowedFileTypes.split(",")));
    }
    
    /**
     * 读取文件字节数组（Java 8兼容）
     */
    private byte[] readFileBytes(File file) throws IOException {
        FileInputStream fis = new FileInputStream(file);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        try {
            while ((length = fis.read(buffer)) != -1) {
                baos.write(buffer, 0, length);
            }
            return baos.toByteArray();
        } finally {
            fis.close();
            baos.close();
        }
    }

    /**
     * 读取输入流字节数组（Java 8兼容）
     */
    private byte[] readInputStream(InputStream inputStream) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        try {
            while ((length = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, length);
            }
            return baos.toByteArray();
        } finally {
            inputStream.close();
            baos.close();
        }
    }

    /**
     * 从输入流读取文本内容（Java 8兼容）
     */
    private String readTextFromInputStream(InputStream inputStream, String extension) throws IOException {
        byte[] bytes = readInputStream(inputStream);
        
        switch (extension) {
            case "txt":
            case "md":
                return new String(bytes, StandardCharsets.UTF_8);
            case "docx":
                return "DOCX文件内容解析功能需要集成Apache POI库";
            case "pdf":
                return "PDF文件内容解析功能需要集成PDF处理库";
            default:
                throw new BusinessException("不支持读取该类型文件");
        }
    }

    /**
     * 文件信息DTO
     */
    public static class FileInfo {
        private String fileName;        // 原始文件名
        private String storedFileName;  // 存储的文件名
        private String relativePath;   // 相对路径
        private String absolutePath;   // 绝对路径
        private Long fileSize;         // 文件大小
        private String contentType;    // 内容类型
        private String fileType;       // 文件类型：document, image, other
        private Date uploadTime;       // 上传时间
        private String userId;         // 用户ID

        // Getters and Setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }

        public String getStoredFileName() { return storedFileName; }
        public void setStoredFileName(String storedFileName) { this.storedFileName = storedFileName; }

        public String getRelativePath() { return relativePath; }
        public void setRelativePath(String relativePath) { this.relativePath = relativePath; }

        public String getAbsolutePath() { return absolutePath; }
        public void setAbsolutePath(String absolutePath) { this.absolutePath = absolutePath; }

        public Long getFileSize() { return fileSize; }
        public void setFileSize(Long fileSize) { this.fileSize = fileSize; }

        public String getContentType() { return contentType; }
        public void setContentType(String contentType) { this.contentType = contentType; }

        public String getFileType() { return fileType; }
        public void setFileType(String fileType) { this.fileType = fileType; }

        public Date getUploadTime() { return uploadTime; }
        public void setUploadTime(Date uploadTime) { this.uploadTime = uploadTime; }

        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
    }
} 