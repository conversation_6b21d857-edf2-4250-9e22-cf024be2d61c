package com.yeepay.ai.main.dto;

/**
 * 消息处理状态枚举
 * 
 * <AUTHOR> PRD Team
 * @since 2024-12-20
 */
public enum MessageProcessingStatus {
    
    PENDING("pending", "处理中", "您的消息已收到，AI正在分析中..."),
    ANALYZING("analyzing", "分析中", "AI正在深度分析您的需求..."),
    GENERATING("generating", "生成中", "AI正在生成回复内容..."),
    COMPLETED("completed", "已完成", "分析完成"),
    FAILED("failed", "处理失败", "处理过程中出现错误，请重试");
    
    private final String code;
    private final String name;
    private final String description;
    
    MessageProcessingStatus(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public static MessageProcessingStatus fromCode(String code) {
        for (MessageProcessingStatus status : MessageProcessingStatus.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return PENDING;
    }
} 