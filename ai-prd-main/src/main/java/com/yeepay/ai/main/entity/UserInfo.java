package com.yeepay.ai.main.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户信息实体
 * 
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@Data
public class UserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 登录名
     */
    private String loginName;

    /**
     * 用户名
     */
    private String username;

    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 部门信息
     */
    private String department;

    /**
     * 角色列表
     */
    private List<String> roles;

    /**
     * 权限列表
     */
    private List<String> permissions;

    /**
     * 用户状态：1-正常，0-禁用
     */
    private Integer status;
} 