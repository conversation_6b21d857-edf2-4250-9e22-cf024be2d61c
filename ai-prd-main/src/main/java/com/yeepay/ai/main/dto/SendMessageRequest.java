package com.yeepay.ai.main.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * 发送消息请求
 *
 * <AUTHOR> PRD Team
 * @since 2024-12-19
 */
@ApiModel(description = "发送消息请求参数")
@Data
public class SendMessageRequest {

    /**
     * 内容类型:1-文本,2-文件,3-图片
     */
    @ApiModelProperty(value = "内容类型", required = true, example = "1", notes = "1-文本,2-文件,3-图片")
    @NotNull(message = "内容类型不能为空")
    private Integer contentType;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容", required = true, example = "我需要添加用户权限管理功能，包括角色管理和权限分配")
    @NotBlank(message = "消息内容不能为空")
    @Size(max = 10000, message = "消息内容长度不能超过10000字符")
    private String content;

    /**
     * 文件URL数组（当内容类型为文件或图片时）
     */
    @ApiModelProperty(value = "文件URL数组", required = false, notes = "当内容类型为文件或图片时使用")
    private List<String> fileUrls;

    /**
     * 文件元信息（当内容类型为文件或图片时）
     */
    @ApiModelProperty(value = "文件元信息", required = false, notes = "包含文件名、大小等信息")
    private Map<String, Object> metadata;
} 