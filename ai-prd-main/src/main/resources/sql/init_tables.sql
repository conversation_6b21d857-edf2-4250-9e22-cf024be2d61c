-- AI PRD Assistant Database Initialization Script
-- Created: 2024-12-19
-- Version: 1.0
-- 基于技术方案设计文档，严格按照设计规范实现

-- 使用数据库
USE ai_prd;

-- 删除已存在的表（开发环境使用，生产环境请谨慎操作）
-- DROP TABLE IF EXISTS prd_version_history;
-- DROP TABLE IF EXISTS prd_document;
-- DROP TABLE IF EXISTS conversation_message;
-- DROP TABLE IF EXISTS conversation;

-- =============================================
-- 对话会话表
-- =============================================
CREATE TABLE IF NOT EXISTS conversation (
    id VARCHAR(64) NOT NULL COMMENT '对话ID',
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    title VARCHAR(200) NOT NULL COMMENT '对话标题',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:1-进行中,2-已完成,3-已删除',
    confidence_score INT DEFAULT 0 COMMENT '当前信心指数',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_user VARCHAR(64) COMMENT '创建人',
    update_user VARCHAR(64) COMMENT '修改人',
    nonce BIGINT NOT NULL DEFAULT 0 COMMENT '乐观锁',
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对话会话表';

-- =============================================
-- 对话消息表
-- =============================================
CREATE TABLE IF NOT EXISTS conversation_message (
    id VARCHAR(64) NOT NULL COMMENT '消息ID',
    conversation_id VARCHAR(64) NOT NULL COMMENT '对话ID',
    sender_type TINYINT NOT NULL COMMENT '发送者类型:1-用户,2-AI',
    content_type TINYINT NOT NULL COMMENT '内容类型:1-文本,2-文件,3-图片',
    content TEXT NOT NULL COMMENT '消息内容',
    file_urls JSON COMMENT '文件URL数组(如果有)',
    metadata JSON COMMENT '文件元信息(原文件名、大小等)',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_conversation_id (conversation_id),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对话消息表';

-- =============================================
-- PRD文档表（按技术方案设计：内容存储在云存储）
-- =============================================
CREATE TABLE IF NOT EXISTS prd_document (
    id VARCHAR(64) NOT NULL COMMENT 'PRD文档ID',
    conversation_id VARCHAR(64) NOT NULL COMMENT '对话ID',
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    title VARCHAR(200) NOT NULL COMMENT '文档标题',
    file_name VARCHAR(255) NOT NULL COMMENT '文档文件名',
    file_url VARCHAR(500) NOT NULL COMMENT '云存储文档URL',
    file_size BIGINT DEFAULT 0 COMMENT '文件大小(字节)',
    version INT NOT NULL DEFAULT 1 COMMENT '版本号',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:1-草稿,2-已发布',
    share_token VARCHAR(64) COMMENT '分享token',
    share_expire_time DATETIME COMMENT '分享过期时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_user VARCHAR(64) COMMENT '创建人',
    update_user VARCHAR(64) COMMENT '修改人',
    nonce BIGINT NOT NULL DEFAULT 0 COMMENT '乐观锁',
    PRIMARY KEY (id),
    UNIQUE KEY uk_conversation_id (conversation_id),
    KEY idx_user_id (user_id),
    KEY idx_share_token (share_token),
    KEY idx_file_name (file_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PRD文档表';

-- =============================================
-- PRD版本历史表
-- =============================================
CREATE TABLE IF NOT EXISTS prd_version_history (
    id VARCHAR(64) NOT NULL COMMENT '版本ID',
    prd_document_id VARCHAR(64) NOT NULL COMMENT 'PRD文档ID',
    version INT NOT NULL COMMENT '版本号',
    file_name VARCHAR(255) NOT NULL COMMENT '历史版本文件名',
    file_url VARCHAR(500) NOT NULL COMMENT '历史版本文档URL',
    file_size BIGINT DEFAULT 0 COMMENT '文件大小(字节)',
    change_log TEXT COMMENT '变更日志',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user VARCHAR(64) COMMENT '创建人',
    PRIMARY KEY (id),
    KEY idx_prd_document_id (prd_document_id),
    KEY idx_version (version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PRD版本历史表';

-- =============================================
-- 架构设计文档表
-- =============================================
CREATE TABLE IF NOT EXISTS architecture_document (
    id VARCHAR(64) NOT NULL COMMENT '架构设计文档ID',
    conversation_id VARCHAR(64) NOT NULL COMMENT '对话ID',
    prd_id VARCHAR(64) NOT NULL COMMENT 'PRD文档ID',
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    title VARCHAR(200) NOT NULL COMMENT '文档标题',
    content LONGTEXT NOT NULL COMMENT '架构设计文档内容(Markdown格式)',
    version INT NOT NULL DEFAULT 1 COMMENT '版本号',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态:1-草稿,2-已发布',
    share_token VARCHAR(64) COMMENT '分享token',
    share_expire_time DATETIME COMMENT '分享过期时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_user VARCHAR(64) COMMENT '创建人',
    update_user VARCHAR(64) COMMENT '修改人',
    nonce BIGINT NOT NULL DEFAULT 0 COMMENT '乐观锁',
    PRIMARY KEY (id),
    UNIQUE KEY uk_prd_id (prd_id),
    KEY idx_conversation_id (conversation_id),
    KEY idx_user_id (user_id),
    KEY idx_share_token (share_token),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='架构设计文档表';

-- =============================================
-- 插入测试数据（仅用于开发测试）
-- =============================================

-- 插入测试对话
INSERT IGNORE INTO conversation (
    id, user_id, title, status, confidence_score, create_user, update_user
) VALUES (
    'test_conv_001', 'test_user_001', '测试对话-电商系统需求', 1, 85, 'system', 'system'
);

-- 插入测试消息
INSERT IGNORE INTO conversation_message (
    id, conversation_id, sender_type, content_type, content
) VALUES 
('test_msg_001', 'test_conv_001', 1, 1, '我想开发一个电商系统，需要用户注册登录、商品管理、订单管理等功能'),
('test_msg_002', 'test_conv_001', 2, 1, '好的，我来帮您分析电商系统的需求。请问您的目标用户群体是什么？预计的订单量级大概是多少？'); 