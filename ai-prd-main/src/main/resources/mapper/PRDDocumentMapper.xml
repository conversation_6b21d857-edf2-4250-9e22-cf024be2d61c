<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.ai.main.dao.PRDDocumentDao">

    <!-- 结果映射 -->
    <resultMap id="PRDDocumentResultMap" type="com.yeepay.ai.main.entity.PRDDocument">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="conversation_id" property="conversationId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="LONGVARCHAR"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="share_token" property="shareToken" jdbcType="VARCHAR"/>
        <result column="share_expire_time" property="shareExpireTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="nonce" property="nonce" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, conversation_id, user_id, title, content, version, status,
        share_token, share_expire_time,
        create_time, update_time, create_user, update_user, nonce
    </sql>

    <!-- 不包含文件内容的字段列表（用于列表查询） -->
    <sql id="List_Column_List">
        id, conversation_id, user_id, title, version, status,
        share_token, share_expire_time,
        create_time, update_time, create_user, update_user, nonce
    </sql>

    <!-- 条件查询 -->
    <sql id="Base_Where_Clause">
        <where>
            <if test="id != null and id != ''">
                AND id = #{id}
            </if>
            <if test="conversationId != null and conversationId != ''">
                AND conversation_id = #{conversationId}
            </if>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
        </where>
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.String" resultMap="PRDDocumentResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM prd_document
        WHERE id = #{id}
    </select>

    <!-- 根据对话ID查询PRD文档 -->
    <select id="selectByConversationId" parameterType="java.lang.String" resultMap="PRDDocumentResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM prd_document
        WHERE conversation_id = #{conversationId} AND status != 3
    </select>

    <!-- 根据用户ID查询PRD文档列表 -->
    <select id="selectByUserId" parameterType="java.lang.String" resultMap="PRDDocumentResultMap">
        SELECT 
        <include refid="List_Column_List"/>
        FROM prd_document
        WHERE user_id = #{userId} AND status != 3
        ORDER BY update_time DESC
    </select>

    <!-- 根据分享Token查询PRD文档 -->
    <select id="selectByShareToken" parameterType="java.lang.String" resultMap="PRDDocumentResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM prd_document
        WHERE share_token = #{shareToken} 
        AND status != 3
        AND (share_expire_time IS NULL OR share_expire_time > NOW())
    </select>

    <!-- 条件查询 -->
    <select id="selectByCondition" parameterType="com.yeepay.ai.main.entity.PRDDocument" resultMap="PRDDocumentResultMap">
        SELECT 
        <include refid="List_Column_List"/>
        FROM prd_document
        <include refid="Base_Where_Clause"/>
        ORDER BY update_time DESC
    </select>

    <!-- 分页查询 -->
    <select id="selectByPage" resultMap="PRDDocumentResultMap">
        SELECT 
        <include refid="List_Column_List"/>
        FROM prd_document
        <where>
            <if test="params.userId != null and params.userId != ''">
                AND user_id = #{params.userId}
            </if>
            <if test="params.status != null">
                AND status = #{params.status}
            </if>
            <if test="params.title != null and params.title != ''">
                AND title LIKE CONCAT('%', #{params.title}, '%')
            </if>
        </where>
        ORDER BY update_time DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 统计用户PRD文档数量 -->
    <select id="countByUserId" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM prd_document
        WHERE user_id = #{userId} AND status != 3
    </select>

    <!-- 统计用户指定状态的PRD文档数量 -->
    <select id="countByUserIdAndStatus" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM prd_document
        WHERE user_id = #{userId} AND status = #{status}
    </select>

    <!-- 检查对话是否已有PRD文档 -->
    <select id="existsByConversationId" parameterType="java.lang.String" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM prd_document
        WHERE conversation_id = #{conversationId} AND status != 3
    </select>

    <!-- 插入PRD文档 -->
    <insert id="insert" parameterType="com.yeepay.ai.main.entity.PRDDocument">
        INSERT INTO prd_document (
            id, conversation_id, user_id, title, content, version, status,
            share_token, share_expire_time,
            create_time, update_time, create_user, update_user, nonce
        ) VALUES (
            #{id}, #{conversationId}, #{userId}, #{title}, #{content}, #{version}, #{status},
            #{shareToken}, #{shareExpireTime},
            #{createTime}, #{updateTime}, #{createUser}, #{updateUser}, #{nonce}
        )
    </insert>

    <!-- 批量插入 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO prd_document (
            id, conversation_id, user_id, title, content, version, status,
            share_token, share_expire_time,
            create_time, update_time, create_user, update_user, nonce
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.conversationId}, #{item.userId}, #{item.title}, #{item.content}, 
             #{item.version}, #{item.status}, #{item.shareToken}, #{item.shareExpireTime},
             #{item.createTime}, #{item.updateTime}, #{item.createUser}, #{item.updateUser}, #{item.nonce})
        </foreach>
    </insert>

    <!-- 自定义更新PRD文档（带乐观锁） -->
    <update id="updateByIdWithOptimisticLock" parameterType="com.yeepay.ai.main.entity.PRDDocument">
        UPDATE prd_document
        <set>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="shareToken != null">
                share_token = #{shareToken},
            </if>
            <if test="shareExpireTime != null">
                share_expire_time = #{shareExpireTime},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
            update_time = NOW(),
            nonce = nonce + 1
        </set>
        WHERE id = #{id} AND nonce = #{nonce}
    </update>

    <!-- 更新分享设置 -->
    <update id="updateShareSettings">
        UPDATE prd_document
        SET share_token = #{shareToken},
            share_expire_time = #{shareExpireTime},
            update_time = NOW(),
            update_user = #{updateUser},
            nonce = nonce + 1
        WHERE id = #{prdId} AND nonce = #{nonce}
    </update>

    <!-- 软删除PRD文档 -->
    <update id="deleteById">
        UPDATE prd_document
        SET status = 3, 
            update_time = NOW(),
            update_user = #{updateUser},
            nonce = nonce + 1
        WHERE id = #{prdId} AND nonce = #{nonce}
    </update>

    <!-- 物理删除PRD文档 -->
    <delete id="deleteByIdPhysical" parameterType="java.lang.String">
        DELETE FROM prd_document WHERE id = #{id}
    </delete>

    <!-- 批量删除 -->
    <update id="deleteBatch">
        UPDATE prd_document
        SET status = 3, 
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 检查PRD文档是否存在 -->
    <select id="existsById" parameterType="java.lang.String" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM prd_document
        WHERE id = #{id}
    </select>

    <!-- 检查用户PRD文档权限 -->
    <select id="checkUserPermission" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM prd_document
        WHERE id = #{prdId} AND user_id = #{userId} AND status != 3
    </select>

    <!-- 获取最新发布的PRD文档 -->
    <select id="selectRecentPublishedPRDs" resultMap="PRDDocumentResultMap">
        SELECT
        <include refid="List_Column_List"/>
        FROM prd_document
        WHERE status = 2
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 搜索PRD文档 -->
    <select id="searchPRDs" resultMap="PRDDocumentResultMap">
        SELECT 
        <include refid="List_Column_List"/>
        FROM prd_document
        WHERE status != 3
        <if test="keyword != null and keyword != ''">
            AND title LIKE CONCAT('%', #{keyword}, '%')
        </if>
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        ORDER BY update_time DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 统计搜索结果数量 -->
    <select id="countSearchResults" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM prd_document
        WHERE status != 3
        <if test="keyword != null and keyword != ''">
            AND title LIKE CONCAT('%', #{keyword}, '%')
        </if>
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
    </select>

    <!-- 获取PRD文档统计信息 -->
    <select id="getPRDStats" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT
            COUNT(*) as total_count,
            COUNT(CASE WHEN status = 1 THEN 1 END) as draft_count,
            COUNT(CASE WHEN status = 2 THEN 1 END) as published_count,
            MIN(create_time) as first_prd_time,
            MAX(update_time) as last_update_time
        FROM prd_document
        WHERE user_id = #{userId} AND status != 3
    </select>

    <!-- 按日期统计PRD创建数量 -->
    <select id="getPRDCountByDate" resultType="java.util.Map">
        SELECT 
            DATE(create_time) as date,
            COUNT(*) as count,
            COUNT(CASE WHEN status = 1 THEN 1 END) as draft_count,
            COUNT(CASE WHEN status = 2 THEN 1 END) as published_count
        FROM prd_document
        WHERE user_id = #{userId} 
        AND create_time >= #{startDate}
        AND create_time &lt;= #{endDate}
        AND status != 3
        GROUP BY DATE(create_time)
        ORDER BY date DESC
    </select>

    <!-- 清理过期的分享Token -->
    <update id="cleanExpiredShareTokens">
        UPDATE prd_document
        SET share_token = NULL,
            update_time = NOW()
        WHERE share_expire_time IS NOT NULL
          AND share_expire_time &lt; NOW()
    </update>

    <!-- 获取即将过期的分享链接 -->
    <select id="selectExpiringShares" resultMap="PRDDocumentResultMap">
        SELECT
        <include refid="List_Column_List"/>
        FROM prd_document
        WHERE share_expire_time IS NOT NULL
        AND share_expire_time > NOW()
        AND share_expire_time &lt;= DATE_ADD(NOW(), INTERVAL #{days} DAY)
        ORDER BY share_expire_time ASC
    </select>

</mapper> 