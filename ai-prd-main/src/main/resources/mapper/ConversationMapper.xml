<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.ai.main.dao.ConversationDao">

    <!-- 结果映射 -->
    <resultMap id="ConversationResultMap" type="com.yeepay.ai.main.entity.Conversation">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="confidence_score" property="confidenceScore" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="nonce" property="nonce" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, title, status, confidence_score, create_time, update_time, create_user, update_user, nonce
    </sql>

    <!-- 条件查询 -->
    <sql id="Base_Where_Clause">
        <where>
            <if test="id != null and id != ''">
                AND id = #{id}
            </if>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="confidenceScore != null">
                AND confidence_score = #{confidenceScore}
            </if>
        </where>
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.String" resultMap="ConversationResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation
        WHERE id = #{id}
    </select>

    <!-- 根据用户ID查询对话列表 -->
    <select id="selectByUserId" parameterType="java.lang.String" resultMap="ConversationResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation
        WHERE user_id = #{userId} AND status != 3
        ORDER BY update_time DESC
    </select>

    <!-- 根据用户ID和状态查询对话列表 -->
    <select id="selectByUserIdAndStatus" resultMap="ConversationResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation
        WHERE user_id = #{userId} AND status = #{status}
        ORDER BY update_time DESC
    </select>

    <!-- 条件查询 -->
    <select id="selectByCondition" parameterType="com.yeepay.ai.main.entity.Conversation" resultMap="ConversationResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation
        <include refid="Base_Where_Clause"/>
        ORDER BY update_time DESC
    </select>

    <!-- 分页查询 -->
    <select id="selectByPage" resultMap="ConversationResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation
        <where>
            <if test="params.userId != null and params.userId != ''">
                AND user_id = #{params.userId}
            </if>
            <if test="params.status != null">
                AND status = #{params.status}
            </if>
            <if test="params.title != null and params.title != ''">
                AND title LIKE CONCAT('%', #{params.title}, '%')
            </if>
        </where>
        ORDER BY update_time DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 统计对话数量 -->
    <select id="countByUserId" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM conversation
        WHERE user_id = #{userId} AND status != 3
    </select>

    <!-- 统计用户状态对话数量 -->
    <select id="countByUserIdAndStatus" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM conversation
        WHERE user_id = #{userId} AND status = #{status}
    </select>

    <!-- 插入对话 -->
    <insert id="insert" parameterType="com.yeepay.ai.main.entity.Conversation">
        INSERT INTO conversation (
            id, user_id, title, status, confidence_score, 
            create_time, update_time, create_user, update_user, nonce
        ) VALUES (
            #{id}, #{userId}, #{title}, #{status}, #{confidenceScore},
            #{createTime}, #{updateTime}, #{createUser}, #{updateUser}, #{nonce}
        )
    </insert>

    <!-- 批量插入 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO conversation (
            id, user_id, title, status, confidence_score, 
            create_time, update_time, create_user, update_user, nonce
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.userId}, #{item.title}, #{item.status}, #{item.confidenceScore},
             #{item.createTime}, #{item.updateTime}, #{item.createUser}, #{item.updateUser}, #{item.nonce})
        </foreach>
    </insert>



    <!-- 更新信心指数 -->
    <update id="updateConfidenceScore">
        UPDATE conversation
        SET confidence_score = #{confidenceScore}, 
            update_time = NOW(),
            nonce = nonce + 1
        WHERE id = #{conversationId} AND nonce = #{nonce}
    </update>

    <!-- 更新标题 -->
    <update id="updateTitle">
        UPDATE conversation
        SET title = #{title}, 
            update_time = NOW(),
            update_user = #{updateUser},
            nonce = nonce + 1
        WHERE id = #{conversationId} AND nonce = #{nonce}
    </update>

    <!-- 软删除对话 -->
    <update id="deleteById">
        UPDATE conversation
        SET status = 3, 
            update_time = NOW(),
            update_user = #{updateUser},
            nonce = nonce + 1
        WHERE id = #{conversationId} AND nonce = #{nonce}
    </update>

    <!-- 物理删除对话 -->
    <delete id="deleteByIdPhysical" parameterType="java.lang.String">
        DELETE FROM conversation WHERE id = #{id}
    </delete>

    <!-- 批量删除 -->
    <update id="deleteBatch">
        UPDATE conversation
        SET status = 3, 
            update_time = NOW(),
            update_user = #{updateUser}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 获取高信心指数的对话 -->
    <select id="selectHighConfidenceConversations" resultMap="ConversationResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation
        WHERE confidence_score >= #{minConfidence} AND status != 3
        ORDER BY confidence_score DESC, update_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询用户最近的对话 -->
    <select id="selectRecentConversations" resultMap="ConversationResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation
        WHERE user_id = #{userId} AND status != 3
        ORDER BY update_time DESC
        LIMIT #{limit}
    </select>

    <!-- 检查对话是否存在 -->
    <select id="existsById" parameterType="java.lang.String" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM conversation
        WHERE id = #{id}
    </select>

    <!-- 检查用户对话权限 -->
    <select id="checkUserPermission" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM conversation
        WHERE id = #{conversationId} AND user_id = #{userId} AND status != 3
    </select>

    <!-- 统计查询 -->
    <select id="getConversationStats" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_count,
            COUNT(CASE WHEN status = 1 THEN 1 END) as in_progress_count,
            COUNT(CASE WHEN status = 2 THEN 1 END) as completed_count,
            AVG(confidence_score) as avg_confidence_score,
            MAX(confidence_score) as max_confidence_score,
            MIN(confidence_score) as min_confidence_score
        FROM conversation
        WHERE user_id = #{userId} AND status != 3
    </select>

    <!-- 按日期统计对话创建数量 -->
    <select id="getConversationCountByDate" resultType="java.util.Map">
        SELECT 
            DATE(create_time) as date,
            COUNT(*) as count
        FROM conversation
        WHERE user_id = #{userId} 
        AND create_time >= #{startDate}
        AND create_time &lt;= #{endDate}
        AND status != 3
        GROUP BY DATE(create_time)
        ORDER BY date DESC
    </select>

</mapper> 