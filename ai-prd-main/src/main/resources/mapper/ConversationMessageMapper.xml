<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeepay.ai.main.dao.ConversationMessageDao">

    <!-- 结果映射 -->
    <resultMap id="ConversationMessageResultMap" type="com.yeepay.ai.main.entity.ConversationMessage">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="conversation_id" property="conversationId" jdbcType="VARCHAR"/>
        <result column="sender_type" property="senderType" jdbcType="TINYINT"/>
        <result column="content_type" property="contentType" jdbcType="TINYINT"/>
        <result column="content" property="content" jdbcType="LONGVARCHAR"/>
        <result column="file_urls" property="fileUrls" jdbcType="VARCHAR" 
                typeHandler="com.yeepay.ai.main.common.handler.JsonTypeHandler"/>
        <result column="metadata" property="metadata" jdbcType="VARCHAR" 
                typeHandler="com.yeepay.ai.main.common.handler.JsonTypeHandler"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, conversation_id, sender_type, content_type, content, file_urls, metadata, create_time
    </sql>

    <!-- 条件查询 -->
    <sql id="Base_Where_Clause">
        <where>
            <if test="id != null and id != ''">
                AND id = #{id}
            </if>
            <if test="conversationId != null and conversationId != ''">
                AND conversation_id = #{conversationId}
            </if>
            <if test="senderType != null">
                AND sender_type = #{senderType}
            </if>
            <if test="contentType != null">
                AND content_type = #{contentType}
            </if>
        </where>
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.String" resultMap="ConversationMessageResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation_message
        WHERE id = #{id}
    </select>

    <!-- 根据对话ID查询消息列表 -->
    <select id="selectByConversationId" parameterType="java.lang.String" resultMap="ConversationMessageResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation_message
        WHERE conversation_id = #{conversationId}
        ORDER BY create_time ASC
    </select>

    <!-- 根据对话ID和发送者类型查询消息列表 -->
    <select id="selectByConversationIdAndSenderType" resultMap="ConversationMessageResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation_message
        WHERE conversation_id = #{conversationId} AND sender_type = #{senderType}
        ORDER BY create_time ASC
    </select>

    <!-- 条件查询 -->
    <select id="selectByCondition" parameterType="com.yeepay.ai.main.entity.ConversationMessage" resultMap="ConversationMessageResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation_message
        <include refid="Base_Where_Clause"/>
        ORDER BY create_time ASC
    </select>

    <!-- 分页查询消息 -->
    <select id="selectByPage" resultMap="ConversationMessageResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation_message
        <where>
            <if test="params.conversationId != null and params.conversationId != ''">
                AND conversation_id = #{params.conversationId}
            </if>
            <if test="params.senderType != null">
                AND sender_type = #{params.senderType}
            </if>
            <if test="params.contentType != null">
                AND content_type = #{params.contentType}
            </if>
        </where>
        ORDER BY create_time ASC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 统计对话消息数量 -->
    <select id="countByConversationId" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM conversation_message
        WHERE conversation_id = #{conversationId}
    </select>

    <!-- 统计对话消息数量（按发送者类型） -->
    <select id="countByConversationIdAndSenderType" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM conversation_message
        WHERE conversation_id = #{conversationId} AND sender_type = #{senderType}
    </select>

    <!-- 获取对话的最后一条消息 -->
    <select id="selectLastMessageByConversationId" parameterType="java.lang.String" resultMap="ConversationMessageResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation_message
        WHERE conversation_id = #{conversationId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 获取对话的最后几条消息 -->
    <select id="selectRecentMessages" resultMap="ConversationMessageResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation_message
        WHERE conversation_id = #{conversationId}
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 分页查询对话消息列表（按时间正序，最新消息在下） -->
    <select id="selectByConversationIdWithPage" resultMap="ConversationMessageResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation_message
        WHERE conversation_id = #{conversationId}
        ORDER BY create_time ASC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 插入消息 -->
    <insert id="insert" parameterType="com.yeepay.ai.main.entity.ConversationMessage">
        INSERT INTO conversation_message (
            id, conversation_id, sender_type, content_type, content, file_urls, metadata, create_time
        ) VALUES (
            #{id}, #{conversationId}, #{senderType}, #{contentType}, #{content}, 
            #{fileUrls}, 
            #{metadata}, 
            #{createTime}
        )
    </insert>

    <!-- 批量插入消息 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO conversation_message (
            id, conversation_id, sender_type, content_type, content, file_urls, metadata, create_time
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.conversationId}, #{item.senderType}, #{item.contentType}, #{item.content},
             #{item.fileUrls}, 
             #{item.metadata}, 
             #{item.createTime})
        </foreach>
    </insert>

    <!-- 更新消息 -->
    <update id="updateById" parameterType="com.yeepay.ai.main.entity.ConversationMessage">
        UPDATE conversation_message
        <set>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="fileUrls != null">
                file_urls = #{fileUrls},
            </if>
            <if test="metadata != null">
                metadata = #{metadata},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除消息 -->
    <delete id="deleteById" parameterType="java.lang.String">
        DELETE FROM conversation_message WHERE id = #{id}
    </delete>

    <!-- 删除对话的所有消息 -->
    <delete id="deleteByConversationId" parameterType="java.lang.String">
        DELETE FROM conversation_message WHERE conversation_id = #{conversationId}
    </delete>

    <!-- 批量删除消息 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        DELETE FROM conversation_message WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 检查消息是否存在 -->
    <select id="existsById" parameterType="java.lang.String" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM conversation_message
        WHERE id = #{id}
    </select>

    <!-- 获取包含文件的消息 -->
    <select id="selectMessagesWithFiles" parameterType="java.lang.String" resultMap="ConversationMessageResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation_message
        WHERE conversation_id = #{conversationId} 
        AND (content_type = 2 OR content_type = 3 OR file_urls IS NOT NULL)
        ORDER BY create_time ASC
    </select>

    <!-- 搜索消息内容 -->
    <select id="searchMessages" resultMap="ConversationMessageResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation_message
        WHERE conversation_id = #{conversationId}
        AND content LIKE CONCAT('%', #{keyword}, '%')
        ORDER BY create_time ASC
    </select>

    <!-- 获取消息统计信息 -->
    <select id="getMessageStats" parameterType="java.lang.String" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_count,
            COUNT(CASE WHEN sender_type = 1 THEN 1 END) as user_message_count,
            COUNT(CASE WHEN sender_type = 2 THEN 1 END) as ai_message_count,
            COUNT(CASE WHEN content_type = 1 THEN 1 END) as text_message_count,
            COUNT(CASE WHEN content_type = 2 THEN 1 END) as file_message_count,
            COUNT(CASE WHEN content_type = 3 THEN 1 END) as image_message_count,
            MIN(create_time) as first_message_time,
            MAX(create_time) as last_message_time
        FROM conversation_message
        WHERE conversation_id = #{conversationId}
    </select>

    <!-- 获取指定时间范围内的消息 -->
    <select id="selectMessagesByTimeRange" resultMap="ConversationMessageResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation_message
        WHERE conversation_id = #{conversationId}
        AND create_time >= #{startTime}
        AND create_time &lt;= #{endTime}
        ORDER BY create_time ASC
    </select>

    <!-- 获取对话中的用户消息（用于AI分析） -->
    <select id="selectUserMessages" parameterType="java.lang.String" resultMap="ConversationMessageResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation_message
        WHERE conversation_id = #{conversationId} AND sender_type = 1
        ORDER BY create_time ASC
    </select>

    <!-- 获取对话中的AI消息 -->
    <select id="selectAiMessages" parameterType="java.lang.String" resultMap="ConversationMessageResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM conversation_message
        WHERE conversation_id = #{conversationId} AND sender_type = 2
        ORDER BY create_time ASC
    </select>

    <!-- 统计消息字符数 -->
    <select id="getTotalContentLength" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT COALESCE(SUM(CHAR_LENGTH(content)), 0)
        FROM conversation_message
        WHERE conversation_id = #{conversationId}
    </select>

    <!-- 按日期统计消息数量 -->
    <select id="getMessageCountByDate" resultType="java.util.Map">
        SELECT 
            DATE(create_time) as date,
            COUNT(*) as count,
            COUNT(CASE WHEN sender_type = 1 THEN 1 END) as user_count,
            COUNT(CASE WHEN sender_type = 2 THEN 1 END) as ai_count
        FROM conversation_message
        WHERE conversation_id = #{conversationId}
        AND create_time >= #{startDate}
        AND create_time &lt;= #{endDate}
        GROUP BY DATE(create_time)
        ORDER BY date ASC
    </select>

</mapper> 