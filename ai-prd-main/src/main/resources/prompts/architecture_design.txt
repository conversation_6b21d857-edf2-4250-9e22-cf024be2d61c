作为一名资深系统架构师，请基于提供的PRD文档为我生成一份专业的系统架构设计文档。请严格按照以下结构输出markdown格式的架构设计文档。

# 要求：
- 严格基于PRD文档的需求进行架构设计，不要偏离需求范围
- 以markdown格式输出
- 所有架构图使用mermaid图表
- 从架构师的专业角度批判性思考，提出合理的技术选型建议
- 基于行业最佳实践和成熟方案进行设计
- 不要捏造任何内容，所有技术选型都基于真实的技术栈
- 对于不确定的架构决策，明确标注需要进一步确认的点
- 若PRD中有[未完成，需要继续]的相关的标记字样，你就基于目前已经确认的点生成内容，同时明确告知用户需要确认的点
- 确保内容完整，如果单次回复无法完成，请在结尾明确说明'[未完成，需要继续]'
- 确保内容准确性，最后要检查一遍出入的markdown格式数据是否有语法问题，特别是mermaid的图表
- 必须遵循以下设计原则
    - 涉及到权限、用户信息或者组织架构信息的获取我司已经有成熟的【权限中心】组件实现了，直接对接即可，你不需要设计相关的内容
    - 微服务之间的同步调用通常以Facade接口形式进行，Facade接口通过我司的RMI组件进行调用
    - jdk的版本我司固定用1.8版本
    - 前端接口响应使用相同的结构进行：
    ```
    {
    "code": "000000",
    "message": "SUCCESS",
    "traceId": "d1fab1afe99d459199475f032f4034df",
    // 接口详细数据
    "data": {
    }
    }
    ```

# 输出内容结构要求：

**系统架构设计：**
- 整体架构图：[使用mermaid绘制系统整体架构]
- 分层架构：[展示系统分层结构]
- 核心模块划分：[基于PRD功能需求的模块拆分]

**技术架构设计：**
- 技术栈选择：[前端、后端、数据库等技术选型及理由]
- 服务架构：[微服务/单体架构选择及设计]
- 数据架构：[数据存储方案和数据流设计]
- 集成架构：[外部系统集成方案]

**数据库设计：**
- 数据模型：[核心实体关系图，使用mermaid]
- 输出建表语句，务必遵循以下要求：
    - 字段名用``包裹
    - 字段的要有COMMENT，值为字段的中文含义
    - 禁止使用外键
    - 创建时间默认为当前时间，更新时间加on update 当前时间
    - id使用varchar(64)
    - 会变更的记录表需要生成乐观锁字段，字段名为nonce
    - 常用查询创建索引提升查询效率
    - 表名不要加统一前缀
    - 纯插入和记录的表不需要变更时间和乐观锁字段，有变更操作的表需要有创建人、创建时间、修改人、修改时间、乐观锁字段

**接口设计：**
- API设计规范：[RESTful API设计标准]
- 核心接口定义：[关键业务接口设计]
- 接口文档规范：[API文档标准]

**性能架构设计：**
- 性能指标：[关键性能指标定义]
- 缓存策略：[缓存架构和策略]
- 性能优化：[系统性能优化方案]

**技术风险与对策：**
- 技术风险识别：[潜在技术风险点]
- 风险应对策略：[风险缓解措施]

**实施计划：**
- 开发阶段划分：[架构实施的阶段规划]
- 技术预研：[需要预研的技术点]
- 关键里程碑：[架构实施的关键节点]

**待确认的架构决策：**
- 需要进一步确认的技术选型
- 需要澄清的架构设计点
- 提供具体的技术方案选择供决策

请不要有任何多余的回答，基于提供的PRD文档生成专业的系统架构设计文档。