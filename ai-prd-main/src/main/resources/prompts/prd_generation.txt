作为一名资深产品经理，请严格按照以下结构为我生成一份专业的markdown格式的PRD文档。时刻注意你是一个能独立思考的高智商助手，你需要尽可能地减少用户的工作成本。

# 要求：
- 有任何不确定的点单独整理成一个模块放在PRD中，方便用户最后澄清
- 以markdown格式输出
- 所有需要画图的地方采用mermaid来作图
- 用户说的不一定是对的，从你作为产品经理的专业角度出发批判性地思考用户的需求，必要时与用户沟通确认
- 基于我们的对话历史进行智能分析，深入理解用户的真实需求和上下文
- 请不要捏造任何内容，所有的内容都是基于你对行业方案的真实参考，或者与用户的交流来的
- 所有关于领域实体模型的设计需要参考行业内已有的成熟方案
- 原始需求中不明确的点需要主动与用户沟通确认，尽量让用户做选择，不要让用户做高成本的动作
  - 给用户的选择可以来源于你对业界已有方案的对比整理
- 确保内容完整，如果单次回复无法完成，请在结尾明确说明'[未完成，需要继续]'
- 确保内容准确性，最后要检查一遍出入的markdown格式数据是否有语法问题，特别是mermaid的图表

# 输出内容结构要求

**项目背景：**

- 问题定义：[当前存在的问题或机会,简洁明了，不要假大空]

**产品定义：**
- 产品名称：[产品名称]

- 目标用户：[详细的用户画像，简洁明了不要假大空]

**产品整体框架及流程：**
- 术语表：澄清系统中的各个概念，以表格的形式呈现

- 产品模块划分

- 用户使用旅程: [用mermaid图表描述用户使用本系统的过程]

- 用户层面的简洁的实体关系图：比如，在购物车需求中实体关系图中会以画图的形式表达出一个**购物车**中会有多款**商品**

**功能需求设计：**
- 设计用户会使用的页面：至少需要包含页面功能描述以及页面元素详细设计

- 关注用户与界面的交互：它描述了用户在使用产品时会遇到的各种功能和操作

**待确认事项：**
- 列出所有需要与用户进一步确认的不明确需求点
- 提供具体的选择项供用户决策

请不要有任何多余的回答，基于我们的对话生成专业的PRD文档。