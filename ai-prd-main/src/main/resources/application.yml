spring:
  application:
    name: ai-prd
  profiles:
    active: dev
  
  # <PERSON>配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # 数据库配置
  datasource:
    dynamic:
      strict: false
      primary: master
      datasource:
        master:
          type: com.yeepay.g3.utils.common.datasource.impl.DruidPooledDataSource
          poolName: AI_PRD
# MyBatis Plus配置
mybatis-plus:
  configuration:
    # 驼峰转下划线
    map-underscore-to-camel-case: true
    # 开发环境打印SQL日志，生产环境关闭
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 主键生成策略
      id-type: assign_id
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  # Mapper扫描路径
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.yeepay.ai.prd.entity
---
server:
  port: 8080
  servlet:
    context-path: /ai-prd
logging:
  config: /apps/commoncfg/log4j2.xml

---
server:
  port: 8066
  tomcat:
    remoteip:
      port-header: X-Forwarded-Port
      protocol-header: X-Forwarded-Proto
      remote-ip-header: X-Forwarded-For
    uri-encoding: UTF-8
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
logging:
  config: classpath:log4j2.xml
spring:
  config:
    activate:
      on-profile: dev
  # 解决SpringFox与Spring Boot 2.6+版本兼容性问题
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
# Swagger配置
springfox:
  documentation:
    swagger-ui:
      enabled: true
    enabled: true
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: ALWAYS