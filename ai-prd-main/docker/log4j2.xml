<?xml version="1.0" encoding="UTF-8"?>
<Configuration
        packages="com.yeepay.g3.utils"><!--packages参数告诉log4j2还需要额外加载哪个包下的Log4j plugin，其中YeepayMessagePatternConverter即为定制的plugin,负责输出的日志带GUID -->

    <Appenders>
        <FluentAppender name="ai-prd" label="" host="app.logsync.yp" port="24324"
                        mode="remote"> <!-- 如果上容器或者新的有appname构建方式，label项无效 -->
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} - %c -%-4r [%t] %-5p %Y %x - %msg%n%throwable"/>
        </FluentAppender>
    </Appenders>

    <Loggers>
        <Logger name="com.yeepay.g3" level="INFO"/>

        <Root level="WARN">
            <AppenderRef ref="ai-prd"/>
        </Root>
    </Loggers>
</Configuration>