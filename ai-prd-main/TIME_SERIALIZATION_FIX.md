# 时间序列化修复说明

## 问题描述
对话列表查询接口的时间类型数据没有正确序列化，导致前端接收到的时间格式不统一或不可读。

## 问题原因
1. 项目中使用了 `LocalDateTime` 类型，但没有配置统一的JSON序列化格式
2. 各个地方创建了独立的 `ObjectMapper` 实例，缺乏统一配置
3. DTO类中的时间字段缺少格式化注解

## 修复方案
1. **全局配置**：在 `application.yml` 中添加Jackson配置
2. **自定义配置类**：创建 `JacksonConfig` 统一时间序列化格式  
3. **DTO注解**：为所有DTO中的时间字段添加 `@JsonFormat` 注解

## 修改内容

### 1. application.yml 配置
```yaml
spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
```

### 2. 全局Jackson配置类
创建 `JacksonConfig.java` 类，配置统一的时间序列化器：
- 使用 `yyyy-MM-dd HH:mm:ss` 格式
- 禁用时间戳序列化
- 支持中国时区 GMT+8

### 3. DTO类更新
为以下DTO类的时间字段添加 `@JsonFormat` 注解：
- `ConversationDTO`：createTime, updateTime
- `MessageDTO`：createTime
- `PRDDocumentDTO`：shareExpireTime, createTime, updateTime

## 修复效果
时间字段现在会被正确序列化为易读的格式：
```json
{
  "createTime": "2024-12-25 10:30:45",
  "updateTime": "2024-12-25 14:20:15"
}
```

## 注意事项
1. 所有时间都使用 GMT+8 时区
2. 格式为 `yyyy-MM-dd HH:mm:ss`
3. 前端可以直接显示这个格式，无需额外转换
4. 配置向后兼容，不影响现有功能 