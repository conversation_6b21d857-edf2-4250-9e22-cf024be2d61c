# AI配置示例

## 配置项说明

在`ConfigEnum.AI_CONFIG`中配置AI相关信息，使用JSON格式的MAP：

```json
{
  "provider": "deepseek",
  "baseUrl": "https://api.deepseek.com/v1",
  "model": "deepseek-chat",
  "apiKey": "ENCRYPTED_YOUR_API_KEY_HERE",
  "maxTokens": 4000,
  "temperature": 0.7,
  "topP": 0.9
}
```

## 配置项说明

| 配置Key | 常量定义 | 说明 | 示例值 |
|--------|----------|------|-------|
| provider | AIConstants.CONFIG_KEYS.PROVIDER | AI提供商类型 | "deepseek", "openai", "gemini" |
| baseUrl | AIConstants.CONFIG_KEYS.BASE_URL | API基础地址 | "https://api.deepseek.com/v1" |
| model | AIConstants.CONFIG_KEYS.MODEL | 模型名称 | "deepseek-chat" |
| apiKey | AIConstants.CONFIG_KEYS.API_KEY | 加密的API密钥 | "ENCRYPTED_..." |
| maxTokens | AIConstants.CONFIG_KEYS.MAX_TOKENS | 最大token数 | 4000 |
| temperature | AIConstants.CONFIG_KEYS.TEMPERATURE | 温度参数 | 0.7 |
| topP | AIConstants.CONFIG_KEYS.TOP_P | Top-P参数 | 0.9 |

## 支持的AI提供商

| 提供商 | 常量定义 | baseUrl示例 | model示例 |
|-------|----------|-------------|-----------|
| DeepSeek | AIConstants.PROVIDERS.DEEPSEEK | https://api.deepseek.com/v1 | deepseek-chat |
| OpenAI | AIConstants.PROVIDERS.OPENAI | https://api.openai.com/v1 | gpt-3.5-turbo |
| Gemini | AIConstants.PROVIDERS.GEMINI | https://generativelanguage.googleapis.com/v1 | gemini-pro |

## API密钥加密

API密钥需要使用易宝统一配置的AES加密方式加密后存储：

```java
// 使用ConfigUtils进行解密
String decryptedKey = ConfigUtils.decryptValue(encryptedApiKey);
```

## 默认配置

如果配置项缺失，系统会使用默认值：
- provider: "deepseek"
- baseUrl: "https://api.deepseek.com/v1"  
- model: "deepseek-chat"
- maxTokens: 4000
- temperature: 0.7
- topP: 0.9 