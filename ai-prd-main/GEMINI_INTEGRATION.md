# Gemini API 集成说明

## 概述

本项目已成功集成Google Gemini API，支持通过OpenAI兼容的API端点调用Gemini模型进行智能对话和PRD文档生成。

## 功能特性

- 支持Gemini 2.5 Flash Preview模型
- 使用OpenAI兼容的API格式
- 智能默认参数配置
- 统一的AI客户端接口
- 完整的错误处理和日志记录

## 配置说明

### 1. 配置Gemini API密钥

在易宝配置管理系统中，使用 `AWESOME_GEMINI_CONFIG` 配置项：

```json
{
  "provider": "gemini",
  "baseUrl": "https://generativelanguage.googleapis.com",
  "model": "gemini-2.5-flash-preview-05-20",
  "apiKey": "encrypted_gemini_api_key_here",
  "maxTokens": 8192,
  "temperature": 0.9,
  "topP": 0.95
}
```

### 2. API密钥加密

API密钥需要使用项目标准的AES加密方式进行加密后存储。

### 3. 默认配置参数

| 参数 | 默认值 | 说明 |
|-----|--------|------|
| `provider` | "gemini" | AI提供商标识 |
| `baseUrl` | "https://generativelanguage.googleapis.com" | API基础URL |
| `model` | "gemini-2.5-flash-preview-05-20" | 默认模型 |
| `maxTokens` | 8192 | 最大响应长度 |
| `temperature` | 0.9 | 创造性参数 |
| `topP` | 0.95 | 词汇选择范围 |

## 使用方法

### 1. 通过配置切换到Gemini

修改 `AWESOME_AI_CONFIG` 配置中的 `provider` 字段为 `"gemini"`，或者创建独立的 `AWESOME_GEMINI_CONFIG` 配置。

### 2. 代码示例

```java
@Autowired
private AIClientFactory aiClientFactory;

public void testGeminiAPI() {
    // 创建Gemini客户端
    AIClient geminiClient = aiClientFactory.createClient();
    
    // 构建消息
    List<ChatMessage> messages = Arrays.asList(
        ChatMessage.system("你是一个专业的产品经理助手"),
        ChatMessage.user("请帮我分析这个需求的可行性")
    );
    
    // 调用API
    ChatResponse response = geminiClient.chat(messages);
    String reply = response.getChoices().get(0).getMessage().getContent();
    
    System.out.println("Gemini回复: " + reply);
    
    // 关闭客户端
    geminiClient.close();
}
```

## API端点信息

- **OpenAI兼容端点**: `/v1beta/openai/chat/completions`
- **支持的请求格式**: 标准OpenAI Chat Completions格式
- **认证方式**: Bearer Token（API Key）

## 错误处理

系统包含完整的错误处理机制：

1. **配置错误**: API密钥未设置或解密失败
2. **网络错误**: 连接超时、读取超时等
3. **API错误**: 请求格式错误、配额超限等
4. **解析错误**: 响应数据格式异常

所有错误都会记录详细的日志信息，便于问题排查。

## 性能优化

- **连接池管理**: 自动管理HTTP连接池
- **超时配置**: 合理的连接和读取超时设置
- **资源释放**: 自动释放客户端资源

## 注意事项

1. **API密钥安全**: 确保API密钥已正确加密存储
2. **网络访问**: 确保服务器可以访问 `generativelanguage.googleapis.com`
3. **配额管理**: 注意Gemini API的调用配额限制
4. **模型版本**: 可根据需要调整model参数使用不同版本的Gemini模型

## 测试验证

可以通过以下方式验证Gemini集成：

1. 检查日志中是否有 "Created Gemini client with model: xxx" 信息
2. 调用AI分析接口，观察是否正常返回结果
3. 查看网络请求日志，确认请求发送到正确的Gemini API端点

## 故障排除

### 常见问题

1. **"Gemini client not implemented yet"**: 确保已更新AIClientFactory实现
2. **API密钥解密失败**: 检查密钥是否正确加密
3. **网络连接失败**: 检查网络连接和防火墙设置
4. **响应解析错误**: 检查API响应格式是否符合预期

### 调试建议

1. 开启DEBUG日志级别查看详细请求/响应信息
2. 使用网络抓包工具检查实际的API调用
3. 参考官方Gemini API文档验证请求格式 