# AI PRD助手后端服务

## 项目简介

AI PRD助手是一个基于Spring Boot的后端服务系统，旨在提供AI驱动的产品需求文档（PRD）管理和协作功能。本项目采用现代化的Java技术栈，提供高效、稳定的后端服务支持。

## 技术栈

- **框架**: Spring Boot 2.7.18
- **数据库**: MySQL 8.0+
- **ORM**: MyBatis Plus 3.5.3
- **连接池**: Druid 1.2.8
- **缓存**: Redis
- **认证**: JWT + 易宝权限中心（UIA）
- **日志**: Log4j2
- **构建工具**: Maven 3.6+
- **JDK版本**: Java 8

## 项目结构

```
ai-prd/
├── ai-prd-main/                     # 主业务模块
│   ├── src/main/java/com/yeepay/ai/main/
│   │   ├── AiPrdAssistantApplication.java  # 启动类
│   │   ├── controller/              # 控制层
│   │   │   ├── BaseController.java  # 基础控制器
│   │   │   └── HealthController.java # 健康检查控制器
│   │   ├── service/                 # 业务逻辑层
│   │   │   ├── BaseService.java     # 基础服务类
│   │   │   └── UserService.java     # 用户服务类
│   │   ├── dao/                     # 数据访问层
│   │   │   ├── BaseDao.java         # 基础DAO类
│   │   │   └── UserDao.java         # 用户DAO接口
│   │   ├── entity/                  # 实体类
│   │   │   └── UserInfo.java        # 用户信息实体
│   │   ├── dto/                     # 数据传输对象
│   │   │   └── UserDto.java         # 用户DTO
│   │   ├── common/                  # 通用类
│   │   │   ├── ResponseResult.java  # 统一响应结果
│   │   │   ├── BusinessException.java # 业务异常
│   │   │   ├── UnauthorizedException.java # 未授权异常
│   │   │   └── GlobalExceptionHandler.java # 全局异常处理
│   │   ├── config/                  # 配置类
│   │   │   ├── MybatisPlusConfig.java # MyBatis Plus配置
│   │   │   └── UiaConfig.java       # UIA权限配置
│   │   └── util/                    # 工具类
│   ├── src/main/resources/
│   │   ├── application.yml          # 主配置文件
│   │   ├── log4j2.xml              # 日志配置
│   │   ├── dbconf/                 # 数据库配置
│   │   └── runtimecfg/             # 运行时配置
│   └── docker/                     # Docker相关文件
├── pom.xml                         # 根POM文件
└── README.md                       # 项目说明文档
```

## 系统架构

### 三层架构设计

本项目采用经典的三层架构模式，确保系统的可维护性和扩展性：

#### 1. 表现层（Controller Layer）
- **位置**: `com.yeepay.ai.main.controller`
- **职责**: 
  - 处理HTTP请求和响应
  - 参数验证和格式转换
  - 权限验证和用户认证
  - 调用业务逻辑层服务
- **主要组件**:
  - `BaseController`: 提供通用的控制器功能，如用户验证、权限检查等
  - `HealthController`: 健康检查接口，用于系统监控

#### 2. 业务逻辑层（Service Layer）
- **位置**: `com.yeepay.ai.main.service`
- **职责**:
  - 实现核心业务逻辑
  - 事务管理
  - 数据校验和处理
  - 调用数据访问层
- **主要组件**:
  - `BaseService`: 提供服务层通用功能，如日志记录、异常处理等
  - `UserService`: 用户相关业务逻辑处理

#### 3. 数据访问层（DAO Layer）
- **位置**: `com.yeepay.ai.main.dao`
- **职责**:
  - 数据库CRUD操作
  - SQL语句执行
  - 数据映射和转换
- **主要组件**:
  - `BaseDao`: 提供数据访问层通用功能
  - `UserDao`: 用户数据访问接口，使用MyBatis进行ORM映射

### 辅助层次

#### 实体层（Entity Layer）
- **位置**: `com.yeepay.ai.main.entity`
- **职责**: 定义数据库表对应的实体类，用于ORM映射

#### 数据传输对象层（DTO Layer）
- **位置**: `com.yeepay.ai.main.dto`
- **职责**: 定义前后端数据传输的对象，包含数据验证规则

#### 通用组件层（Common Layer）
- **位置**: `com.yeepay.ai.main.common`
- **职责**: 
  - 统一响应格式（`ResponseResult`）
  - 异常定义和处理（`BusinessException`, `GlobalExceptionHandler`）
  - 通用工具和常量

#### 配置层（Config Layer）
- **位置**: `com.yeepay.ai.main.config`
- **职责**: 
  - 框架配置（MyBatis Plus、Redis等）
  - 第三方服务配置（UIA权限中心）
  - 自定义配置类

### 架构优势

1. **职责清晰**: 每一层都有明确的职责划分，避免代码混乱
2. **松耦合**: 层与层之间通过接口进行交互，降低耦合度
3. **易维护**: 分层设计使得代码更容易理解和维护
4. **可扩展**: 可以独立地对各层进行扩展和优化
5. **可测试**: 每一层都可以独立进行单元测试

## 快速开始

### 环境要求

- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 启动步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd ai-prd
   ```

2. **配置数据库**
   - 创建MySQL数据库
   - 修改 `ai-prd-main/src/main/resources/dbconf/AI_PRD.properties` 中的数据库连接信息

3. **配置Redis**
   - 修改 `ai-prd-main/src/main/resources/runtimecfg/spring-redis-conf.properties` 中的Redis连接信息

4. **编译项目**
   ```bash
   mvn clean compile
   ```

5. **启动应用**
   ```bash
   cd ai-prd-main
   mvn spring-boot:run
   ```

### 健康检查

启动成功后，可以通过以下接口验证服务状态：

- 基础健康检查: `GET http://localhost:8080/api/health/check`
- 权限验证检查: `GET http://localhost:8080/api/health/auth-check`

## 开发规范

### 代码规范

1. **包命名**: 使用小写字母，单词间用点分隔
2. **类命名**: 使用大驼峰命名法（PascalCase）
3. **方法命名**: 使用小驼峰命名法（camelCase）
4. **常量命名**: 使用全大写，单词间用下划线分隔

### 日志规范

1. **日志级别**:
   - `DEBUG`: 详细的调试信息，生产环境不输出
   - `INFO`: 一般信息，如业务流程关键节点
   - `WARN`: 警告信息，需要注意但不影响运行
   - `ERROR`: 错误信息，需要立即处理

2. **日志内容**: 使用英文记录日志，包含必要的上下文信息

### 异常处理

1. 业务异常使用 `BusinessException`
2. 权限异常使用 `UnauthorizedException`
3. 系统异常由 `GlobalExceptionHandler` 统一处理

## 部署说明

### Docker部署

项目提供了Docker部署配置，相关文件位于 `ai-prd-main/docker/` 目录：

- `dockerfile-part`: Docker构建配置
- `setenv.sh`: 环境变量设置脚本
- `log4j2.xml`: 容器内日志配置

### 配置管理

- 数据库配置: `dbconf/AI_PRD.properties`
- Redis配置: `runtimecfg/spring-redis-conf.properties`
- 易宝组件配置: `runtimecfg/yeepay-config.properties`

## 版本信息

- **当前版本**: 1.0.0-SNAPSHOT
- **更新日期**: 2024-12-19
- **维护团队**: AI PRD Team

## 相关文档

- [详细架构设计](ai-prd-main/docs/ARCHITECTURE.md) - 系统架构详细说明
- [API文档](ai-prd-main/docs/API.md) - RESTful API接口文档（待补充）
- [部署指南](ai-prd-main/docs/DEPLOYMENT.md) - 详细部署说明（待补充）

## 联系方式

如有问题或建议，请联系开发团队。 