# AI PRD 助手 - 后端技术方案设计

> **文档版本:** v1.0  
> **创建日期:** 2024-12-19  
> **架构师:** 后端系统架构师  

## 1. 需求分析阶段

### 1.1 系统模块设计

#### 应用模块拆分

考虑到这是AI提效的试点产品，采用**单体架构**降低复杂度和成本，同时保持模块化设计便于未来扩展：

```mermaid
graph TB
    subgraph "AI PRD助手 - 单体应用"
        A[Web前端] --> B[Spring Boot应用]
        
        subgraph "业务模块层"
            B --> C[对话模块 ConversationModule] 
            B --> D[PRD模块 PRDModule]
            B --> E[AI模块 AIModule]
            B --> F[文件模块 FileModule]
        end
        
        subgraph "数据访问层"
            C --> G[ConversationRepository]
            D --> H[PRDRepository]
        end
    end
    
    subgraph "外部依赖"
        B --> J[权限中心]
        E --> K[DeepSeek API]
        G --> L[MySQL数据库]
        H --> L
        E --> M[Redis缓存]
        F --> N[公司云存储组件]
    end
```

**核心模块设计说明：**

1. **对话模块 (ConversationModule)** 
   - 对话会话管理
   - 消息存储与检索
   - 对话历史管理

2. **PRD模块 (PRDModule)**
   - PRD文档生成与管理
   - 文档版本控制
   - 导出与分享功能

3. **AI模块 (AIModule)**
   - 需求理解与分析
   - 信心指数评估
   - 智能问答生成

4. **文件模块 (FileModule)**
   - 文件上传下载
   - 图片处理
   - 文档转换

#### 运行流程设计

```mermaid
sequenceDiagram
    participant U as 用户
    participant APP as Spring Boot应用
    participant PC as 权限中心
    participant CM as 对话模块  
    participant AM as AI模块
    participant PM as PRD模块
    participant FM as 文件模块
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant N as 易宝云存储
    
    U->>APP: 带Token的请求
    APP->>PC: 验证用户Token
    PC->>APP: 返回用户信息
    APP->>U: 验证成功
    
    U->>APP: 创建新对话
    APP->>CM: 创建对话会话
    CM->>DB: 保存对话记录
    DB->>CM: 返回会话ID
    CM->>APP: 返回会话信息
    APP->>U: 会话创建成功
    
    U->>APP: 发送需求信息(文本/文件)
    alt 包含文件
        APP->>FM: 处理文件上传
        FM->>FM: 文件内容提取
        FM->>DB: 保存文件信息
    end
    APP->>CM: 保存用户消息
    CM->>DB: 存储消息
    APP->>AM: 分析需求内容
    AM->>AM: 调用DeepSeek API
    AM->>AM: 计算信心指数
    AM->>R: 缓存分析结果
    AM->>APP: 返回AI回复
    APP->>CM: 保存AI回复
    CM->>DB: 存储AI消息
    APP->>U: 返回AI响应
    
    loop 需求澄清
        U->>APP: 回答AI问题
        APP->>CM: 保存消息
        APP->>AM: 继续分析
        AM->>R: 更新信心指数缓存
        AM->>APP: 返回分析结果
        APP->>U: 返回分析结果
    end
    
    AM->>AM: 信心指数达到阈值(80%)
    AM->>PM: 触发PRD生成
    PM->>PM: 生成PRD文档内容
    PM->>PM: 生成文档文件名
    PM->>N: 上传PRD到云存储
    N->>PM: 返回文档URL
    PM->>DB: 保存PRD元信息和URL
    PM->>APP: PRD生成完成
    APP->>U: 通知PRD已生成
    
    U->>APP: 查看PRD信息
    APP->>PM: 获取PRD元信息
    PM->>DB: 查询PRD文档记录
    DB->>PM: 返回文档元信息
    PM->>APP: 返回PRD信息
    APP->>U: 返回PRD信息
    
    U->>APP: 查看PRD内容
    APP->>PM: 获取PRD内容
    PM->>N: 从云存储下载文档
    N->>PM: 返回文档内容
    PM->>APP: 返回PRD内容
    APP->>U: 返回PRD内容
    
    U->>APP: 下载PRD
    APP->>PM: 下载请求
    PM->>N: 从云存储获取文档
    N->>PM: 返回文档内容
    PM->>APP: 返回文件流
    APP->>U: 提供文件下载
```

#### 模块间交互设计

单体架构下，各模块通过直接的方法调用进行交互，权限验证统一通过权限中心：

**1. 权限验证组件**
```java
// 权限验证服务
@Component
public class PermissionHelper {
    
    @Autowired
    private PermissionCenterClient permissionCenterClient;
    
    public UserInfo validateToken(String token) {
        return permissionCenterClient.getUserByToken(token);
    }
    
    public boolean hasPermission(String userId, String resource) {
        return permissionCenterClient.checkPermission(userId, resource);
    }
}
```

**2. 对话模块接口**
```java
// 对话管理服务
@Service
public class ConversationService {
    public ResponseResult<ConversationDTO> createConversation(String userId, CreateConversationRequest request);
    public ResponseResult<List<ConversationDTO>> getConversationList(String userId);
    public ResponseResult<Void> saveMessage(SaveMessageRequest request);
    public ResponseResult<List<MessageDTO>> getMessages(String conversationId);
}
```

**3. AI模块接口**
```java
// AI分析服务
@Service
public class AIAnalysisService {
    public ResponseResult<AIAnalysisResult> analyzeRequirement(AnalyzeRequest request);
    public ResponseResult<Integer> getConfidenceScore(String conversationId);
    public ResponseResult<QuestionDTO> generateClarificationQuestion(String conversationId);
}
```

**4. PRD模块接口**
```java
// PRD管理服务
@Service
public class PRDService {
    public ResponseResult<PRDDocumentDTO> generatePRD(GeneratePRDRequest request);
    public ResponseResult<PRDDocumentDTO> updatePRD(UpdatePRDRequest request);
    public ResponseResult<String> exportPRD(ExportPRDRequest request);
    public ResponseResult<String> sharePRD(SharePRDRequest request);
}
```

**5. Controller层统一权限验证示例**
```java
// 统一的权限验证控制器基类
@RestController
public class BaseController {
    
    @Autowired
    private PermissionHelper permissionHelper;
    
    protected UserInfo validateAndGetUser(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (StringUtils.isBlank(token)) {
            throw new UnauthorizedException("Token is required");
        }
        
        UserInfo userInfo = permissionHelper.validateToken(token);
        if (userInfo == null) {
            throw new UnauthorizedException("Invalid token");
        }
        
        return userInfo;
    }
}

// 具体业务控制器
@RestController
@RequestMapping("/api/conversations")
public class ConversationController extends BaseController {
    
    @Autowired
    private ConversationService conversationService;
    
    @PostMapping
    public ResponseResult<ConversationDTO> createConversation(
            HttpServletRequest request,
            @RequestBody CreateConversationRequest req) {
        
        // 统一权限验证
        UserInfo user = validateAndGetUser(request);
        
        // 执行业务逻辑
        return conversationService.createConversation(user.getUserId(), req);
    }
    
    @GetMapping
    public ResponseResult<List<ConversationDTO>> getConversations(HttpServletRequest request) {
        UserInfo user = validateAndGetUser(request);
        return conversationService.getConversationList(user.getUserId());
    }
}
```

### 1.2 功能需求

#### 核心功能实现逻辑

**1. 多模态输入处理**
- 文本输入：直接解析用户描述
- 文件上传：支持.txt、.md、.docx、.pdf格式，提取文本内容
- 图片上传：OCR识别图片中的文字和手绘草图
- 边界条件：文件大小限制10MB，图片分辨率限制

**2. AI需求理解引擎**
- 使用NLP技术提取关键信息
- 实时计算信心指数(0-100)
- 当信心指数<40%时，生成针对性澄清问题
- 支持上下文记忆，避免重复提问

**3. PRD自动生成**
- 基于预定义模板和AI分析结果
- 支持Markdown格式输出
- 包含完整的PRD结构（背景、目标用户、功能需求等）

**4. 信心指数状态流转**

```mermaid
stateDiagram-v2
    [*] --> 初始状态
    初始状态 --> 信息收集: 用户输入
    信息收集 --> 需求澄清: 信心指数<40%
    需求澄清 --> 信息收集: 用户回答
    信息收集 --> 深度分析: 40%≤信心指数<80%
    深度分析 --> 信息收集: 继续提问
    深度分析 --> PRD生成: 信心指数≥80%
    PRD生成 --> 完成: 生成成功
    完成 --> [*]
```

### 1.3 数据库设计

#### 对话模块数据库

```sql
-- 对话会话表
CREATE TABLE `conversation` (
  `id` varchar(64) NOT NULL COMMENT '对话ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `title` varchar(200) NOT NULL COMMENT '对话标题',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态:1-进行中,2-已完成,3-已删除',
  `confidence_score` int DEFAULT 0 COMMENT '当前信心指数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(64) COMMENT '创建人',
  `update_user` varchar(64) COMMENT '修改人',
  `nonce` bigint NOT NULL DEFAULT 0 COMMENT '乐观锁',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对话会话表';

-- 对话消息表
CREATE TABLE `conversation_message` (
  `id` varchar(64) NOT NULL COMMENT '消息ID',
  `conversation_id` varchar(64) NOT NULL COMMENT '对话ID',
  `sender_type` tinyint NOT NULL COMMENT '发送者类型:1-用户,2-AI',
  `content_type` tinyint NOT NULL COMMENT '内容类型:1-文本,2-文件,3-图片',
  `content` text NOT NULL COMMENT '消息内容',
  `file_urls` json COMMENT '文件URL数组(如果有)',
  `metadata` json COMMENT '文件元信息(原文件名、大小等)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对话消息表';
```

#### PRD模块数据库

```sql
-- PRD文档表
CREATE TABLE `prd_document` (
  `id` varchar(64) NOT NULL COMMENT 'PRD文档ID',
  `conversation_id` varchar(64) NOT NULL COMMENT '对话ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `title` varchar(200) NOT NULL COMMENT '文档标题',
  `file_name` varchar(255) NOT NULL COMMENT '文档文件名',
  `file_url` varchar(500) NOT NULL COMMENT '云存储文档URL',
  `file_size` bigint DEFAULT 0 COMMENT '文件大小(字节)',
  `version` int NOT NULL DEFAULT 1 COMMENT '版本号',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态:1-草稿,2-已发布',
  `share_token` varchar(64) COMMENT '分享token',
  `share_expire_time` datetime COMMENT '分享过期时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(64) COMMENT '创建人',
  `update_user` varchar(64) COMMENT '修改人',
  `nonce` bigint NOT NULL DEFAULT 0 COMMENT '乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_conversation_id` (`conversation_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_share_token` (`share_token`),
  KEY `idx_file_name` (`file_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PRD文档表';

-- PRD版本历史表
CREATE TABLE `prd_version_history` (
  `id` varchar(64) NOT NULL COMMENT '版本ID',
  `prd_document_id` varchar(64) NOT NULL COMMENT 'PRD文档ID',
  `version` int NOT NULL COMMENT '版本号',
  `file_name` varchar(255) NOT NULL COMMENT '历史版本文件名',
  `file_url` varchar(500) NOT NULL COMMENT '历史版本文档URL',
  `file_size` bigint DEFAULT 0 COMMENT '文件大小(字节)',
  `change_log` text COMMENT '变更日志',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(64) COMMENT '创建人',
  PRIMARY KEY (`id`),
  KEY `idx_prd_document_id` (`prd_document_id`),
  KEY `idx_version` (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PRD版本历史表';
```

#### 文件处理说明

**不需要独立的文件表**，原因如下：

1. **用户文件特点**：主要是个人需求文档和草图，重复率极低
2. **云存储组件**：已提供文件管理能力，无需重复建设  
3. **简化原则**：避免过度设计，专注核心业务价值

**文件信息存储方案(分离存储)**：
- 文件直接上传到云存储组件
- **完整的提取内容**存储在Redis(key: file_content_key, TTL: 7天)
- **文件摘要**(前200字)存储在`conversation_message.content`
- 原文件URL存储在`conversation_message.file_url` 
- 文件元信息(原文件名、大小等)存储在`conversation_message.metadata`

**优势**：
- message表保持轻量，查询速度快
- 完整文件内容按需加载，节省内存

#### PRD文档命名规则

**文档命名规范**：
```
PRD_{用户ID}_{对话ID}_{版本号}_{时间戳}.md
```

**命名规则详细说明**：

1. **固定前缀**：`PRD_` - 标识文档类型
2. **用户ID**：确保不同用户文档隔离，支持权限控制
3. **对话ID**：关联具体的需求对话会话，便于追溯
4. **版本号**：`v1`, `v2`, `v3`... 版本管理，支持回滚
5. **时间戳**：`yyyyMMddHHmmss` 格式，确保文件名唯一性
6. **文件后缀**：`.md` Markdown格式

**命名示例**：
```
PRD_user001_conv12345_v1_20241219143052.md
PRD_user001_conv12345_v2_20241219150130.md
PRD_user002_conv67890_v1_20241219160245.md
```

**存储路径规范**：
```
/ai-prd/{年份}/{月份}/{用户ID}/PRD_{用户ID}_{对话ID}_{版本号}_{时间戳}.md
```

**路径示例**：
```
/ai-prd/2024/12/user001/PRD_user001_conv12345_v1_20241219143052.md
/ai-prd/2024/12/user001/PRD_user001_conv12345_v2_20241219150130.md
```

**命名规则优势**：
- **唯一性保证**：时间戳确保文件名不重复
- **权限隔离**：用户ID确保数据安全隔离
- **版本管理**：版本号支持文档版本控制
- **快速定位**：规则化命名便于文件管理和查找
- **存储优化**：按年月分目录，避免单目录文件过多

## 4. 实施过程任务拆解

### 4.1 需求规模评估

**项目复杂度评估**：
- **功能模块**：4个核心模块（对话、PRD、AI、文件）
- **技术栈**：单体架构，技术栈相对简单
- **外部依赖**：权限中心、DeepSeek API、云存储组件
- **预计开发周期**：6-8周

**拆分原则**：
- 任务间保证最小耦合，支持并行开发
- 每个任务包含完整的功能闭环
- 优先级排序，核心功能优先实现

### 4.2 任务拆分详情

#### 任务1：项目框架搭建+权限中心对接 (优先级：P0)

**预计工期**：1.5周

**任务目标**：
- 搭建Spring Boot基础框架
- 配置数据库连接和基础中间件
- 实现统一的接口响应格式和异常处理
- 对接公司权限中心组件
- 实现统一的用户身份验证

**交付物**：
```
├── 基础项目结构
├── 数据库连接配置
├── 统一响应格式(ResponseResult)
├── 全局异常处理器
├── 基础配置类
├── PermissionHelper权限工具类
├── BaseController基础控制器
├── Token验证拦截器
└── 健康检查接口
```

**验收标准**：
- [x] 项目可正常启动
- [x] 数据库连接正常
- [x] 健康检查接口正常响应
- [x] 统一响应格式验证通过
- [x] Token验证功能正常
- [x] 用户信息获取正常
- [x] 权限拦截器工作正常

**核心代码结构**：
```java
// 统一响应结果类
public class ResponseResult<T> {
    private String code;
    private String message;
    private String traceId;
    private T data;
}

// 全局异常处理器
@RestControllerAdvice
public class GlobalExceptionHandler {
    // 处理业务异常、参数异常等
}

// 权限工具类
@Component
public class PermissionHelper {
    public UserInfo validateToken(String token);
    public boolean hasPermission(String userId, String resource);
}

// 基础控制器
@RestController
public class BaseController {
    protected UserInfo validateAndGetUser(HttpServletRequest request);
}
```

---

#### 任务2：AI模型适配层实现 (优先级：P0)

**预计工期**：1.5周

**任务目标**：
- 实现AI模型抽象接口
- 对接DeepSeek API
- 实现配置化的模型切换机制

**交付物**：
```
├── AIModelClient接口定义
├── DeepSeekModelClient实现
├── AIConfigService配置服务
├── AESDecryptUtil解密工具
├── model-config.properties配置文件
└── AI模型工厂类
```

**验收标准**：
- [x] DeepSeek API调用正常
- [x] 配置文件读取和解密正常
- [x] AI模型切换机制验证
- [x] 错误处理和重试机制正常

**核心接口**：
```java
// AI模型客户端接口
public interface AIModelClient {
    String generateResponse(String prompt);
    AIAnalysisResponse analyzeRequirement(AIAnalysisRequest request);
}

// AI配置服务
@Service
public class AIConfigService {
    public String getDecryptedApiKey(String provider);
    public String getConfig(String key);
}
```

---

#### 任务3：对话管理模块 (优先级：P1)

**预计工期**：1.5周

**任务目标**：
- 实现对话会话管理
- 支持多模态消息存储
- 实现对话历史查询功能

**交付物**：
```
├── 对话相关数据表
├── ConversationService服务类
├── ConversationController控制器
├── 对话DTO和实体类
├── 消息存储和查询接口
└── 对话状态管理
```

**验收标准**：
- [x] 创建/查询对话功能正常
- [x] 消息存储和查询正常
- [x] 支持文本、文件、图片消息
- [x] 对话状态更新正常

**API接口**：
```java
@RestController
@RequestMapping("/api/conversations")
public class ConversationController extends BaseController {
    
    @PostMapping
    public ResponseResult<ConversationDTO> createConversation(
        @RequestBody CreateConversationRequest request);
    
    @GetMapping("/{conversationId}/messages")
    public ResponseResult<List<MessageDTO>> getMessages(
        @PathVariable String conversationId);
        
    @PostMapping("/{conversationId}/messages")
    public ResponseResult<MessageDTO> sendMessage(
        @PathVariable String conversationId,
        @RequestBody SendMessageRequest request);
}
```

---

#### 任务5：文件处理模块 (优先级：P1)

**预计工期**：1周

**任务目标**：
- 对接云存储组件
- 实现文件上传和内容提取
- 支持多种文件格式解析

**交付物**：
```
├── FileService文件服务
├── FileController文件控制器
├── 文件内容提取工具类
├── OCR图片识别接口
├── 文件格式解析器
└── 文件上传接口
```

**验收标准**：
- [x] 文件上传到云存储正常
- [x] 文本文件内容提取正常
- [x] 图片OCR识别正常
- [x] 文件元信息存储正常

**API接口**：
```java
@RestController
@RequestMapping("/api/files")
public class FileController extends BaseController {
    
    @PostMapping("/upload")
    public ResponseResult<FileUploadDTO> uploadFile(
        @RequestParam("file") MultipartFile file);
    
    @PostMapping("/extract")
    public ResponseResult<String> extractContent(
        @RequestBody FileExtractionRequest request);
}
```

---

#### 任务6：需求分析引擎 (优先级：P1)

**预计工期**：2周

**任务目标**：
- 实现AI需求理解和分析
- 建立信心指数计算机制
- 实现澄清问题生成逻辑

**交付物**：
```
├── RequirementAnalysisService分析服务
├── AI分析结果存储表
├── 信心指数计算算法
├── 澄清问题生成逻辑
├── 分析结果聚合器
└── 需求分析API接口
```

**验收标准**：
- [x] AI需求分析功能正常
- [x] 信心指数计算准确
- [x] 澄清问题生成合理
- [x] 分析结果存储正常

**核心逻辑**：
```java
@Service
public class RequirementAnalysisService {
    
    public AnalysisResult analyzeRequirement(String conversationId, String userInput);
    
    public String generateClarificationQuestion(AnalysisResult result);
    
    public int calculateConfidenceScore(AIAnalysisResponse response);
}
```

---

#### 任务6：PRD生成模块 (优先级：P2)

**预计工期**：1.5周

**任务目标**：
- 实现基于分析结果的PRD生成
- 支持Markdown格式输出
- 实现PRD版本管理

**交付物**：
```
├── PRDService PRD服务
├── PRDController PRD控制器
├── PRD模板管理
├── Markdown生成器
├── 版本历史管理
└── PRD导出功能
```

**验收标准**：
- [x] PRD自动生成功能正常
- [x] Markdown格式输出正确
- [x] 版本管理功能正常
- [x] PRD内容结构完整

**API接口**：
```java
@RestController
@RequestMapping("/api/prd")
public class PRDController extends BaseController {
    
    @Autowired
    private PRDGeneratorService prdGeneratorService;
    
    @PostMapping("/generate")
    public ResponseResult<PRDDocumentDTO> generatePRD(
        HttpServletRequest request,
        @RequestBody GeneratePRDRequest req) {
        
        UserInfo user = validateAndGetUser(request);
        
        PRDDocument document = prdGeneratorService.generatePRD(
            req.getConversationId(), user.getUserId());
        
        return ResponseResult.success(convertToDTO(document));
    }
    
    @PutMapping("/{prdId}")
    public ResponseResult<PRDDocumentDTO> updatePRD(
        HttpServletRequest request,
        @PathVariable String prdId,
        @RequestBody UpdatePRDRequest req) {
        
        UserInfo user = validateAndGetUser(request);
        
        PRDDocument document = prdGeneratorService.updatePRD(
            prdId, user.getUserId(), req.getContent());
        
        return ResponseResult.success(convertToDTO(document));
    }
    
    @GetMapping("/{prdId}")
    public ResponseResult<PRDDocumentDTO> getPRDInfo(
        HttpServletRequest request,
        @PathVariable String prdId) {
        
        UserInfo user = validateAndGetUser(request);
        
        PRDDocument document = prdGeneratorService.getPRDDocument(prdId);
        
        // 权限验证：只有文档所有者可以查看
        if (!user.getUserId().equals(document.getUserId())) {
            throw new UnauthorizedException("Access denied");
        }
        
        return ResponseResult.success(convertToDTO(document));
    }
    
    @GetMapping("/{prdId}/content")
    public ResponseResult<String> getPRDContent(
        HttpServletRequest request,
        @PathVariable String prdId) {
        
        UserInfo user = validateAndGetUser(request);
        
        PRDDocument document = prdGeneratorService.getPRDDocument(prdId);
        
        // 权限验证
        if (!user.getUserId().equals(document.getUserId())) {
            throw new UnauthorizedException("Access denied");
        }
        
        // 从云存储获取文档内容
        String content = prdGeneratorService.getPRDContent(prdId);
        
        return ResponseResult.success(content);
    }
    
    @GetMapping("/{prdId}/download")
    public ResponseEntity<Resource> downloadPRD(
        HttpServletRequest request,
        @PathVariable String prdId) {
        
        UserInfo user = validateAndGetUser(request);
        
        PRDDocument document = prdGeneratorService.getPRDDocument(prdId);
        
        // 权限验证
        if (!user.getUserId().equals(document.getUserId())) {
            throw new UnauthorizedException("Access denied");
        }
        
        // 从云存储获取文档内容
        String content = prdGeneratorService.getPRDContent(prdId);
        
        // 构建下载响应
        ByteArrayResource resource = new ByteArrayResource(
            content.getBytes(StandardCharsets.UTF_8));
        
        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, 
                "attachment; filename=\"" + document.getFileName() + "\"")
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .contentLength(resource.contentLength())
            .body(resource);
    }
    
    @GetMapping("/{prdId}/versions")
    public ResponseResult<List<PRDVersionDTO>> getPRDVersions(
        HttpServletRequest request,
        @PathVariable String prdId) {
        
        UserInfo user = validateAndGetUser(request);
        
        PRDDocument document = prdGeneratorService.getPRDDocument(prdId);
        
        // 权限验证
        if (!user.getUserId().equals(document.getUserId())) {
            throw new UnauthorizedException("Access denied");
        }
        
        List<PRDVersionHistory> versions = prdGeneratorService.getPRDVersions(prdId);
        List<PRDVersionDTO> versionDTOs = versions.stream()
            .map(this::convertToVersionDTO)
            .collect(Collectors.toList());
        
        return ResponseResult.success(versionDTOs);
    }
    
    @PostMapping("/{prdId}/share")
    public ResponseResult<ShareLinkDTO> generateShareLink(
        HttpServletRequest request,
        @PathVariable String prdId,
        @RequestBody SharePRDRequest req) {
        
        UserInfo user = validateAndGetUser(request);
        
        ShareLinkDTO shareLink = prdGeneratorService.generateShareLink(
            prdId, user.getUserId(), req.getExpireHours());
        
        return ResponseResult.success(shareLink);
    }
    
    private PRDDocumentDTO convertToDTO(PRDDocument document) {
        return PRDDocumentDTO.builder()
            .id(document.getId())
            .conversationId(document.getConversationId())
            .title(document.getTitle())
            .fileName(document.getFileName())
            .fileSize(document.getFileSize())
            .version(document.getVersion())
            .status(document.getStatus())
            .shareToken(document.getShareToken())
            .shareExpireTime(document.getShareExpireTime())
            .createTime(document.getCreateTime())
            .updateTime(document.getUpdateTime())
            .build();
    }
    
    private PRDVersionDTO convertToVersionDTO(PRDVersionHistory version) {
        return PRDVersionDTO.builder()
            .id(version.getId())
            .version(version.getVersion())
            .fileName(version.getFileName())
            .fileSize(version.getFileSize())
            .changeLog(version.getChangeLog())
            .createTime(version.getCreateTime())
            .createUser(version.getCreateUser())
            .build();
    }
}
```

---

#### 任务7：前端页面开发 (优先级：P2)

**预计工期**：2周

**任务目标**：
- 实现Vue3前端应用
- 对话界面和PRD编辑器
- 文件上传和预览功能

**交付物**：
```
├── Vue3项目结构
├── 对话聊天界面
├── Monaco Editor集成
├── 文件上传组件
├── PRD预览界面
└── 响应式布局
```

**验收标准**：
- [x] 用户界面美观易用
- [x] 实时对话功能正常
- [x] PRD编辑功能正常
- [x] 文件上传预览正常

---

#### 任务8：系统集成测试 (优先级：P2)

**预计工期**：1周

**任务目标**：
- 端到端功能测试
- 性能测试和优化
- 安全测试和漏洞修复

**交付物**：
```
├── 集成测试用例
├── 性能测试报告
├── 安全测试报告
├── Bug修复记录
└── 部署文档
```

**验收标准**：
- [x] 所有核心功能测试通过
- [x] 性能指标满足要求
- [x] 安全漏洞修复完毕
- [x] 部署文档完整

### 4.3 任务依赖关系

```mermaid
graph TB
    A[任务1: 项目框架搭建+权限中心对接] --> B[任务2: AI模型适配层]
    
    A --> C[任务3: 对话管理模块]
    B --> C
    
    A --> D[任务4: 文件处理模块]
    
    B --> E[任务5: 需求分析引擎]
    C --> E
    D --> E
    
    E --> F[任务6: PRD生成模块]
    
    C --> G[任务7: 前端页面开发]
    F --> G
    
    G --> H[任务8: 系统集成测试]
    F --> H
```

### 4.4 并行开发建议

**第一阶段 (1-2周)**：
- 任务1: 项目框架搭建+权限中心对接

**第二阶段 (3-4周)**：
- 任务2: AI模型适配层实现
- 任务4: 文件处理模块 (并行开发)

**第三阶段 (5-6周)**：
- 任务3: 对话管理模块
- 任务5: 需求分析引擎

**第四阶段 (7-8周)**：
- 任务6: PRD生成模块
- 任务7: 前端页面开发 (并行开发)

**第五阶段 (9周)**：
- 任务8: 系统集成测试

### 4.5 风险控制

**技术风险**：
- AI模型API稳定性风险 → 实现重试机制和降级策略
- 文件处理性能风险 → 异步处理和限流机制

**进度风险**：
- 任务间依赖过多 → 优化并行开发策略
- 需求变更风险 → 模块化设计，便于调整

**质量风险**：
- 代码质量风险 → 代码评审和自动化测试
- 集成风险 → 持续集成和早期集成测试
- Redis TTL自动清理过期内容
- 对话列表展示只需要摘要信息

**实现示例**：
```java
@Service
public class FileService {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    public FileProcessResult processUploadedFile(MultipartFile file) {
        // 1. 提取完整文本内容
        String fullContent = extractContent(file);
        
        // 2. 生成缓存key并存储到Redis(7天TTL)
        String contentKey = "file_content:" + UUID.randomUUID().toString();
        redisTemplate.opsForValue().set(contentKey, fullContent, Duration.ofDays(7));
        
        // 3. 生成摘要(前200字 + "...")
        String summary = fullContent.length() > 200 
            ? fullContent.substring(0, 200) + "..." 
            : fullContent;
            
        return FileProcessResult.builder()
            .fileUrl(fileUrl)
            .summary(summary)  // 存储到message.content
            .contentKey(contentKey)  // 存储到message.file_content_key
            .metadata(metadata)
            .build();
    }
    
    // AI分析时获取完整内容
    public String getFullFileContent(String contentKey) {
        return redisTemplate.opsForValue().get(contentKey);
    }
}
```

## 2. 技术架构设计

### 2.1 技术栈选择

**后端技术栈：**
- **JDK版本：** 1.8 (按公司标准)
- **框架：** Spring Boot 2.7.x (单体架构)
- **数据库：** MySQL 8.0 + Redis 6.0
- **对象存储：** 公司云存储组件
- **AI模型：** DeepSeek API (支持多AI模型适配)
- **构建工具：** Maven
- **日志框架：** Logback

**前端技术栈：**
- **框架：** Vue 3 + TypeScript  
- **编辑器：** Monaco Editor (支持Markdown)
- **UI组件：** Element Plus

### 2.2 单体架构部署方案

**部署架构优势：**
- **成本低：** 单机部署，节省服务器资源
- **简单易维护：** 统一部署，调试方便
- **性能好：** 模块间直接调用，无网络开销
- **适合试点：** 快速迭代，降低技术复杂度

```mermaid
graph TB
    subgraph "部署环境"
        A[负载均衡器] --> B[AI PRD助手应用]
        
        subgraph "应用内部结构"
            B --> C[Controller层]
            C --> D[Service层]
            D --> E[Repository层]
            
            subgraph "业务模块"
                D --> F1[ConversationService]  
                D --> F2[PRDService]
                D --> F3[AIAnalysisService]
                D --> F4[FileService]
            end
        end
        
        subgraph "数据层"
            E --> G1[MySQL数据库]
            F3 --> G2[Redis缓存]
            F4 --> G3[公司云存储]
        end
        
        subgraph "外部API"
            F3 --> H1[DeepSeek API]
            C --> H2[权限中心]
        end
    end
```

**资源配置建议：**
- **服务器配置：** 4核8G内存，足够支撑试点阶段使用
- **数据库：** MySQL 8.0，单机部署即可
- **缓存：** Redis单实例，用于会话和AI结果缓存
- **监控：** 基础的应用监控和日志收集

### 2.3 接口响应格式

所有前端接口统一响应格式：

```json
{
  "code": "000000",
  "message": "SUCCESS", 
  "traceId": "d1fab1afe99d459199475f032f4034df",
  "data": {
    // 接口具体数据
  }
}
```

## 3. 关键技术实现

### 3.1 AI模型适配层设计

为支持快速切换不同AI服务商，设计统一的AI模型适配层：

```java
// AI模型接口抽象
public interface AIModelClient {
    AIAnalysisResponse analyzeRequirement(AIAnalysisRequest request);
    PRDContent generatePRDContent(ConversationAnalysis analysis, PRDTemplate template);
    String generateClarificationQuestion(AIAnalysisResponse response);
}

// DeepSeek实现
@Component("deepseekClient")
public class DeepSeekModelClient implements AIModelClient {
    
    @Autowired
    private AIConfigService aiConfigService;
    
    private String getDecryptedApiKey() {
        return aiConfigService.getDecryptedApiKey("deepseek");
    }
    
    private String getBaseUrl() {
        return aiConfigService.getConfig("ai.deepseek.base-url");
    }
    
    @Override
    public AIAnalysisResponse analyzeRequirement(AIAnalysisRequest request) {
        // DeepSeek API调用实现
        DeepSeekRequest deepSeekRequest = convertToDeepSeekRequest(request);
        DeepSeekResponse response = deepSeekApiClient.chat(deepSeekRequest);
        return convertToAIAnalysisResponse(response);
    }
    
    // 其他方法实现...
}

// AI模型工厂类
@Component
public class AIModelFactory {
    
    @Value("${ai.provider:deepseek}")
    private String defaultProvider;
    
    @Autowired
    private Map<String, AIModelClient> aiModelClients;
    
    public AIModelClient getClient() {
        return getClient(defaultProvider);
    }
    
    public AIModelClient getClient(String provider) {
        AIModelClient client = aiModelClients.get(provider + "Client");
        if (client == null) {
            throw new IllegalArgumentException("Unsupported AI provider: " + provider);
        }
        return client;
    }
}

// AI配置服务
@Service
public class AIConfigService {
    
    @Autowired
    private ApplicationCenterClient applicationCenterClient;
    
    @Autowired
    private AESDecryptUtil aesDecryptUtil;
    
    private static final String CONFIG_FILE = "model-config.properties";
    
    /**
     * 获取解密后的API密钥
     */
    public String getDecryptedApiKey(String provider) {
        String encryptedKey = getConfig("ai." + provider + ".api-key");
        if (StringUtils.isBlank(encryptedKey)) {
            throw new IllegalArgumentException("API key not found for provider: " + provider);
        }
        
        try {
            return aesDecryptUtil.decrypt(encryptedKey);
        } catch (Exception e) {
            log.error("Failed to decrypt API key for provider: {}", provider, e);
            throw new RuntimeException("API key decryption failed", e);
        }
    }
    
    /**
     * 获取配置项
     */
    public String getConfig(String key) {
        try {
            return applicationCenterClient.getProperty(CONFIG_FILE, key);
        } catch (Exception e) {
            log.error("Failed to get config: {}", key, e);
            throw new RuntimeException("Config retrieval failed: " + key, e);
        }
    }
    
    /**
     * 获取整数配置项
     */
    public Integer getIntConfig(String key) {
        String value = getConfig(key);
        return StringUtils.isNotBlank(value) ? Integer.valueOf(value) : null;
    }
    
    /**
     * 获取浮点配置项
     */
    public Double getDoubleConfig(String key) {
        String value = getConfig(key);
        return StringUtils.isNotBlank(value) ? Double.valueOf(value) : null;
    }
    
    /**
     * 获取布尔配置项
     */
    public Boolean getBooleanConfig(String key) {
        String value = getConfig(key);
        return StringUtils.isNotBlank(value) ? Boolean.valueOf(value) : false;
    }
}

// AES解密工具类
@Component
public class AESDecryptUtil {
    
    @Value("${app.aes.secret-key}")
    private String secretKey;
    
    /**
     * AES解密
     */
    public String decrypt(String encryptedText) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        
        byte[] encrypted = Base64.getDecoder().decode(encryptedText);
        byte[] decrypted = cipher.doFinal(encrypted);
        
        return new String(decrypted, StandardCharsets.UTF_8);
    }
}

// 应用中心客户端接口
public interface ApplicationCenterClient {
    
    /**
     * 从应用中心获取配置属性
     */
    String getProperty(String configFile, String key);
    
    /**
     * 获取整个配置文件的属性映射
     */
    Map<String, String> getProperties(String configFile);
}

// 配置类
@Configuration
public class AIModelConfig {
    
    @Autowired
    private AIConfigService aiConfigService;
    
    // 支持未来扩展其他AI模型
    @Bean
    @ConditionalOnExpression("#{aiConfigService.getBooleanConfig('ai.openai.enabled')}")
    public AIModelClient openaiClient() {
        return new OpenAIModelClient();
    }
    
    @Bean
    @ConditionalOnExpression("#{aiConfigService.getBooleanConfig('ai.claude.enabled')}")
    public AIModelClient claudeClient() {
        return new ClaudeModelClient();
    }
}
```

**配置文件设计 (model-config.properties):**

使用公司应用中心统一管理AI模型配置，支持AES加密的apiKey：

```properties
# AI模型配置文件 - model-config.properties
# 当前使用的AI提供商
ai.provider=deepseek

# DeepSeek配置
ai.deepseek.api-key=U2FsdGVkX1+xxx...  # AES加密后的密文
ai.deepseek.base-url=https://api.deepseek.com
ai.deepseek.model=deepseek-chat
ai.deepseek.max-tokens=4000
ai.deepseek.temperature=0.7
ai.deepseek.enabled=true

# OpenAI配置(预留)
ai.openai.enabled=false
ai.openai.api-key=U2FsdGVkX1+yyy...  # AES加密后的密文
ai.openai.base-url=https://api.openai.com
ai.openai.model=gpt-4
ai.openai.max-tokens=4000
ai.openai.temperature=0.7

# Claude配置(预留)
ai.claude.enabled=false
ai.claude.api-key=U2FsdGVkX1+zzz...  # AES加密后的密文
ai.claude.base-url=https://api.anthropic.com
ai.claude.model=claude-3-sonnet
```

**快速切换AI模型示例：**

```java
// 运行时动态切换AI模型
@RestController
@RequestMapping("/admin/ai")
public class AIConfigController {
    
    @Autowired
    private AIModelFactory aiModelFactory;
    
    @PostMapping("/switch-provider")
    public ResponseResult<Void> switchProvider(@RequestParam String provider) {
        try {
            // 验证新的AI提供商是否可用
            AIModelClient newClient = aiModelFactory.getClient(provider);
            
            // 更新默认提供商配置
            updateDefaultProvider(provider);
            
            log.info("AI provider switched to: {}", provider);
            return ResponseResult.success();
        } catch (Exception e) {
            log.warn("Failed to switch AI provider to: {}", provider, e);
            return ResponseResult.error("Switch failed: " + e.getMessage());
        }
    }
}
```

### 3.2 AI需求理解引擎

```java
@Service
public class RequirementAnalysisService {
    
    @Autowired
    private AIModelFactory aiModelFactory;
    
    @Autowired
    private ConversationService conversationService;
    
    public AnalysisResult analyzeRequirement(String conversationId, String userInput) {
        // 1. 获取对话历史上下文
        List<Message> history = conversationService.getMessageHistory(conversationId);
        
        // 2. 构建AI分析请求
        AIAnalysisRequest request = buildAnalysisRequest(history, userInput);
        
        // 3. 调用AI模型分析
        AIModelClient aiModelClient = aiModelFactory.getClient();
        AIAnalysisResponse response = aiModelClient.analyzeRequirement(request);
        
        // 4. 计算信心指数
        int confidenceScore = calculateConfidenceScore(response);
        
        // 5. 生成澄清问题(如果需要)
        String clarificationQuestion = null;
        if (confidenceScore < 80) {
            clarificationQuestion = generateClarificationQuestion(response);
        }
        
        // 6. 更新对话状态
        conversationService.updateConfidenceScore(conversationId, confidenceScore);
        
        return AnalysisResult.builder()
            .confidenceScore(confidenceScore)
            .clarificationQuestion(clarificationQuestion)
            .analysisResult(response)
            .build();
    }
    
    private int calculateConfidenceScore(AIAnalysisResponse response) {
        // 信心指数计算逻辑
        // 基于提取到的关键信息完整度
        int score = 0;
        
        // 产品背景信息权重20%
        if (StringUtils.isNotBlank(response.getProductBackground())) {
            score += 20;
        }
        
        // 目标用户信息权重25%  
        if (StringUtils.isNotBlank(response.getTargetUsers())) {
            score += 25;
        }
        
        // 核心功能信息权重30%
        if (CollectionUtils.isNotEmpty(response.getCoreFeatures())) {
            score += Math.min(30, response.getCoreFeatures().size() * 10);
        }
        
        // 业务流程信息权重25%
        if (StringUtils.isNotBlank(response.getBusinessFlow())) {
            score += 25;
        }
        
        return Math.min(100, score);
    }
}
```

### 3.3 PRD生成引擎

```java
@Service
public class PRDGeneratorService {
    
    @Autowired
    private TemplateEngine templateEngine;
    
    @Autowired
    private AIModelFactory aiModelFactory;
    
    @Autowired
    private CloudStorageClient cloudStorageClient;
    
    @Autowired
    private PRDDocumentService prdDocumentService;
    
    public PRDDocument generatePRD(String conversationId, String userId) {
        // 1. 获取对话分析结果
        ConversationAnalysis analysis = getConversationAnalysis(conversationId);
        
        // 2. 选择合适的PRD模板
        PRDTemplate template = selectTemplate(analysis);
        
        // 3. 使用AI填充模板内容
        AIModelClient aiModelClient = aiModelFactory.getClient();
        PRDContent content = aiModelClient.generatePRDContent(analysis, template);
        
        // 4. 生成最终PRD文档Markdown内容
        String markdownContent = templateEngine.render(template, content);
        
        // 5. 生成文档文件名
        String fileName = generatePRDFileName(userId, conversationId, 1);
        log.info("Generated PRD fileName: {}", fileName);
        
        // 6. 上传文档到云存储
        PRDFileUploadResult uploadResult = uploadPRDToCloudStorage(
            fileName, markdownContent, userId);
        
        // 7. 保存PRD文档元信息到数据库
        PRDDocument document = new PRDDocument();
        document.setConversationId(conversationId);
        document.setUserId(userId);
        document.setTitle(content.getTitle());
        document.setFileName(fileName);
        document.setFileUrl(uploadResult.getFileUrl());
        document.setFileSize(uploadResult.getFileSize());
        document.setVersion(1);
        document.setCreateUser(userId);
        document.setUpdateUser(userId);
        
        PRDDocument savedDocument = prdDocumentService.save(document);
        log.info("PRD document saved successfully, documentId={}, fileUrl={}", 
            savedDocument.getId(), uploadResult.getFileUrl());
        
        return savedDocument;
    }
    
    /**
     * 更新PRD文档(生成新版本)
     */
    public PRDDocument updatePRD(String prdDocumentId, String userId, String newContent) {
        // 1. 获取当前文档信息
        PRDDocument currentDoc = prdDocumentService.getById(prdDocumentId);
        if (currentDoc == null) {
            throw new BusinessException("PRD document not found");
        }
        
        // 2. 生成新版本号
        int newVersion = currentDoc.getVersion() + 1;
        
        // 3. 保存当前版本到历史记录
        saveVersionHistory(currentDoc);
        
        // 4. 生成新版本文件名
        String newFileName = generatePRDFileName(userId, 
            currentDoc.getConversationId(), newVersion);
        
        // 5. 上传新版本到云存储
        PRDFileUploadResult uploadResult = uploadPRDToCloudStorage(
            newFileName, newContent, userId);
        
        // 6. 更新数据库记录
        currentDoc.setFileName(newFileName);
        currentDoc.setFileUrl(uploadResult.getFileUrl());
        currentDoc.setFileSize(uploadResult.getFileSize());
        currentDoc.setVersion(newVersion);
        currentDoc.setUpdateUser(userId);
        
        return prdDocumentService.update(currentDoc);
    }
    
    /**
     * 获取PRD文档内容
     */
    public String getPRDContent(String prdDocumentId) {
        PRDDocument document = prdDocumentService.getById(prdDocumentId);
        if (document == null) {
            throw new BusinessException("PRD document not found");
        }
        
        // 从云存储下载文档内容
        return cloudStorageClient.downloadAsString(document.getFileUrl());
    }
    
    /**
     * 生成PRD文档文件名
     * 格式: PRD_{用户ID}_{对话ID}_{版本号}_{时间戳}.md
     */
    private String generatePRDFileName(String userId, String conversationId, int version) {
        String timestamp = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
            .format(LocalDateTime.now());
        return String.format("PRD_%s_%s_v%d_%s.md", 
            userId, conversationId, version, timestamp);
    }
    
    /**
     * 上传PRD文档到云存储
     */
    private PRDFileUploadResult uploadPRDToCloudStorage(String fileName, 
            String content, String userId) {
        try {
            // 生成存储路径
            String storagePath = generateStoragePath(userId, fileName);
            
            // 转换为字节数组
            byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
            
            // 上传到云存储
            String fileUrl = cloudStorageClient.uploadBytes(
                contentBytes, storagePath, "text/markdown");
            
            log.info("PRD uploaded to cloud storage successfully, fileName={}, fileUrl={}", 
                fileName, fileUrl);
            
            return PRDFileUploadResult.builder()
                .fileUrl(fileUrl)
                .fileSize((long) contentBytes.length)
                .storagePath(storagePath)
                .build();
                
        } catch (Exception e) {
            log.error("Failed to upload PRD to cloud storage, fileName={}", fileName, e);
            throw new BusinessException("PRD upload failed: " + e.getMessage());
        }
    }
    
    /**
     * 生成云存储路径
     * 格式: /ai-prd/{年份}/{月份}/{用户ID}/文件名
     */
    private String generateStoragePath(String userId, String fileName) {
        LocalDateTime now = LocalDateTime.now();
        return String.format("/ai-prd/%d/%02d/%s/%s", 
            now.getYear(), now.getMonthValue(), userId, fileName);
    }
    
    /**
     * 保存版本历史记录
     */
    private void saveVersionHistory(PRDDocument currentDoc) {
        PRDVersionHistory history = new PRDVersionHistory();
        history.setPrdDocumentId(currentDoc.getId());
        history.setVersion(currentDoc.getVersion());
        history.setFileName(currentDoc.getFileName());
        history.setFileUrl(currentDoc.getFileUrl());
        history.setFileSize(currentDoc.getFileSize());
        history.setCreateUser(currentDoc.getUpdateUser());
        
        prdVersionHistoryService.save(history);
        log.debug("Version history saved, documentId={}, version={}", 
            currentDoc.getId(), currentDoc.getVersion());
    }
}

/**
 * PRD文件上传结果
 */
@Data
@Builder
public class PRDFileUploadResult {
    private String fileUrl;
    private Long fileSize;
    private String storagePath;
}
```

### 3.4 文件处理服务(简化版)

```java
@Service
public class FileService {
    
    @Autowired
    private CloudStorageClient cloudStorageClient;
    
    @Autowired
    private OCRService ocrService;
    
    /**
     * 处理用户上传的文件
     * 上传到云存储 + 提取文本内容，不单独存储文件元信息
     */
    public FileProcessResult processUploadedFile(MultipartFile file) {
        try {
            // 1. 文件格式验证
            validateFileFormat(file);
            
            // 2. 上传到云存储(由云存储组件处理重复文件)
            String fileUrl = cloudStorageClient.upload(file);
            
            // 3. 提取文本内容
            String extractedContent = extractContent(file);
            
            // 4. 构建元信息(存储到message的metadata字段)
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("originalName", file.getOriginalFilename());
            metadata.put("fileSize", file.getSize());
            metadata.put("mimeType", file.getContentType());
            
            return FileProcessResult.builder()
                .fileUrl(fileUrl)
                .extractedContent(extractedContent)
                .metadata(metadata)
                .success(true)
                .build();
                
        } catch (Exception e) {
            log.warn("File processing failed, fileName={}", file.getOriginalFilename(), e);
            return FileProcessResult.builder()
                .success(false)
                .errorMessage(e.getMessage())
                .build();
        }
    }
    
    private String extractContent(MultipartFile file) {
        String contentType = file.getContentType();
        
        if (contentType.startsWith("image/")) {
            // 图片OCR识别手绘草图
            return ocrService.extractText(file);
        } else if (contentType.contains("pdf")) {
            // PDF需求文档文本提取
            return pdfTextExtractor.extract(file);
        } else if (contentType.contains("word")) {
            // Word需求文档文本提取  
            return wordTextExtractor.extract(file);
        } else {
            // 纯文本需求文件
            return new String(file.getBytes(), StandardCharsets.UTF_8);
        }
    }
}
```

## 4. 性能与安全设计

### 4.1 单体架构性能优化策略

1. **缓存策略**
   - Redis缓存用户会话信息，减少数据库查询
   - AI分析结果缓存，避免重复调用DeepSeek API
   - PRD模板缓存，提升文档生成速度
   - 本地缓存配合Redis，双重缓存提升性能

2. **异步处理**
   - 文件上传异步处理，提升用户体验
   - AI分析采用异步调用，支持长时间处理
   - 使用@Async注解实现方法异步执行

3. **数据库优化**
   - 合理设计索引，优化查询性能
   - 单库单表设计，简化部署和维护
   - 连接池优化，控制数据库连接数

4. **单体架构优势**
   - 模块间直接方法调用，无网络传输开销
   - 事务处理简单，数据一致性容易保证
   - 部署简单，启动速度快

### 4.2 安全策略

1. **身份认证**
   - JWT Token认证
   - 对接权限中心

2. **数据安全**
   - 敏感数据加密存储
   - API接口限流
   - 文件上传安全检查

3. **隐私保护**
   - 用户数据隔离
   - 分享链接时效控制
   - 数据删除策略

## 5. 监控与运维

### 5.1 监控指标

- **业务指标：** 用户活跃度、对话完成率、PRD生成成功率
- **技术指标：** 接口响应时间、数据库性能、缓存命中率
- **AI指标：** 模型调用成功率、信心指数准确性

### 5.2 日志策略

```java
// 统一日志格式示例
log.info("User conversation started, userId={}, conversationId={}", userId, conversationId);
log.debug("AI analysis request sent, conversationId={}, inputLength={}", conversationId, inputLength);
log.warn("Confidence score below threshold, conversationId={}, score={}", conversationId, score);
```

## 6. 未来扩展策略

### 6.1 微服务化迁移预案

当系统发展到一定规模时，可按以下策略进行微服务化改造：

**迁移时机判断：**
- 用户量超过1万，日活跃超过1000
- 单体应用性能出现瓶颈
- 团队规模超过10人，需要并行开发
- 业务复杂度需要独立演进

**迁移策略：**

```mermaid
graph LR
    A[当前单体架构] --> B[垂直拆分]
    B --> C[数据库拆分]
    C --> D[服务治理]
    
    subgraph "第一阶段"
        B1[按模块拆分]
        B2[保持共享数据库]
    end
    
    subgraph "第二阶段"
        C1[数据库按模块拆分]
        C2[实现数据同步]
    end
    
    subgraph "第三阶段"
        D1[引入注册中心]
        D2[配置中心]
        D3[监控告警]
    end
```

**代码改造建议：**
- 当前的@Service注解可直接改为@RMIService
- 模块间接口已预留，可快速改造为Facade接口
- 数据库设计已考虑拆分，减少改造成本

### 6.2 成本效益分析

**当前单体架构成本：**
- **开发成本：** 低，一个团队即可维护
- **部署成本：** 低，单机部署
- **运维成本：** 低，统一监控和管理
- **学习成本：** 低，技术栈简单

**预期效果：**
- 快速上线验证产品价值
- 降低试点阶段的技术风险
- 为后续规模化打下基础

---

## 7. 调整说明

### 7.1 PRD存储策略调整

根据需求调整，PRD文档存储策略从**数据库存储**调整为**云存储**方式：

**调整前**：
- PRD内容直接存储在MySQL数据库的`content`字段
- 版本历史内容也存储在数据库中

**调整后**：
- PRD文档以Markdown文件形式存储在易宝云存储组件
- 数据库只存储文档元信息：`file_name`、`file_url`、`file_size`等
- 版本历史也采用相同策略，存储历史版本的文件信息

### 7.2 文档命名规则明确

制定了规范的PRD文档命名规则：

**命名格式**：`PRD_{用户ID}_{对话ID}_{版本号}_{时间戳}.md`

**存储路径**：`/ai-prd/{年份}/{月份}/{用户ID}/文件名`

**示例**：
- 文件名：`PRD_user001_conv12345_v1_20241219143052.md`
- 路径：`/ai-prd/2024/12/user001/PRD_user001_conv12345_v1_20241219143052.md`

### 7.3 调整优势

1. **存储成本优化**：大文档存储在云存储，减少数据库存储压力
2. **性能提升**：数据库查询更快，文档内容按需加载
3. **扩展性增强**：支持更大的PRD文档，不受数据库字段长度限制
4. **文件管理**：利用云存储的文件管理能力，支持直接下载
5. **版本控制**：每个版本独立文件，便于管理和回滚

### 7.4 技术实现要点

- **CloudStorageClient**：统一的云存储客户端接口
- **文档生成流程**：生成内容 → 上传云存储 → 保存元信息到数据库
- **权限控制**：基于用户ID的文档访问权限验证
- **版本管理**：新版本生成新文件，历史版本保留在云存储

---

**方案完成度：98%**

此技术方案采用**单体架构**设计，专为AI PRD助手试点阶段量身定制。PRD文档采用**云存储**方式，优化了存储成本和性能表现。在保证功能完整性的同时，最大化降低了开发和部署成本，适合快速验证产品价值。当业务规模增长时，可按预设策略平滑迁移至微服务架构。