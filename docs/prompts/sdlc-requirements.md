# --- 元数据 (Metadata) ---
id: string # 需求的唯一标识符, e.g., "REQ-2025-001"
version: string # 当前设计文档的版本号, e.g., 1.0.0
status: "Draft" | "Reviewing" | "Approved" | "Rejected" # 当前需求状态
author: string # 需求创建者
productOwner: string # 需求负责人
creationTimestamp: datetime # 创建时间戳, e.g., "2023-10-27T10:00:00Z" (ISO 8601 format recommended)
lastUpdateTimestamp: datetime # 最后更新时间戳, e.g., "2023-10-27T10:00:00Z" (ISO 8601 format recommended)

# --- 业务上下文 (Business Context) - 新增部分 ---
businessContext:
  background: string # 描述当前的情况、问题或机会
  purpose: string # 解释为什么要做这个需求，它试图解决的核心问题是什么
  smartGoals: # SMART 目标列表
    - goal: string # 总体目标描述 (e.g., "提升新用户次月留存率")
      specific: string # 具体做什么 (e.g., "开发一个新用户引导任务系统")
      measurable: string # 如何衡量 (e.g., "次月留存率指标从20%提升到25%")
      achievable: string # 是否可实现 (e.g., "基于现有用户数据分析，5%的提升是可行的")
      relevant: string # 与目的的相关性 (e.g., "提升留存率是当前阶段的核心业务指标")
      time_bound: string # 时间限制 (e.g., "在Q3结束前上线")

# --- 功能性需求 (Functional Requirements) ---
epic: string # 需求所属的史诗级故事
userStories:
  - id: string # 用户故事的唯一ID, e.g., "US-001"
    as_a: string # 角色 (e.g., "作为一名网站用户")
    i_want_to: string # 意图 (e.g., "我想要重置我的密码")
    so_that: string # 价值 (e.g., "以便于在我忘记密码时能够重新访问我的账户")
    acceptanceCriteria: # 验收标准列表，使用结构化格式提升机器可读性，便于自动化测试用例生成
      - id: string # 验收标准的唯一标识 (e.g., "AC-001")，可选字段
        given: string # 前置条件 (e.g., "用户忘记了密码")
        when: string # 触发条件 (e.g., "用户输入正确的重置密码令牌并提供新密码")
        then: string # 预期结果 (e.g., "用户的密码应该被成功更新")
      - id: string # 验收标准的唯一标识 (e.g., "AC-002")，可选字段
        given: string # 前置条件 (e.g., "用户输入了无效的重置令牌")
        when: string # 触发条件 (e.g., "用户尝试重置密码")
        then: string # 预期结果 (e.g., "系统显示错误消息并拒绝密码重置")

# --- 非功能性需求 (Non-Functional Requirements / NFRs) ---
nfrs:
  performance:
    - description: string # 描述 (e.g., "用户登录接口响应时间")
      metric: "P99 Latency" | "Throughput" # 关键指标
      target: string # 目标值 (e.g., "< 300ms", "1000 RPS")
  availability:
    - description: string # 描述 (e.g., "核心认证服务")
      metric: "Uptime Percentage"
      target: "99.9%" | "99.99%"
  security:
    - description: string # 描述 (e.g., "用户密码存储")
      requirement: string # 具体要求 (e.g., "必须使用Bcrypt进行哈希加盐处理")
  scalability:
    - description: string # 描述 (e.g., "预期用户增长")
      metric: "Concurrent Users"
      target_1yr: number # 1年内目标 (e.g., 10000)
  data_management: # 数据管理
    - description: string # 描述 (e.g., "用户操作日志")
      retention_period: string # 保留周期 (e.g., "30 days")

# --- 约束与依赖 (Constraints & Dependencies) ---
constraints:
  - string # 技术或业务约束 (e.g., "必须使用公司内部的SSO服务进行认证")
dependencies:
  - string # 外部系统或团队依赖 (e.g., "依赖'支付网关团队'提供新的API")
