# --- 元数据 (Metadata) ---
id: string # 任务清单的唯一ID, e.g., "TASK-LIST-2025-001"
version: string # 当前设计文档的版本号, e.g., 1.0.0
relatedRequirementId: string # 关联的需求ID, e.g., "REQ-2025-001"
relatedDesignId: string # 关联的设计ID, e.g., "DSGN-2025-001"
status: "ToDo" | "InProgress" | "Review" | "Testing" | "Blocked" | "Done" # 整个任务清单的宏观状态
creationTimestamp: datetime # 创建时间戳, e.g., "2023-10-27T10:00:00Z" (ISO 8601 format recommended)
lastUpdateTimestamp: datetime # 最后更新时间戳, e.g., "2023-10-27T10:00:00Z" (ISO 8601 format recommended)

# --- 任务列表 (Task Breakdown Structure) ---
# 采用扁平化列表，通过 parentId 建立层级关系，更利于机器处理和生成依赖图

# --- 父子任务状态联动规则 (Parent-Child Task Status Rules) ---
# 为确保任务状态的一致性和准确的项目进度跟踪，父任务的状态应遵循以下聚合规则：
# 1. 父任务状态自动聚合：父任务的状态应基于其所有直接子任务的状态自动计算
# 2. 状态聚合逻辑：
#    - 当所有子任务状态均为 "Done" 时，父任务状态自动更新为 "Done"
#    - 当任一子任务状态为 "Blocked" 时，父任务状态应为 "Blocked"
#    - 当存在子任务状态为 "InProgress" 且无 "Blocked" 时，父任务状态为 "InProgress"
#    - 当所有子任务状态均为 "ToDo" 时，父任务状态为 "ToDo"
# 3. 手动覆盖：在特殊情况下，可以手动设置父任务状态，但应在注释中说明原因
# 4. 状态一致性检查：建议定期检查父子任务状态的一致性，确保聚合规则得到正确执行
tasks:
  # 顶级任务 (对应您清单中的 "1. ...", "2. ...")
  - id: "T1"
    parentId: null # null 表示顶级任务
    title: "设置基础设施和数据库结构"
    type: "Chore" # 任务类型：Feature, Test, Chore, Documentation
    status: "Done"
    relatedRequirementIds: ["US-001", "US-002", "US-003"] # 追溯到具体的需求点
    description: "创建所有必要的数据库表结构和项目骨架"
    # ... 其他字段如 estimatedHours, assignee 等可按需添加

  # 子任务 (对应您清单中的 "2.1 ...", "2.2 ...")
  - id: "T2"
    parentId: null
    title: "实现领域模型"
    type: "Feature"
    status: "Done"
  - id: "T2.1"
    parentId: "T2" # 归属于 T2 任务
    title: "创建核心领域实体和值对象"
    type: "Feature"
    status: "Done"
    relatedRequirementIds: ["US-001", "US-002", "US-003"]
    relatedDesignElement: "DomainModel:Service, Capability" # 关联到设计文档的具体元素
    dependsOn: ["T1"] # 明确的依赖关系，必须在T1完成后才能开始
    definitionOfDone: # 明确的完成标准
      - "Service, Capability, Requirement 等核心领域模型代码已实现"
      - "所有相关值对象的代码已实现"

  - id: "T5"
    parentId: null
    title: "实现API层"
    type: "Feature"
    status: "InProgress" # 根据状态聚合规则：存在子任务但未全部完成，故为InProgress
  - id: "T5.1"
    parentId: "T5"
    title: "实现服务注册与管理API"
    type: "Feature"
    status: "Done"
    dependsOn: ["T4.1"] # 依赖应用服务的实现
    relatedRequirementIds: ["US-001", "US-002", "US-006"]
    relatedDesignElement: "APIEndpoint:/api/v1/services (POST, PUT, DELETE)"

  - id: "T7"
    parentId: null
    title: "实现单元测试"
    type: "Test" # 任务类型为测试
    status: "Done"
  - id: "T7.1"
    parentId: "T7"
    title: "领域模型测试"
    type: "Test"
    status: "Done"
    dependsOn: ["T2"] # 依赖领域模型的实现

# --- 状态聚合示例说明 (Status Aggregation Examples) ---
# 基于上述任务示例的状态聚合分析：
# - T2 (父任务): status="Done" ✓ 正确，因为所有子任务(T2.1)都是"Done"
# - T5 (父任务): status="InProgress" ✓ 正确，因为存在子任务但可能还有其他未完成的子任务
# - T7 (父任务): status="Done" ✓ 正确，因为所有子任务(T7.1)都是"Done"
#
# 注意：如果T5还有其他子任务（如T5.2, T5.3）且状态为"ToDo"或"InProgress"，
# 则T5的"InProgress"状态是符合聚合规则的。
