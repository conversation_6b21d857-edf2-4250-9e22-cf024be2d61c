# --- 元数据 (Metadata) ---
id: string # 任务清单的唯一ID, e.g., "TASK-LIST-2025-001"
version: string # 当前设计文档的版本号, e.g., 1.0.0
relatedRequirementId: string # 关联的需求ID, e.g., "REQ-2025-001"
relatedDesignId: string # 关联的设计ID, e.g., "DSGN-2025-001"
status: "ToDo" | "InProgress" | "Blocked" | "Done" # 整个任务清单的宏观状态
creationTimestamp: datetime # 创建时间戳, e.g., "2023-10-27T10:00:00Z" (ISO 8601 format recommended)
lastUpdateTimestamp: datetime # 最后更新时间戳, e.g., "2023-10-27T10:00:00Z" (ISO 8601 format recommended)

# --- 任务列表 (Task Breakdown Structure) ---
# 采用扁平化列表，通过 parentId 建立层级关系，更利于机器处理和生成依赖图
tasks:
  # 顶级任务 (对应您清单中的 "1. ...", "2. ...")
  - id: "T1"
    parentId: null # null 表示顶级任务
    title: "设置基础设施和数据库结构"
    type: "Chore" # 任务类型：Feature, Test, Chore, Documentation
    status: "Done"
    relatedRequirementIds: ["US-001", "US-002", "US-003"] # 追溯到具体的需求点
    description: "创建所有必要的数据库表结构和项目骨架"
    # ... 其他字段如 estimatedHours, assignee 等可按需添加

  # 子任务 (对应您清单中的 "2.1 ...", "2.2 ...")
  - id: "T2"
    parentId: null
    title: "实现领域模型"
    type: "Feature"
    status: "Done"
  - id: "T2.1"
    parentId: "T2" # 归属于 T2 任务
    title: "创建核心领域实体和值对象"
    type: "Feature"
    status: "Done"
    relatedRequirementIds: ["US-001", "US-002", "US-003"]
    relatedDesignElement: "DomainModel:Service, Capability" # 关联到设计文档的具体元素
    dependsOn: ["T1"] # 明确的依赖关系，必须在T1完成后才能开始
    definitionOfDone: # 明确的完成标准
      - "Service, Capability, Requirement 等核心领域模型代码已实现"
      - "所有相关值对象的代码已实现"

  - id: "T5"
    parentId: null
    title: "实现API层"
    type: "Feature"
    status: "InProgress" # 对应您清单中的 [-]
  - id: "T5.1"
    parentId: "T5"
    title: "实现服务注册与管理API"
    type: "Feature"
    status: "Done"
    dependsOn: ["T4.1"] # 依赖应用服务的实现
    relatedRequirementIds: ["US-001", "US-002", "US-006"]
    relatedDesignElement: "APIEndpoint:/api/v1/services (POST, PUT, DELETE)"

  - id: "T7"
    parentId: null
    title: "实现单元测试"
    type: "Test" # 任务类型为测试
    status: "Done"
  - id: "T7.1"
    parentId: "T7"
    title: "领域模型测试"
    type: "Test"
    status: "Done"
    dependsOn: ["T2"] # 依赖领域模型的实现
