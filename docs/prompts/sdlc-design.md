# --- 元数据 (Metadata) ---
id: string # 设计文档的唯一ID, 由系统生成, e.g., "DSGN-2025-001"
version: string # 当前设计文档的版本号, e.g., 1.0.0
status: "Draft" | "Reviewing" | "Approved" | "Implemented" # 设计文档的生命周期状态
author: string # 设计文档创建者
relatedRequirementId: string # 必须关联到一份已批准的需求ID, 确保设计的可追溯性, e.g., "REQ-2025-001"
creationTimestamp: datetime # 创建时间戳, e.g., "2023-10-27T10:00:00Z" (ISO 8601 format recommended)
lastUpdateTimestamp: datetime # 最后更新时间戳, e.g., "2023-10-27T10:00:00Z" (ISO 8601 format recommended)

# --- 概述 (Overview) ---
overview:
  purpose: string # 描述该系统或功能的核心目的和价值
  scope: string # 明确定义本次设计的边界和范围

# --- 架构设计 (Architecture) ---
architecture:
  pattern: "Microservices" | "Serverless" | "Layered Monolith" | "Event-Driven" | "Hexagonal" # 阐明本次设计遵循的核心架构范式，这是最高级别的技术决策
  layers: # 描述系统的逻辑分层，灵感来自您文档中的分层描述
    - name: "Interface Layer"
      responsibility: "定义API契约，处理外部请求和响应转换，实现RESTful API。"
    - name: "Application Layer"
      responsibility: "协调领域对象和服务，实现具体用例，如服务注册、发现和需求匹配。"
    - name: "Domain Layer"
      responsibility: "包含核心业务逻辑和领域模型，如Service, Capability等实体。"
    - name: "Infrastructure Layer"
      responsibility: "提供技术实现细节，如数据库访问、消息队列集成、外部服务调用。"
  diagrams: # 对应您文档中的 "系统架构图"，可嵌入Mermaid.js代码
    - name: "系统分层架构图"
      type: "Mermaid-Graph-TD"
      code: |
        graph TD
            Client[客户端] --> InterfaceLayer[接口层]
            InterfaceLayer --> ApplicationLayer[应用层]
            ApplicationLayer --> DomainLayer[领域层]
            ApplicationLayer --> InfrastructureLayer[基础设施层 (通过仓储接口)]
            DomainLayer -- 依赖 --> DomainLayer
            InfrastructureLayer --> ExternalSystems[(外部系统/数据库)]

# --- C4模型核心元素 (Core C4 Model Elements) ---
# C4模型有助于分层、清晰地展示软件架构。这里我们关注L2(容器)和L3(组件)。
containers: # “容器”指可独立部署和运行的单元，如一个服务、一个数据库、一个前端应用
  - name: string # 容器的唯一名称, e.g., "UserService"
    technology: string # 实现该容器的核心技术, e.g., "Go 1.21 / Gin"
    description: string # 精确描述该容器的职责, e.g., "负责用户管理、认证和授权"
    interfaces: # 定义该容器如何与外界交互
      - to: string # 交互的目标容器或外部系统, e.g., "AuthDatabase"
        protocol: "HTTPS/REST" | "gRPC" | "AMQP" | "Database Connection" # 交互所使用的协议
        description: string # 描述交互的目的, e.g., "读写用户信息"
c4Components: # “组件”是容器内部的逻辑模块划分，代表了代码层面的组织结构
  - container: string # 该组件所属的容器名称, e.g., "UserService"
    name: string # 组件名称, e.g., "PasswordHasher"
    responsibility: string # 该组件的核心职责
    technology: string # 实现该组件的核心库或框架, e.g., "golang.org/x/crypto/bcrypt"
    description: string # 组件的单一职责描述, e.g., "处理用户密码的哈希和验证"
    # 将您文档中的Java接口定义，抽象为语言无关的接口契约描述
    interfaces:
      - name: string # e.g., "PasswordHasher"
        methods:
          - name: "hashPassword"
            params: [{name: "plainPassword", type: "string"}]
            returnType: "string"
            description: "将明文密码进行哈希加密"
          - name: "verifyPassword"
            params: [{name: "plainPassword", type: "string"}, {name: "hashedPassword", type: "string"}]
            returnType: "boolean"
            description: "验证明文密码与哈希密码是否匹配"
  # 更多组件示例，展示不同类型的内部构建块
  - container: string # 该组件所属的容器名称, e.g., "UserService"
    name: string # 组件名称, e.g., "UserProfileRepository"
    responsibility: string # 该组件的核心职责, e.g., "管理用户档案数据的持久化操作"
    technology: string # 实现该组件的核心库或框架, e.g., "GORM / database/sql"
    description: string # 组件的单一职责描述, e.g., "提供用户档案的CRUD操作和查询功能"
  - container: string # 该组件所属的容器名称, e.g., "UserService"
    name: string # 组件名称, e.g., "EmailValidator"
    responsibility: string # 该组件的核心职责, e.g., "验证邮箱地址格式和有效性"
    technology: string # 实现该组件的核心库或框架, e.g., "regexp / validator"
    description: string # 组件的单一职责描述, e.g., "提供邮箱格式验证和域名有效性检查"

# --- 数据模型设计 (Data Model Design) ---
dataModel: # 描述此方案需要持久化的数据结构
  - name: string # 数据表的逻辑名称, e.g., "users"
    type: "PostgreSQL Table" | "MongoDB Collection" | "Redis Key" # 明确存储类型
    description: string # 描述此数据表的用途, e.g., "存储平台所有用户的核心信息"
    schema: # 详细的字段定义
      - field: string # 字段名, e.g., "email"
        dataType: string # 数据类型, e.g., "VARCHAR(254)", "INTEGER", "TEXT", "BOOLEAN", "TIMESTAMP"
        constraints: "UNIQUE, NOT NULL" # 约束条件, e.g., "主键、唯一、非空"
    indexes: # 关键索引策略，直接关系到查询性能
      - name: string # 索引名称, e.g., "idx_users_email"
        columns: [string] # 组成索引的列
        type: "BTREE" # 索引类型

# --- OpenAPI 3.0 规范定义 (OpenAPI 3.0 Specification) ---
openapi: "3.0.3" # OpenAPI版本
info:
  title: string # API标题, e.g., "用户服务API"
  description: string # API描述, e.g., "提供用户管理、认证和授权相关的RESTful API"
  version: string # API版本, e.g., "1.0.0"
  contact:
    name: string # 联系人姓名, e.g., "API团队"
    email: string # 联系邮箱, e.g., "<EMAIL>"
  license:
    name: string # 许可证名称, e.g., "MIT"
    url: string # 许可证URL, e.g., "https://opensource.org/licenses/MIT"

servers: # API服务器列表
  - url: string # 服务器URL, e.g., "https://api.example.com/v1"
    description: string # 服务器描述, e.g., "生产环境"
  - url: string # 服务器URL, e.g., "https://staging-api.example.com/v1"
    description: string # 服务器描述, e.g., "测试环境"

paths: # API路径定义，遵循OpenAPI 3.0规范
  "/api/v1/services": # 服务管理API示例
    post:
      summary: "创建新服务"
      description: "注册一个新的服务到系统中"
      tags: ["服务管理"]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateServiceRequest"
      responses:
        "201":
          description: "服务创建成功"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ServiceDTO"
        "400":
          description: "请求参数无效"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "409":
          description: "服务已存在"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    get:
      summary: "获取服务列表"
      description: "分页获取系统中的服务列表"
      tags: ["服务管理"]
      parameters:
        - name: "page"
          in: "query"
          description: "页码，从1开始"
          required: false
          schema:
            type: "integer"
            minimum: 1
            default: 1
        - name: "size"
          in: "query"
          description: "每页大小"
          required: false
          schema:
            type: "integer"
            minimum: 1
            maximum: 100
            default: 20
        - name: "status"
          in: "query"
          description: "服务状态过滤"
          required: false
          schema:
            type: "string"
            enum: ["active", "inactive", "maintenance"]
      responses:
        "200":
          description: "获取成功"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PagedServiceResponse"
        "400":
          description: "请求参数无效"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  "/api/v1/services/{serviceId}": # 单个服务操作API示例
    get:
      summary: "获取服务详情"
      description: "根据服务ID获取服务的详细信息"
      tags: ["服务管理"]
      parameters:
        - name: "serviceId"
          in: "path"
          description: "服务唯一标识符"
          required: true
          schema:
            type: "string"
            pattern: "^[a-zA-Z0-9-]+$"
      responses:
        "200":
          description: "获取成功"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ServiceDTO"
        "404":
          description: "服务不存在"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    put:
      summary: "更新服务信息"
      description: "更新指定服务的信息"
      tags: ["服务管理"]
      parameters:
        - name: "serviceId"
          in: "path"
          description: "服务唯一标识符"
          required: true
          schema:
            type: "string"
            pattern: "^[a-zA-Z0-9-]+$"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateServiceRequest"
      responses:
        "200":
          description: "更新成功"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ServiceDTO"
        "400":
          description: "请求参数无效"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: "服务不存在"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    delete:
      summary: "删除服务"
      description: "从系统中删除指定的服务"
      tags: ["服务管理"]
      parameters:
        - name: "serviceId"
          in: "path"
          description: "服务唯一标识符"
          required: true
          schema:
            type: "string"
            pattern: "^[a-zA-Z0-9-]+$"
      responses:
        "204":
          description: "删除成功"
        "404":
          description: "服务不存在"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "409":
          description: "服务正在使用中，无法删除"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

components: # OpenAPI 3.0 组件定义
  schemas: # 数据模型定义
    # 服务相关的数据传输对象
    ServiceDTO:
      type: "object"
      description: "服务信息的数据传输对象"
      properties:
        id:
          type: "string"
          description: "服务唯一标识符"
          example: "service-001"
        name:
          type: "string"
          description: "服务名称"
          example: "用户认证服务"
        description:
          type: "string"
          description: "服务描述"
          example: "提供用户登录、注册和权限验证功能"
        status:
          type: "string"
          enum: ["active", "inactive", "maintenance"]
          description: "服务状态"
          example: "active"
        createTime:
          type: "string"
          format: "date-time"
          description: "创建时间"
          example: "2023-10-27T10:00:00Z"
        updateTime:
          type: "string"
          format: "date-time"
          description: "更新时间"
          example: "2023-10-27T10:00:00Z"
      required: ["id", "name", "status"]

    CreateServiceRequest:
      type: "object"
      description: "创建服务的请求体"
      properties:
        name:
          type: "string"
          description: "服务名称"
          example: "用户认证服务"
          minLength: 1
          maxLength: 100
        description:
          type: "string"
          description: "服务描述"
          example: "提供用户登录、注册和权限验证功能"
          maxLength: 500
        tags:
          type: "array"
          description: "服务标签"
          items:
            type: "string"
          example: ["authentication", "user-management"]
      required: ["name"]

    UpdateServiceRequest:
      type: "object"
      description: "更新服务的请求体"
      properties:
        name:
          type: "string"
          description: "服务名称"
          example: "用户认证服务"
          minLength: 1
          maxLength: 100
        description:
          type: "string"
          description: "服务描述"
          example: "提供用户登录、注册和权限验证功能"
          maxLength: 500
        status:
          type: "string"
          enum: ["active", "inactive", "maintenance"]
          description: "服务状态"
          example: "active"
        tags:
          type: "array"
          description: "服务标签"
          items:
            type: "string"
          example: ["authentication", "user-management"]

    PagedServiceResponse:
      type: "object"
      description: "分页服务列表响应"
      properties:
        data:
          type: "array"
          description: "服务列表"
          items:
            $ref: "#/components/schemas/ServiceDTO"
        pagination:
          $ref: "#/components/schemas/PaginationInfo"
      required: ["data", "pagination"]

    PaginationInfo:
      type: "object"
      description: "分页信息"
      properties:
        page:
          type: "integer"
          description: "当前页码"
          example: 1
          minimum: 1
        size:
          type: "integer"
          description: "每页大小"
          example: 20
          minimum: 1
        total:
          type: "integer"
          description: "总记录数"
          example: 100
          minimum: 0
        totalPages:
          type: "integer"
          description: "总页数"
          example: 5
          minimum: 0
      required: ["page", "size", "total", "totalPages"]

    ErrorResponse:
      type: "object"
      description: "错误响应"
      properties:
        code:
          type: "string"
          description: "错误代码"
          example: "INVALID_PARAMETER"
        message:
          type: "string"
          description: "错误消息"
          example: "请求参数无效"
        details:
          type: "array"
          description: "错误详情"
          items:
            type: "string"
          example: ["name字段不能为空", "description字段长度不能超过500字符"]
        timestamp:
          type: "string"
          format: "date-time"
          description: "错误发生时间"
          example: "2023-10-27T10:00:00Z"
        traceId:
          type: "string"
          description: "请求追踪ID"
          example: "d1fab1afe99d459199475f032f4034df"
      required: ["code", "message", "timestamp"]

  # 可重用的参数定义
  parameters:
    ServiceIdParam:
      name: "serviceId"
      in: "path"
      description: "服务唯一标识符"
      required: true
      schema:
        type: "string"
        pattern: "^[a-zA-Z0-9-]+$"

    PageParam:
      name: "page"
      in: "query"
      description: "页码，从1开始"
      required: false
      schema:
        type: "integer"
        minimum: 1
        default: 1

    SizeParam:
      name: "size"
      in: "query"
      description: "每页大小"
      required: false
      schema:
        type: "integer"
        minimum: 1
        maximum: 100
        default: 20

  # 可重用的响应定义
  responses:
    NotFound:
      description: "资源不存在"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

    BadRequest:
      description: "请求参数无效"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

    InternalServerError:
      description: "服务器内部错误"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"

  # 安全方案定义
  securitySchemes:
    BearerAuth:
      type: "http"
      scheme: "bearer"
      bearerFormat: "JWT"
      description: "JWT Bearer Token认证"

    ApiKeyAuth:
      type: "apiKey"
      in: "header"
      name: "X-API-Key"
      description: "API Key认证"

# 全局安全要求
security:
  - BearerAuth: []

# --- 非功能性需求 (Non-Functional Requirements / NFRs) ---
# 定义系统的性能、可用性、安全性等质量属性要求

## 性能需求 (Performance Requirements)
performance:
  # API响应时间要求
  responseTime:
    - operation: "用户认证API (/api/v1/auth/login)"
      metric: "P95 Response Time"
      target: "< 200ms"
      description: "95%的登录请求应在200毫秒内完成"
      testCondition: "正常负载下，数据库连接正常"

    - operation: "服务查询API (/api/v1/services)"
      metric: "P99 Response Time"
      target: "< 500ms"
      description: "99%的服务查询请求应在500毫秒内完成"
      testCondition: "包含分页查询，每页20条记录"

    - operation: "服务创建API (/api/v1/services)"
      metric: "P95 Response Time"
      target: "< 1000ms"
      description: "95%的服务创建请求应在1秒内完成"
      testCondition: "包含数据验证和持久化操作"

    - operation: "服务更新API (/api/v1/services/{id})"
      metric: "P95 Response Time"
      target: "< 800ms"
      description: "95%的服务更新请求应在800毫秒内完成"
      testCondition: "包含数据验证和更新操作"

  # 吞吐量要求
  throughput:
    - operation: "服务查询API"
      metric: "Requests Per Second (RPS)"
      target: "> 1000 RPS"
      description: "系统应支持每秒1000次以上的查询请求"
      testCondition: "单实例，正常负载"

    - operation: "服务创建API"
      metric: "Requests Per Second (RPS)"
      target: "> 100 RPS"
      description: "系统应支持每秒100次以上的创建请求"
      testCondition: "单实例，包含数据库写入操作"

    - operation: "用户认证API"
      metric: "Requests Per Second (RPS)"
      target: "> 500 RPS"
      description: "系统应支持每秒500次以上的认证请求"
      testCondition: "单实例，包含JWT验证"

  # 并发用户数要求
  concurrency:
    - scenario: "正常业务负载"
      metric: "Concurrent Users"
      target: "> 5000"
      description: "系统应支持5000个并发用户同时在线"
      testCondition: "混合读写操作，70%读取，30%写入"

    - scenario: "峰值负载"
      metric: "Concurrent Users"
      target: "> 10000"
      description: "系统应支持10000个并发用户的峰值负载"
      testCondition: "主要为查询操作，持续时间不超过10分钟"

  # 资源利用率要求
  resourceUtilization:
    - resource: "CPU使用率"
      metric: "Average CPU Utilization"
      target: "< 70%"
      description: "正常负载下CPU平均使用率应低于70%"
      testCondition: "单实例，1000 RPS混合负载"

    - resource: "内存使用率"
      metric: "Memory Utilization"
      target: "< 80%"
      description: "内存使用率应低于80%"
      testCondition: "包含缓存和连接池"

    - resource: "数据库连接池"
      metric: "Connection Pool Utilization"
      target: "< 85%"
      description: "数据库连接池使用率应低于85%"
      testCondition: "峰值负载下"

## 可用性需求 (Availability Requirements)
availability:
  - component: "核心API服务"
    metric: "Uptime Percentage"
    target: "99.9%"
    description: "核心API服务年度可用性应达到99.9%"
    allowedDowntime: "8.76小时/年"

  - component: "数据库服务"
    metric: "Uptime Percentage"
    target: "99.95%"
    description: "数据库服务年度可用性应达到99.95%"
    allowedDowntime: "4.38小时/年"

  - component: "认证服务"
    metric: "Uptime Percentage"
    target: "99.99%"
    description: "认证服务年度可用性应达到99.99%"
    allowedDowntime: "52.56分钟/年"

## 可扩展性需求 (Scalability Requirements)
scalability:
  - dimension: "水平扩展"
    metric: "Scale-out Capability"
    target: "支持10倍扩展"
    description: "系统应支持从1个实例扩展到10个实例"
    constraint: "无状态设计，负载均衡"

  - dimension: "数据增长"
    metric: "Data Volume"
    target: "支持1000万条记录"
    description: "系统应支持1000万条服务记录的存储和查询"
    constraint: "查询性能不显著下降"

  - dimension: "用户增长"
    metric: "User Growth"
    target: "支持100万注册用户"
    description: "系统应支持100万注册用户"
    constraint: "认证和授权性能保持稳定"

## 安全性需求 (Security Requirements)
security:
  - category: "认证"
    requirement: "所有API必须通过JWT进行认证"
    implementation: "Spring Security + JWT Token验证"
    testCriteria: "未认证请求返回401状态码"

  - category: "授权"
    requirement: "基于角色的访问控制(RBAC)"
    implementation: "Spring Security + 自定义权限注解"
    testCriteria: "无权限用户访问受保护资源返回403状态码"

  - category: "数据加密"
    requirement: "敏感数据必须加密存储"
    implementation: "密码使用Bcrypt，敏感字段使用AES-256"
    testCriteria: "数据库中无明文敏感信息"

  - category: "传输安全"
    requirement: "所有API通信必须使用HTTPS"
    implementation: "TLS 1.2+，强制HTTPS重定向"
    testCriteria: "HTTP请求自动重定向到HTTPS"

## 可维护性需求 (Maintainability Requirements)
maintainability:
  - aspect: "代码质量"
    metric: "Code Coverage"
    target: "> 80%"
    description: "单元测试覆盖率应超过80%"

  - aspect: "部署时间"
    metric: "Deployment Time"
    target: "< 5分钟"
    description: "应用部署时间应少于5分钟"

  - aspect: "故障恢复"
    metric: "Mean Time To Recovery (MTTR)"
    target: "< 30分钟"
    description: "系统故障后应在30分钟内恢复"

## 监控和观测性需求 (Observability Requirements)
observability:
  - type: "应用指标"
    metrics: ["请求量", "响应时间", "错误率", "CPU/内存使用率"]
    retention: "30天"
    alerting: "响应时间P95 > 1秒或错误率 > 1%时告警"

  - type: "业务指标"
    metrics: ["服务注册数", "用户活跃度", "API调用分布"]
    retention: "90天"
    alerting: "业务指标异常波动时告警"

  - type: "日志"
    level: "INFO级别以上"
    retention: "7天"
    structure: "结构化JSON格式"

  - type: "链路追踪"
    sampling: "10%采样率"
    retention: "3天"
    coverage: "所有外部调用和关键内部调用"

# --- 技术栈选型 (Technology Stack) ---
technologyStack: # 明确列出所有技术选型及其原因
  - category: "Language" | "Framework" | "Database" | "Cache" | "MessageQueue" | "Deployment" # 技术分类
    choice: string # 具体选择, e.g., "Go 1.21", "Kubernetes"
    reason: string # 做出此选择的关键理由，需关联NFR, e.g., "Go因其高并发性能和静态类型安全被选中，满足性能NFR"

# --- 错误处理 (Error Handling) ---
# 对应您文档中的 "Error Handling"
errorHandling:
  strategy: "采用统一的全局异常处理器，将特定异常映射为标准化的HTTP响应。"
  exceptions:
    - name: "ServiceNotFoundException"
      type: "Domain Exception"
      httpStatus: 404
      description: "当根据ID查找服务但未找到时抛出。"

# --- 测试策略 (Testing Strategy) ---
# 对应您文档中的 "Testing Strategy"
testingStrategy:
  - level: "Unit Test"
    scope: "测试各个组件的独立功能，如领域模型、应用服务。"
    tools: "JUnit 5, Mockito"
  - level: "Integration Test"
    scope: "测试组件间的交互，如仓储层与数据库的集成。"
    tools: "Testcontainers, Spring Boot Test"
  - level: "End-to-End Test"
    scope: "通过API测试完整的业务流程，如服务注册到发现。"
    tools: "RestAssured, Cypress"

# --- 安全考量 (Security Considerations) ---
# 对应您文档中的 "安全考虑"
securityConsiderations:
  - category: "Authentication"
    requirement: "所有API必须通过JWT进行认证。"
    implementation: "通过Spring Security过滤器链集成JWT校验逻辑。"
  - category: "Data Security"
    requirement: "用户密码等敏感数据禁止明文存储。"
    implementation: "采用Bcrypt对密码进行哈希加盐处理。"

# --- 扩展性与集成 (Scalability & Integrations) ---
# 对应您文档中的 "扩展性考虑" 和 "集成点"
scalability:
  - concern: "水平扩展"
    strategy: "API服务设计为无状态，便于通过增加实例进行水平扩展。"
integrationPoints:
  - system: "监控系统 (Prometheus)"
    purpose: "暴露/metrics端点，供Prometheus抓取核心性能指标。"
    contract: "遵循Micrometer的度量标准格式。"

# --- 实现计划 (Implementation Plan) ---
# 对应您文档中的 "实现计划"，提供一个高阶版本
implementationPlan:
  - phase: 1
    name: "核心基础与服务注册"
    deliverables: ["数据库表结构创建", "实现服务注册与管理组件及API"]
  - phase: 2
    name: "服务发现与能力管理"
    deliverables: ["实现服务发现组件与能力管理组件及API"]

# --- 风险与缓解措施 (Risks & Mitigations) ---
risks: # 主动识别潜在的技术风险
  - id: "RISK-001"
    description: string # 对风险的具体描述, e.g., "依赖的第三方短信服务SLA较低，可能影响注册流程可用性"
    probability: "High" | "Medium" | "Low" # 风险发生的可能性
    impact: "High" | "Medium" | "Low" # 发生后对项目的影响程度
    mitigation: string # 计划采取的缓解措施, e.g., "设计备用短信通道，实现服务降级和自动切换"
