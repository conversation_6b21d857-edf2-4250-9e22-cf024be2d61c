# --- 元数据 (Metadata) ---
id: string # 设计文档的唯一ID, 由系统生成, e.g., "DSGN-2025-001"
version: string # 当前设计文档的版本号, e.g., 1.0.0
status: "Draft" | "Reviewing" | "Approved" | "Implemented" # 设计文档的生命周期状态
author: "Architect Agent" # 标明此文档由架构师Agent生成
relatedRequirementId: string # 必须关联到一份已批准的需求ID, 确保设计的可追溯性, e.g., "REQ-2025-001"
creationTimestamp: datetime # 创建时间戳, e.g., "2023-10-27T10:00:00Z" (ISO 8601 format recommended)
lastUpdateTimestamp: datetime # 最后更新时间戳, e.g., "2023-10-27T10:00:00Z" (ISO 8601 format recommended)

# --- 概述 (Overview) ---
overview:
  purpose: string # 描述该系统或功能的核心目的和价值
  scope: string # 明确定义本次设计的边界和范围

# --- 架构设计 (Architecture) ---
architecture:
  pattern: "Microservices" | "Serverless" | "Layered Monolith" | "Event-Driven" | "Hexagonal" # 阐明本次设计遵循的核心架构范式，这是最高级别的技术决策
  layers: # 描述系统的逻辑分层，灵感来自您文档中的分层描述
    - name: "Interface Layer"
      responsibility: "定义API契约，处理外部请求和响应转换，实现RESTful API。"
    - name: "Application Layer"
      responsibility: "协调领域对象和服务，实现具体用例，如服务注册、发现和需求匹配。"
    - name: "Domain Layer"
      responsibility: "包含核心业务逻辑和领域模型，如Service, Capability等实体。"
    - name: "Infrastructure Layer"
      responsibility: "提供技术实现细节，如数据库访问、消息队列集成、外部服务调用。"
  diagrams: # 对应您文档中的 "系统架构图"，可嵌入Mermaid.js代码
    - name: "系统分层架构图"
      type: "Mermaid-Graph-TD"
      code: |
        graph TD
            Client[客户端] --> InterfaceLayer[接口层]
            InterfaceLayer --> ApplicationLayer[应用层]
            ApplicationLayer --> DomainLayer[领域层]
            ApplicationLayer --> InfrastructureLayer[基础设施层 (通过仓储接口)]
            DomainLayer -- 依赖 --> DomainLayer
            InfrastructureLayer --> ExternalSystems[(外部系统/数据库)]

# --- C4模型核心元素 (Core C4 Model Elements) ---
# C4模型有助于分层、清晰地展示软件架构。这里我们关注L2(容器)和L3(组件)。
containers: # “容器”指可独立部署和运行的单元，如一个服务、一个数据库、一个前端应用
  - name: string # 容器的唯一名称, e.g., "UserService"
    technology: string # 实现该容器的核心技术, e.g., "Go 1.21 / Gin"
    description: string # 精确描述该容器的职责, e.g., "负责用户管理、认证和授权"
    interfaces: # 定义该容器如何与外界交互
      - to: string # 交互的目标容器或外部系统, e.g., "AuthDatabase"
        protocol: "HTTPS/REST" | "gRPC" | "AMQP" | "Database Connection" # 交互所使用的协议
        description: string # 描述交互的目的, e.g., "读写用户信息"
components: # “组件”是容器内部的逻辑模块划分，代表了代码层面的组织结构
  - container: string # 该组件所属的容器名称, e.g., "UserService"
    name: string # 组件名称, e.g., "PasswordHasher"
    responsibility: string # 该组件的核心职责
    technology: string # 实现该组件的核心库或框架, e.g., "golang.org/x/crypto/bcrypt"
    description: string # 组件的单一职责描述, e.g., "处理用户密码的哈希和验证"
    # 将您文档中的Java接口定义，抽象为语言无关的接口契约描述
    interfaces:
      - name: string # e.g., "ServiceRegistryService"
        methods:
          - name: "registerService"
            params: [{name: "command", type: "ServiceRegistrationCommand"}]
            returnType: "ServiceDTO"
            description: "处理新服务的注册"

# --- 数据模型设计 (Data Model Design) ---
dataModel: # 描述此方案需要持久化的数据结构
  - name: string # 数据表的逻辑名称, e.g., "users"
    type: "PostgreSQL Table" | "MongoDB Collection" | "Redis Key" # 明确存储类型
    description: string # 描述此数据表的用途, e.g., "存储平台所有用户的核心信息"
    schema: # 详细的字段定义
      - field: string # 字段名, e.g., "email"
        dataType: string # 数据类型, e.g., "VARCHAR(254)"
        constraints: "UNIQUE, NOT NULL" # 约束条件, e.g., "主键、唯一、非空"
    indexes: # 关键索引策略，直接关系到查询性能
      - name: string # 索引名称, e.g., "idx_users_email"
        columns: [string] # 组成索引的列
        type: "BTREE" # 索引类型

# --- API端点定义 (API Endpoint Definition) ---
apiEndpoints: # 定义所有对外暴露的API接口
  - path: string # 接口路径, 遵循RESTful风格, e.g., "/api/v1/users/{userId}"
    method: "GET" | "POST" | "PUT" | "DELETE"
    description: string # 接口功能描述, e.g., "获取指定ID用户的信息"
    requestBody: object # 请求体的数据结构，建议使用JSON Schema格式进行精确描述
    responses: # 响应体的数据结构，同样建议使用JSON Schema
      "201": { description: "服务创建成功", content: { "application/json": { schema: { $ref: "#/components/schemas/ServiceDTO" }}}}
      "400": { description: "无效的请求数据" }

# --- 技术栈选型 (Technology Stack) ---
technologyStack: # 明确列出所有技术选型及其原因
  - category: "Language" | "Framework" | "Database" | "Cache" | "MessageQueue" | "Deployment" # 技术分类
    choice: string # 具体选择, e.g., "Go 1.21", "Kubernetes"
    reason: string # 做出此选择的关键理由，需关联NFR, e.g., "Go因其高并发性能和静态类型安全被选中，满足性能NFR"

# --- 错误处理 (Error Handling) ---
# 对应您文档中的 "Error Handling"
errorHandling:
  strategy: "采用统一的全局异常处理器，将特定异常映射为标准化的HTTP响应。"
  exceptions:
    - name: "ServiceNotFoundException"
      type: "Domain Exception"
      httpStatus: 404
      description: "当根据ID查找服务但未找到时抛出。"

# --- 测试策略 (Testing Strategy) ---
# 对应您文档中的 "Testing Strategy"
testingStrategy:
  - level: "Unit Test"
    scope: "测试各个组件的独立功能，如领域模型、应用服务。"
    tools: "JUnit 5, Mockito"
  - level: "Integration Test"
    scope: "测试组件间的交互，如仓储层与数据库的集成。"
    tools: "Testcontainers, Spring Boot Test"
  - level: "End-to-End Test"
    scope: "通过API测试完整的业务流程，如服务注册到发现。"
    tools: "RestAssured, Cypress"

# --- 安全考量 (Security Considerations) ---
# 对应您文档中的 "安全考虑"
securityConsiderations:
  - category: "Authentication"
    requirement: "所有API必须通过JWT进行认证。"
    implementation: "通过Spring Security过滤器链集成JWT校验逻辑。"
  - category: "Data Security"
    requirement: "用户密码等敏感数据禁止明文存储。"
    implementation: "采用Bcrypt对密码进行哈希加盐处理。"

# --- 扩展性与集成 (Scalability & Integrations) ---
# 对应您文档中的 "扩展性考虑" 和 "集成点"
scalability:
  - concern: "水平扩展"
    strategy: "API服务设计为无状态，便于通过增加实例进行水平扩展。"
integrationPoints:
  - system: "监控系统 (Prometheus)"
    purpose: "暴露/metrics端点，供Prometheus抓取核心性能指标。"
    contract: "遵循Micrometer的度量标准格式。"

# --- 实现计划 (Implementation Plan) ---
# 对应您文档中的 "实现计划"，提供一个高阶版本
implementationPlan:
  - phase: 1
    name: "核心基础与服务注册"
    deliverables: ["数据库表结构创建", "实现服务注册与管理组件及API"]
  - phase: 2
    name: "服务发现与能力管理"
    deliverables: ["实现服务发现组件与能力管理组件及API"]

# --- 风险与缓解措施 (Risks & Mitigations) ---
risks: # 主动识别潜在的技术风险
  - id: "RISK-001"
    description: string # 对风险的具体描述, e.g., "依赖的第三方短信服务SLA较低，可能影响注册流程可用性"
    probability: "High" | "Medium" | "Low" # 风险发生的可能性
    impact: "High" | "Medium" | "Low" # 发生后对项目的影响程度
    mitigation: string # 计划采取的缓解措施, e.g., "设计备用短信通道，实现服务降级和自动切换"
